from dotenv import load_dotenv
load_dotenv()

import sys,os

# add path to python path 
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from termcolor import colored

from app import create_app
from app.service_task import AsrsTask
from config import Config
from app.common.displayFormatter import formatString

sys.dont_write_bytecode = True

# remove flask default cli
cli = sys.modules['flask.cli']
cli.show_server_banner = lambda *x: None


if __name__ == "__main__":   
    app = create_app()

    flask_url = Config.get_flask_conn()
    flask_host = flask_url[0]
    flask_port = flask_url[1]

    
    # Start Up different service
    AsrsTask.run()

    # ASRS
    print(formatString('INFO',colored(f"Starting up Flask server at \tip: {flask_host} port: {flask_port}","light_green")))
    app.run(host=flask_host,port=flask_port, debug = False)


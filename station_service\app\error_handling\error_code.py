from typing import TypedDict

ERROR_VERSION = {
    1:{"GENERAL":
        {1: ('E-Stop Pressed',
            'Please release all E-Stop button. Press reset and start back the machine',
            1),
        2: ('Curtain Sensor Triggered',
            'Please remove any object detected by curtain sensor. Press reset and '
            'start back the machine.',
            1),
        3: ('Power Contactor Not On',
            'Machine down. Please call technician to check.',
            2),
        4: ('TC Server Connection Fail',
            'Make sure the PLC is connected to the internet. If the error persist, '
            'please call technician to check.',
            2),
        5: ('Item Bin NFC Serial Connection Fail',
            'Make sure the PLC is connected to the internet. If the error persist, '
            'please call technician to check.',
            2),
        6: ('Cross Zone NFC TCPIP Connection Fail',
            'Make sure the PLC is connected to the internet. If the error persist, '
            'please call technician to check.',
            2),
        7: ('Load Cell Serial Connection Fail',
            'Machine down. Please call technician to check.',
            2),
        8: ('Load Cell Read Value Error',
            'Machine down. Please call technician to check.',
            2),
        9: ('Load Cell Overweight Error',
            'Make sure the goods is not exceeding the allowable weight. ',
            1),
        10: ('Sliding Door Motor Alarm',
            'Machine down. Please call technician to check.',
            2),
        11: ('Sliding Door Fail to Open',
            'Machine down. Please call technician to check.',
            2),
        12: ('Sliding Door Fail to Close',
            'Machine down. Please call technician to check.',
            2),
        13: ('Door Signal Triggered',
            'Make sure the door is fully closed. If the error persist, please call '
            'technician to check.',
            1),
        14: ('TC Reply Timeout',
            'Make sure the PLC is connected to the internet. If the error persist, '
            'please call technician to check.',
            2),
        15: ('Machine Not Running',
            'Machine down. Please call technician to check.',
            2),
        20: ('Slider Motor Alarm',
            'Machine down. Please call technician to check.',
            2),
        21: ('Slider Motor Not Reach Home Position Timeout',
            'Machine down. Please call technician to check.',
            2),
        22: ('Slider Popup Motor Alarm',
            'Machine down. Please call technician to check.',
            2),
        23: ('Slider Popup Motor Failed to Retract',
            'Machine down. Please call technician to check.',
            2),
        24: ('Slider Popup Motor Failed to Extend',
            'Machine down. Please call technician to check.',
            2)},
        "ZONE":
        {1: ('Curtain Sensor Triggered',
            'Please remove any object detected by curtain sensor. Press reset and '
            'start back the machine.',
            1),
        2: ('Bin Jamming Error',
            'Please push the bin back to the original zone, reset and start back the '
            'machine',
            1),
        3: ('Power Roller Error', 'Machine down. Please call technician to check.', 2),
        4: ('UBT Power Roller Error',
            'Machine down. Please call technician to check.',
            2),
        5: ('UBT Motor Alarm', 'Machine down. Please call technician to check.', 2),
        6: ('UBT Failed to Retract',
            'Machine down. Please call technician to check.',
            2),
        7: ('UBT Failed to Extend',
            'Machine down. Please call technician to check.',
            2),
        8: ('Stopper Failed to Retract',
            'Machine down. Please call technician to check.',
            2),
        9: ('Stopper Failed to Extend',
            'Machine down. Please call technician to check.',
            2),
        10: ('Bin ID Read Fail', 'Machine down. Please call technician to check.', 2),
        11: ('Load Cell Read Fail',
            'Machine down. Please call technician to check.',
            2)}
    },
    
    2:{"GENERAL":
        {1: ('ESTOP PRESSED, RELEASE ESTOP BUTTON',
            'Please release all E-Stop button. Press reset and start back the machine',
            1),
        10: ('MV Z0 DEST ALARM, CHECK ACTUAL BIN / SENSOR',
            'Check Unattend Bin Present',
            1),
        11: ('MV Z1 DEST ALARM, CHECK ACTUAL BIN / SENSOR',
            'Check Unattend Bin Present',
            1),
        12: ('MV Z2 DEST ALARM, CHECK ACTUAL BIN / SENSOR',
            'Check Unattend Bin Present',
            1),
        13: ('MV Z0 SENSOR NOT CLEAR, CHECK ACTUAL BIN / SENSOR',
            'Check Unattend Bin Present',
            1),
        14: ('MV Z1 SENSOR NOT CLEAR, CHECK ACTUAL BIN / SENSOR',
            'Check Unattend Bin Present',
            1),
        15: ('MV Z2 SENSOR NOT CLEAR, CHECK ACTUAL BIN / SENSOR',
            'Check Unattend Bin Present',
            1),
        16: ('BUFFER BIN SENSOR NOT ON ALARM, CHECK ACTUAL BIN / SENSOR',
            'Check Bin Position, Move until sensor on press reset and start',
            1),
        17: ('WS BIN SENSOR NOT ON ALARM, CHECK ACTUAL BIN / SENSOR',
            'Check Bin Position, Move until sensor on press reset and start',
            1),
        18: ('DROP/PICK BIN SENSOR NOT ON ALARM, CHECK ACTUAL BIN / SENSOR',
            'Check Bin Position, Move until sensor on press reset and start',
            1),
        19: ('OFFSET SENSOR ON ALARM, CHECK ACTUAL BIN / SENSOR',
            'Check Bin Position, Move until sensor off press reset and start',
            1),
        20: ('WEIGHT NOT ZERO ALARM, CHECK WEIGHER', 'Check Weigher Connection', 1),
        21: ('OVER WEIGHT ALARM, REDUCE WEIGHT ON BIN',
            'Check Weight of the Bin, remove weight and press reset and start',
            1),
        22: ('DROP/PICK BIN SENSOR ON ALARM, CHECK ACTUAL BIN / SENSOR',
            'Check Bin Position, Move until sensor on press reset and start',
            1),
        23: ('WS BIN SENSOR ON ALARM, CHECK ACTUAL BIN / SENSOR',
            'Check Bin Position, Move until sensor on press reset and start',
            1),
        24: ('BUFFER BIN SENSOR ON ALARM, CHECK ACTUAL BIN / SENSOR',
            'Check Bin Position, Move until sensor on press reset and start',
            1),
        25: ('BARCODE NOT MATCH ALARM, CHECK BIN BARCODE',
            'Check Barcode Label or Barcode reader.',
            1),
        26: ('BARCODE READ TIMEOUT ALARM, CHECK BARCODE READER',
            'Check Barcode Label or Barcode reader.',
            1),
        27: ('CURTAIN SENSOR TRIGGERED, REMOVE HAND AND START',
            'Remove Hand from WS, Press reset and start',
            1),
        28: ('ITEM PROTRUDE, TIDY BIN AND START',
            'Remove Hand from WS, Press reset and start',
            1),
        29: ('WEIGHER NO RESPONSE, RESET & START / CHECK CONNECTION',
            'Check Weigher Connection',
            1)},
        "ZONE":
        {}
    }
}


class ErrorDict(TypedDict):
    error_code: int
    error_warning: str
    error_action: str
    error_level: int


def get_error_code(error_code:int,is_general=True)->ErrorDict|None:
    from config import Config

    try:
        error_type = ERROR_VERSION.get(Config.ERROR_CODE_VERSION)
        if not error_type:
            error_type = ERROR_VERSION.get(1)
            
        if is_general:
            error_list = error_type.get("GENERAL")
        else:
            error_list = error_type.get("ZONE")
            
        if not error_list:
            return None

        if not (error:= error_list.get(error_code)):
            return None
        
        return ErrorDict(error_code=error_code,error_warning=error[0],error_action=error[1],error_level=error[2])

    except Exception as e:
        print(e)
import requests

from requests import Response
from termcolor import colored

from config import Config
from .... import database as db,common as common,enum_collections as ec

class SMHttpClient:
    
    sm_host = Config.get_sm_host()

    # def inform_bridge_failure(cls,station_code, bin_code):
    #     try:
    #         send_data = common.JsonPayload.informBridgeFailurePayload(station_code,bin_code)
    #         response = requests.post(f'{cls.sm_host}/v1/app/hwx/recovery-bridge', send_data, timeout=2)
    #         data = response.json()
    #         return data
    #     except Exception as e:
    #         sm_http_send.error(f'inform_bridge_failure error data: {send_data}. Exception thrown: {e}')
    
    @classmethod
    def inform_bin_enroll(cls,station_code:int,storage_code:int)->int:
    
        from ....service_handler.cube_handler import enrollment_log

        try:
            
            url = f"{cls.sm_host}/v3/operations/enroll"
            payload=common.JsonPayload.informBinEnrollPayload(station_code,storage_code)
            
            enrollment_log.info(f'Sent - {url} : '+f'{payload}')
            print(common.formatString("INFO",colored(f'Sent - {url} to SM : ','blue')+f'Bin {storage_code} enroll in ST{station_code}'))

            if not Config.TC_DRY_RUN_MODE:
                response = cls.send_post_request(url,payload)
                data = response.json()
                enrollment_log.info(f'Received - {url} response from SM : {response.reason}: {data}')
                print(common.formatString("INFO",colored(f'Received - {url} response from SM : '+f'{response.status_code}, {response.reason}, {data}','blue')))
                
                if response.status_code == ec.HTTPStatus.OK.value:
                    to_index = int(data['data']['toIndex']) if data['data']['toIndex'] is not None else None
                    station = db.st_db_func.get_station_by_code(station_code)
                    db.st_mov_db_func.on_station_order(station_code,str(storage_code),None,station.worker if station.type != ec.CubeStationType.I.value else station.inner_drop,enroll=True,to_index=to_index)
                    # db.st_mov_db_func.complete_next_bin_job(station_code,str(storage_code),to_index)

                else:
                    """
                    {
                        "pagination": null,
                        "errors": [
                            {
                            "code": "storage_not_found",
                            "message": "storage not found."
                            }
                        ],
                        "data": null
                        }
                    """
                    enrollment_log.info(f'SM provide {response} {data} response.')
                    print(common.formatString("ERROR",f'SM provide {response} {data} response.'))

                return response.status_code
        except Exception as e:
            enrollment_log.error(f'{cls.inform_bin_enroll.__qualname__}() error with data: {payload}. Exception thrown: {e}')
            print(common.formatString("ERROR",f'{cls.inform_bin_enroll.__qualname__}() error data: {payload}. Exception thrown: {e}'))
            return None

    @classmethod
    def send_post_request(cls,url,payload)->Response:
        res =  requests.post(url,json=payload,timeout=2)
        return res
    
    @classmethod
    def inform_bin_enroll_v2(cls,station_code:int,storage_code:int):
        try:
            from ....service_handler.cube_handler import enrollment_log

            payload = common.JsonPayload.informBinEnrollPayloadv2(station_code,storage_code)
            
            enrollment_log.info(f'Sent - /v2/storages/enroll to SM : '+f'{payload}')
            print(common.formatString("INFO",colored(f'Sent - /v2/storages/enroll to SM : ','blue')+f'Bin {storage_code} enroll in ST{station_code}'))

            url = f"{cls.sm_host}/v2/storages/enroll"
            res =  requests.post(url,json=payload,timeout=2)
            
            enrollment_log.info(f'Received - /v2/storages/enroll response from SM : {res.json()}')
            print(common.formatString("INFO",colored(f'Received - /v2/storages/enroll response from SM : '+f'{res.status_code}','blue')))
            
            if res.status_code == 400:
                enrollment_log.info(f'SM provide {res} response, {storage_code} is already in sm db')
                print(common.formatString("ERROR",f'SM provide {res} response, {storage_code} is already in sm db'))
        except Exception as e:
            enrollment_log.error(f'{cls.inform_bin_enroll_v2.__qualname__}() error with data: {payload}. Exception thrown: {e}')
            print(common.formatString("ERROR",f'{cls.inform_bin_enroll_v2.__qualname__}() error data: {payload}. Exception thrown: {e}'))




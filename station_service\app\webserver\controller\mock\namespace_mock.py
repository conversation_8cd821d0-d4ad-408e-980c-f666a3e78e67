import json

from flask_restx.inputs import boolean
from flask_restx import Namespace,Resource, reqparse

from .... import common as common,database as db,enum_collections as ec,service_handler as handler
from ... import api_routing as api_routing,request_parsers as req_parser

from .... import api
from .... import database as db

nsmock = Namespace(api_routing.UrlFolder.mock,description="Mock API")
    
@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsmock.route(api_routing.UrlPath.mock_station_msg)
class MockStationMsg(Resource):
    @nsmock.doc(description = "To mock station message from station.")
    @api.expect(req_parser.mock_msg_body_parser)
    def post(self):

        from ....runtime import runtime
        
        data = req_parser.mock_msg_body_parser.parse_args(strict = False)

        id = data['station_code']
        msg = data['message']
        
        msg = msg.replace(";", "")
        
        msg_handler = handler.StationMessageHandler(msg,None,False)
        runtime.msg_received_queue.put(msg_handler)

        return common.StandardResponse.response()

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsmock.route(api_routing.UrlPath.send_station_msg)
class SendStationMsg(Resource):
    @nsmock.doc(description = "To mock send station messgae to station.")
    @api.expect(req_parser.mock_msg_body_parser)
    def post(self):

        data = req_parser.mock_msg_body_parser.parse_args(strict = False)

        station_code = int(data['station_code'])
        msg = data['message']

        if msg[-1] != ';':
            msg += ';'
        
        handler.send_tcp_msg(station_code, msg)

        return common.StandardResponse.response()


mock_ws_request_parser = reqparse.RequestParser()
mock_ws_request_parser.add_argument("event",required = True,type = str,location = "json")
mock_ws_request_parser.add_argument("entity",required = True,type = str,location = "json")
mock_ws_request_parser.add_argument("namespace",required = True,type = str,location = "json")
mock_ws_request_parser.add_argument("data",required = True,type = str,location = "json")

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsmock.route(api_routing.UrlPath.mock_ws_request)
class MockWSRequest(Resource):
    @nsmock.doc(description = "To mock ws request to socketIO client.")
    @api.expect(mock_ws_request_parser)
    def post(self):

        data = mock_ws_request_parser.parse_args(strict = False)

        event = data['event']
        entity = data['entity']     
        send_data = json.loads(data['data'])
    
        if not entity or entity == '' or entity == ' ':
            return common.StandardResponse.response(False,message="Entity is required")
        send_data = common.JsonPayload.jsonFormat(send_data,"")
        db.ws_request_db_func.add_ws_request(entity, send_data, event)        
            
        return common.StandardResponse.response()

mock_station_order_parser = reqparse.RequestParser()
mock_station_order_parser.add_argument('station_code',required = True,type = int,location = 'args')
mock_station_order_parser.add_argument('storage_code',required = True,type = str,location = 'args')
mock_station_order_parser.add_argument('request_by',required = True,type = str,default = "TC",location = 'args')
mock_station_order_parser.add_argument('drop_index',required = True,type = int,location = 'args')
mock_station_order_parser.add_argument('enroll',required = True,type = boolean, default = False,location = 'args')
mock_station_order_parser.add_argument('qc',required = True,type = boolean,default = False,location = 'args')
mock_station_order_parser.add_argument('bridge',required = True,type = boolean,default = False,location = 'args')
mock_station_order_parser.add_argument('order_id',required = True,type = int,location = 'args')

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsmock.route(api_routing.UrlPath.mock_station_order)
class MockStationOrder(Resource):
    @nsmock.doc(description = "To mock station order for HCC")
    @api.expect(mock_station_order_parser)
    def post(self):

        data = mock_station_order_parser.parse_args(strict=True)

        station_code = int(data['station_code'])
        storage_code = str(data['storage_code'])
        entity = data['request_by']
        
        station = db.st_db_func.get_station_by_code(station_code)        
        
        qc = data['qc']
        bridge = data['bridge']
        start_index = int(data['drop_index'])
        enroll = data['enroll']
        if enroll:
            tc_order_id = None
            is_unique = not db.st_order_db_func.check_enroll_order_exist(storage_code)
        else:
            tc_order_id = data['order_id']
            is_unique = not db.st_order_db_func.check_order_exist(tc_order_id)
          
        if is_unique:
            if not db.st_mov_db_func.on_station_order(station_code,storage_code,tc_order_id,start_index,qc=qc,bridge=bridge,enroll=enroll):
                return common.StandardResponse.response(False,message="Failed to create station order")

        else:
            return common.StandardResponse.response(False,"Duplicate order_id")
        
        return common.StandardResponse.response()
    

mock_inform_sm_bin_enroll = reqparse.RequestParser()
mock_inform_sm_bin_enroll.add_argument("order_id",required=True,type=int,location='args')

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsmock.route(api_routing.UrlPath.mock_inform_sm_enroll)
class MockInformSMEnroll(Resource):
    @nsmock.doc(description="To mock inform sm enroll bin when first attempt failed and job already created")
    @api.expect(mock_inform_sm_bin_enroll)
    def post(self):
        from ....blueprints.route.client import SMHttpClient

        data = mock_inform_sm_bin_enroll.parse_args()
        
        order_id = data.order_id

        order = db.st_order_db_func.find_order(dict(id=order_id,
                                                    status=ec.OrderStatus.PROCESSING.value,
                                                    is_processed=False))
        
        if order:
            res_code = SMHttpClient.inform_bin_enroll(order.station_code,order.storage_code,order.id)

            if res_code == ec.HTTPStatus.OK.value:
                return common.StandardResponse.response()
            else:
                return common.StandardResponse.response(False,message=f'SM rejected.')
        else:
            return common.StandardResponse.response(False,f'No processing enrolll order to send to SM')

mock_complete_next_job = reqparse.RequestParser()
mock_complete_next_job.add_argument('message',required=True,type=str,location='json')


@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsmock.route(api_routing.UrlPath.mock_complete_next_job)
class MockCompleteNextJob(Resource):
    @nsmock.doc(description='To complete next job of a bin in station.')
    @api.expect(mock_complete_next_job)
    def post(self):

        from ....service_handler.cube_handler import StationMessageHandler
        data = mock_complete_next_job.parse_args(strict=True)
        
        msg = data.message
        msg = msg.replace(';','')
        splited_msg = msg.split(',')
        if splited_msg[2] == ec.CubesProtocol.M.value:
            msg = msg.replace('M','J')
        elif splited_msg[2] == ec.CubesProtocol.B.value:
            splited_msg[2] = 'J' 
            station = db.st_db_func.get_station_by_code(int(splited_msg[1]))
            adj_station_code = station.adjacent
            if not adj_station_code is None:
                splited_msg[1] = str(adj_station_code)
            msg = ','.join(splited_msg)
        elif splited_msg[2] == ec.CubesProtocol.R.value:
            msg = msg.replace('R','A')
        elif splited_msg[2] == ec.CubesProtocol.CR.value:
            msg = msg.replace('CR','AC')
        elif splited_msg[2] == ec.CubesProtocol.U.value:
            msg = 'ACK,'+msg
        elif splited_msg[2] == ec.CubesProtocol.L.value:
            msg = 'ACK,'+msg
        else:
            return common.StandardResponse.response(False)
        StationMessageHandler(msg,None,False).process()
        return common.StandardResponse.response()

job_able_to_mock_parser = req_parser.code_parser.copy()

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsmock.route(api_routing.UrlPath.job_able_to_mock)
class JobAbleToMock(Resource):
    @nsmock.doc(description='To return all the message that able to mock by a station at the given specific time')
    @api.expect(job_able_to_mock_parser)
    def get(self):
        data = job_able_to_mock_parser.parse_args(strict=True)

        station_code = data.code

        data = []

        station = db.st_db_func.get_station_by_code(station_code)

        for position,msg in station.latest_drop_msg.items():
            if msg != '':
                splited_msg = msg.split('|')
                storage_code = splited_msg[-1]
                storage_code = storage_code.replace(';','')
                data.append({'storage_code' : storage_code,
                             'message' : msg})
        for position,msg in station.latest_pick_msg.items():
            if msg != '':
                splited_msg = msg.split('|')
                storage_code = splited_msg[-1]
                storage_code = storage_code.replace(';','')
                data.append({'storage_code' : storage_code,
                             'message' : msg})
        for position,msg in station.latest_cancel_drop_msg.items():
            if msg != '':
                splited_msg = msg.split('|')
                storage_code = splited_msg[-1]
                storage_code = storage_code.replace(';','')
                data.append({'storage_code' : storage_code,
                             'message' : msg})
        for position,msg in station.latest_cancel_pick_msg.items():
            if msg != '':
                splited_msg = msg.split('|')
                storage_code = splited_msg[-1]
                storage_code = storage_code.replace(';','')
                data.append({'storage_code' : storage_code,
                             'message' : msg})
        
        active_order_list = db.st_order_db_func.find_all_active_order(station_code)

        for active_order in active_order_list:
            job_to_run = db.st_order_db_func.find_next_job_to_run(station_code,active_order.storage_code)
            if job_to_run:
                if job_to_run.type in [ec.CubeStationJobType.MOVE_TO_PICK.value,
                                ec.CubeStationJobType.MOVE_TO_WORK.value,
                                ec.CubeStationJobType.MOVE_TO_JUNCTION.value,
                                ec.CubeStationJobType.TRANSIT.value]:
                    if active_order.type == ec.OrderType.BRIDGE.value and job_to_run.type == ec.CubeStationJobType.MOVE_TO_WORK.value:
                        msg = common.STMsgFormatter.bridge_msg(station_code,job_to_run.id,job_to_run.from_index,job_to_run.to_index,station.rotation,job_to_run.bin_no)
                    else:
                        msg = common.STMsgFormatter.move_msg(station_code,job_to_run.id,job_to_run.from_index,job_to_run.to_index,station.rotation,job_to_run.bin_no)
                    data.append({'storage_code':job_to_run.bin_no,
                                'message':msg})
                elif job_to_run.type == ec.CubeStationJobType.BRIDGE.value:
                    msg = common.STMsgFormatter.bridge_msg(station_code,job_to_run.id,job_to_run.from_index,job_to_run.to_index,station.rotation,job_to_run.bin_no)
                    data.append({'storage_code':job_to_run.bin_no,
                                'message':msg})
                elif job_to_run.type == ec.CubeStationJobType.LIGHT_UP.value:
                    msg = common.STMsgFormatter.light_up_msg(station_code,job_to_run.bin_no)
                    data.append({'storage_code':job_to_run.bin_no,
                                'message':msg})
                elif job_to_run.type == ec.CubeStationJobType.LIGHT_DOWN.value:
                    msg = common.STMsgFormatter.light_down_msg(station_code,job_to_run.bin_no)
                    data.append({'storage_code':job_to_run.bin_no,
                                'message':msg})


                continue

        return common.StandardResponse.response(True,data=data)
        
        

mock_emo_parser = reqparse.RequestParser()
mock_emo_parser.add_argument('message',required=True,type=str,location='json')
mock_emo_parser.add_argument('to_emo',required=True,type=boolean,default=True,location='json')
mock_emo_parser.add_argument('emo_id',required=True,type=int,default=1,location='json')

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsmock.route(api_routing.UrlPath.mock_emo)
class MockEmo(Resource):

    @nsmock.doc(description='To mock message to/from EMO')
    @api.expect(mock_emo_parser)
    def post(self):
        args = mock_emo_parser.parse_args(strict=True)
        msg = args['message']
        to_emo = args['to_emo']
        emo_id = args['emo_id']

        if to_emo:
            from ....service_handler.cube_handler import send_tcp_msg

            if not send_tcp_msg(emo_id,msg,target_module=ec.ModuleCode.EMO.value):
                return common.StandardResponse.response(False,message='Failed to send message to EMO')
        else:

            from ....service_handler.cube_handler import EMOMessageHandler

            if msg[-1] == ';':
                msg = msg[:-1]

            EMOMessageHandler(msg,None,True).process()


        return common.StandardResponse.response()

mock_sm_wa_parser = reqparse.RequestParser()
mock_sm_wa_parser.add_argument('station_code',required=True,type=int,default=11,location='json')
mock_sm_wa_parser.add_argument('storage_code',required=True,type=str,location='json')

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsmock.route(api_routing.UrlPath.mock_sm_work_arrival_callback)
class MockSMWorkArrivalCallbak(Resource):

    @nsmock.doc(description='To mock arrival callback from sm to create door open job')
    @api.expect(mock_sm_wa_parser)
    def post(self):
        args = mock_sm_wa_parser.parse_args()
        station_code=args['station_code']
        storage_code=args['storage_code']

        station = db.st_db_func.get_station_by_code(station_code)
        if station.type == ec.CubeStationType.LARGE.value and station.adjacent:
            order = db.st_order_db_func.find_active_order(station_code,storage_code)
            next_bin_job = db.st_mov_db_func.find_job(dict(type = ec.CubeStationJobType.NEXT_BIN.value,
                                                                  order_id = order.id))
            if next_bin_job: # Prevent unable to find job when sm 
                db.st_db_func.update_station(station_code,dict(bin_at_worker = storage_code))
                pred_job = db.st_mov_db_func.find_job(dict(id=next_bin_job.pred_id))
                if pred_job.type == ec.CubeStationJobType.BRIDGE.value: # only if is bridge job need to control open door 
                    db.st_mov_db_func.inject_door_open(next_bin_job,pred_job) #If it is already door open, ignore create

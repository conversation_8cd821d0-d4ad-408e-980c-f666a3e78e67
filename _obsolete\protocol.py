# import json
# import time
# import threading
# from . import server
# from simple_chalk import greenBright, redBright, blueBright, cyanBright

# lock = threading.Lock()


# # INBOUND receiving/labeling
# fakedata = '{ "station_type":"RECV", "station_id": 1 , "bin_id": "MY001-TB0001", "destination" : "L", "grn":"GRN001"}'
# data = json.loads(fakedata)
# status = "A"  # bin accept or deny
# case = "LABEL"


# def protocol(decodedMsg, c, addr):
#     splitedMsg = decodedMsg.split(",")

#     if ('P' in splitedMsg[2]):
#         if len(server.connection_list) != 0:
#             for conList in server.connection_list:
#                 if(conList[3] == threading.currentThread().getName()):
#                     conList[3] = str(splitedMsg[0]) + str(splitedMsg[1])
#         c.send((splitedMsg[0]+","+splitedMsg[1]+",<PERSON>,<PERSON>UCCESS;").encode())
#         if (splitedMsg[0] == "ST"):
#             lock.acquire()
#             server.receivedBroadcastQ.append([splitedMsg[1], decodedMsg])
#             lock.release()
#             time.sleep(0.1)
#             c.send((splitedMsg[0]+","+splitedMsg[1]+",S;").encode())

# # --------------------------- Station --------------------------------------------
#     elif ('S' in splitedMsg[2] or 'J' in splitedMsg[2]):
#         lock.acquire()
#         server.receivedBroadcastQ.append([splitedMsg[1], decodedMsg])
#         lock.release()

# # --------------------------- Inbound --------------------------------------------

#     elif ('R' in splitedMsg[2]):

#         server.sendMsg(splitedMsg[0]+splitedMsg[1], "ACK," + decodedMsg)

#         if (splitedMsg[0] == "QC"):
#             # Get Information from WMS
#             reply_to_return = "QC,1,N,A,MY001-TB0001|W|R1;"
#             server.sendMsg(splitedMsg[0]+splitedMsg[1], reply_to_return)
#         else:
#             # Get Information from WMS
#             reply_to_return = data['station_type'] + "," + str(data['station_id']) + "," + "N" + "," + status + "," + data['bin_id'] + "|" + data['grn'] + "|" + data['destination'] + ";"
#             server.sendMsg(splitedMsg[0]+splitedMsg[1], reply_to_return)

#     elif ('N,A,JD' in decodedMsg):
#         server.sendMsg(splitedMsg[0]+splitedMsg[1], "ACK," + decodedMsg)
#         # ACK WMS has complete
#     else:
#         lock.acquire()
#         server.receivedMsgQ.append([addr[0], addr[1], decodedMsg])
#         lock.release()

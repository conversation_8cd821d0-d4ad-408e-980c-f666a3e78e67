from typing import List, Dict
from flask_restx.inputs import boolean
from flask_restx import Namespace,Resource,reqparse, fields

from .... import api
from ... import api_routing as api_routing,request_parsers as req_parser
from .... import adapter as adp,common as common,enum_collections as ec,database as db

nsruntime = Namespace(api_routing.UrlFolder.runtime,description="Runtime API")


runtime_station_paser = reqparse.RequestParser()
runtime_station_paser.add_argument("code",required = False,type = int,location = 'args')

class _Parser:
    put_parser = nsruntime.model('RuntimeStation.put_parser',{
        'code': fields.Integer(),
        'update': fields.Nested(nsruntime.model('RuntimeStation.put_parser.update',{}))
    })

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsruntime.route(api_routing.UrlPath.station)
class RuntimeStation(Resource):
    @api.expect(runtime_station_paser)
    def get(self):
        '''
        To get the information of the runtime station
        '''
        from ....runtime import runtime
        
        args = runtime_station_paser.parse_args(strict=True)
            
        code = args['code']
        
        result_dict = {}

        if code:
            station = db.st_db_func.get_station_by_code(code)
            result_dict[code]  = station.as_dict()
        else:
            for code,station in runtime.runtime_st.items():
                result_dict[code] = station.as_dict()
        return common.StandardResponse.response(True,data = result_dict)

    @api.expect(_Parser.put_parser)
    def put(self):
        '''
        To update the information of the runtime station
        '''
        data = nsruntime.payload

        station_code = int(data['code'])
        update :dict = data['update']

        db.st_db_func.update_station(station_code,update)
        
        return common.StandardResponse.response()

class _HttpReqPutParser:
    put_parser = nsruntime.model('HttpRequest.put_parser',{
        'req_id': fields.Integer(),
        'update': fields.Nested(nsruntime.model('HttpRequest.put_parser.update',{}))
    })

http_post_delete_parser = reqparse.RequestParser()
http_post_delete_parser.add_argument("post_id",required=True, type = int,location='args')
@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsruntime.route(api_routing.UrlPath.http_post)
class HttpPost(Resource):

    @api.expect(_HttpReqPutParser.put_parser)
    def put(self):
        '''Update http request'''
        from ....http_router import HttpRequest

        data = nsruntime.payload

        req_id = int(data["req_id"])
        update = data["update"]

        req = HttpRequest.update_request(req_id,update)

        return common.StandardResponse.response(model=req)
    
    def post(self):
        '''
        Reinitialize the runtime http router
        '''
        from ....http_router import HttpRequest

        HttpRequest.reinitialization()
        return common.StandardResponse.response()
    
    @nsruntime.doc(description='To be used to delete a http post .')
    @api.expect(http_post_delete_parser)
    def delete(self):
        '''
        Delete http request 
        '''
        from ....http_router import HttpRequest
        data = http_post_delete_parser.parse_args(strict=True)

        post_id = data.post_id

        req = db.http_req_db_func.find_request(dict(id = post_id))

        if not req:
            return common.StandardResponse.response(False, f'Not able to find post request wiht id {post_id}')

        HttpRequest.event_loop.call_soon_threadsafe(
            HttpRequest.soft_delete_post_by_id, post_id
        )

        return common.StandardResponse.response()
    
socketio_req_delete_parser = reqparse.RequestParser()
socketio_req_delete_parser.add_argument("req_id",required=True, type = int,location='args')
@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsruntime.route(api_routing.UrlPath.sio_req)
class SioReq(Resource):

    @api.expect(_HttpReqPutParser.put_parser)
    def put(self):
        '''Update socketio request'''
        from ....communications_provider.socketio.cube import SioBroadcastHandler

        data = nsruntime.payload

        req_id = int(data["req_id"])
        update = data["update"]

        SioBroadcastHandler.update_request(req_id,update)

        return common.StandardResponse.response()
    
    def post(self):
        '''
        Reinitialize the runtime socket io broadcast ahndler
        '''
        from ....communications_provider.socketio.cube import SioBroadcastHandler

        SioBroadcastHandler.reinitialization()
        return common.StandardResponse.response()
    
    @nsruntime.doc(description='To be used to delete a socketio req .')
    @api.expect(socketio_req_delete_parser)
    def delete(self):
        '''
        Delete socketio request  
        '''
        from ....communications_provider.socketio.cube import SioBroadcastHandler
        data = socketio_req_delete_parser.parse_args(strict=True)

        req_id = data.req_id

        req = db.ws_request_db_func.get_request(req_id)

        if not req:
            return common.StandardResponse.response(False, f'Not able to find post request wiht id {req_id}')

        
        SioBroadcastHandler.event_loop.call_soon_threadsafe(
            SioBroadcastHandler.soft_delete_req_by_id, req_id
        )
        return common.StandardResponse.response()
    
add_st_event_parser = reqparse.RequestParser()
add_st_event_parser.add_argument("station_code",required = True,type = int,location = 'args')
add_st_event_parser.add_argument("storage_code", required = True, type = int,location = "args")
add_st_event_parser.add_argument("event_name", required = True, type = str, choices = list(ec.CubesIStationEvent._value2member_map_),location = "args")
add_st_event_parser.add_argument("is_processed", required = False, type = boolean,location = "args", default = False)
add_st_event_parser.add_argument("is_enroll", required = False, type = boolean,location = "args", default = False)
add_st_event_parser.add_argument("request_id", required = False, type = int,location = "args", default = None)
add_st_event_parser.add_argument("job_to_complete_event", required = False, type = str,location = "args", default = "[]")


class _Parser:
    put_parser = nsruntime.model('STEvent.put_parser',{
        'station_code': fields.Integer(),
        'event_id':fields.Integer(),
        'update': fields.Nested(nsruntime.model('STEvent.put_parser.update',{}))
    })

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsruntime.route(api_routing.UrlPath.st_event)
class STEvent(Resource):
    @api.expect(add_st_event_parser)
    def post(self):
        '''
        To add new station event for recovery
        '''
        data = add_st_event_parser.parse_args()

        station_code = int(data['station_code'])
        event_name = str(data['event_name'])
        storage_code = str(data['storage_code'])
        is_processed = data['is_processed']
        is_enroll = data['is_enroll']
        request_id = data['request_id']
        job_to_complete = data['job_to_complete_event']

        db.st_event_db_func.create_st_event(event_name,station_code,storage_code,is_enroll,request_id,is_processed,job_to_complete)

        return common.StandardResponse.response()

    @api.expect(_Parser.put_parser)
    def put(self):
        '''
        To update station event for recovery
        '''
        data = nsruntime.payload

        event_id = int(data['event_id'])
        station_code = int(data['station_code'])
        update = data['update']

        target_st_event = None
        
        st_event_queue = db.st_db_func.get_station_by_code(station_code).event_queue.queue()


        for event in st_event_queue:
            if event.id == event_id:
                target_st_event = event
                break

        if target_st_event is not None:
            db.st_event_db_func.update_st_event(target_st_event,update)
            return common.StandardResponse.response()

        else:
            return common.StandardResponse.response(False,"Could not find the event in event queue")

st_event_manual_parser = req_parser.station_code_parser.copy()
st_event_manual_parser.add_argument("event_id",required=True,type=int,location='args')
st_event_manual_parser.add_argument("status",required=True,type=str,choices=[ec.CubesIStationEvent.RECOVERY.value,ec.OrderStatus.CRITICAL_ERROR.value,ec.OrderStatus.CANCELED.value],location='args')
@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsruntime.route(api_routing.UrlPath.st_event_manual_flag)
class STEventManualFlag(Resource):
    @api.expect(st_event_manual_parser)
    def patch(self):
        '''
        To reset the error station event and rerun it
        '''
        data = st_event_manual_parser.parse_args(strict=True)

        station_code = data.station_code
        event_id = data.event_id
        status = data.status

        target_st_event = None

        st_event_queue : List[adp.EventAdapter] = db.st_db_func.get_station_by_code(station_code).event_queue.queue()

        for event in st_event_queue:
            if event.id == event_id:
                target_st_event = event
                break

        if target_st_event is None:
            return common.StandardResponse.response(False,"Could not find the event in event queue")
        
        if status == ec.CubesIStationEvent.RECOVERY.value:
            job_generated = target_st_event.job_to_complete_event
            for job_id in job_generated:
                db.st_mov_db_func.delete_job(job_id)
            db.st_event_db_func.recover_st_event(target_st_event)
        elif status == ec.OrderStatus.CRITICAL_ERROR.value:
            target_st_event.trigger_error()
        elif status == ec.OrderStatus.CANCELED.value:
            target_st_event.cancel_event()
        else:
            pass

        return common.StandardResponse.response()


st_event_queue_parser = reqparse.RequestParser()
st_event_queue_parser.add_argument("station_code",required = True,type = int,location = 'args')

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsruntime.route(api_routing.UrlPath.st_event_queue)
class STEventQueue(Resource):
    @api.expect(st_event_queue_parser)
    def get(self):
        '''
        To get the runtime event queue of a station
        '''
        from ....adapter import EventAdapter

        data = st_event_queue_parser.parse_args()

        station_code = int(data['station_code'])

        result:List[Dict] = list()

        event_queue = db.st_db_func.get_station_event_queue(station_code)

        for evt in event_queue.queue():
            event_dict = dict()
            evt:EventAdapter
            event_dict['is_paused']= event_queue.is_paused
            event_dict['event_id'] = evt.id
            event_dict['event_name'] = evt.event_name
            event_dict['storage_code'] = evt.storage_code
            event_dict['status'] = evt.status
            event_dict['is_processed'] = evt.is_processed
            event_dict['err_msg'] = evt.err_msg

            result.append(event_dict)

        return common.StandardResponse.response(data=result)
    
    @api.expect(st_event_queue_parser)
    def post(self):
        """Reinitialize runtime station event queue follow db, only add AVAILABLE station event only, order by created_at"""
        from ....adapter import EventQueue

        data = st_event_queue_parser.parse_args()
    
        station_code = int(data['station_code'])

        station = db.st_db_func.get_station_by_code(station_code)

        station.event_queue = EventQueue()
        event_list = db.st_event_db_func.get_all_active_event(station.code)

        for event in event_list:
            adp.EventAdapter(event)

        return common.StandardResponse.response()




@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsruntime.route(api_routing.UrlPath.rearrnge_event_queue)
class RearrangeSTEventQueue(Resource):

    @api.expect(st_event_queue_parser)
    def patch(self):
        '''
        To move the first event to the end of the queue
        '''
        data = st_event_queue_parser.parse_args()

        station_code = int(data['station_code'])

        event_queue = db.st_db_func.get_station_event_queue(station_code)

        curr_event = event_queue.next_event()
        event_queue.add_event(curr_event)

        return common.StandardResponse.response()


pause_st_event_queue_parser = reqparse.RequestParser()
pause_st_event_queue_parser.add_argument("station_code",required = True,type = int,location = 'args')
pause_st_event_queue_parser.add_argument("is_pause",required = True,type = boolean,choices=(True,False))

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsruntime.route(api_routing.UrlPath.pause_st_event_queue)
class PauseSTEventQueue(Resource):
    @api.expect(pause_st_event_queue_parser)
    def patch(self):
        """Pause / Unpause the execution of a station event queue"""
        data = pause_st_event_queue_parser.parse_args()

        station_code = int(data['station_code'])
        is_pause = data['is_pause']

        station_event_queue = db.st_db_func.get_station_event_queue(station_code)

        if station_event_queue:
            if is_pause:
                station_event_queue.pause()
            else:
                station_event_queue.unpause()

        return common.StandardResponse.response()
        

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsruntime.route(api_routing.UrlPath.rabbit_mq)
class RabbitMQ(Resource):    
    def post(self):

        from ....common import BackgroundTask

        BackgroundTask.reset_connection()
        return common.StandardResponse.response()
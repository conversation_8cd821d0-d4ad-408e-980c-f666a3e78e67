# import eventlet

# from os import name
# from datetime import datetime
# from simple_chalk import redBright, yellowBright, magentaBright

# import app.database as db
# import app.common as common
# import app.server as maint_dock_server
# import app.enum_collections as enum_collection

# from app import Session, app, flasksocketio as socketio

# md_handler_log = common.get_logger_console('md_handler')

# def process_md_orders(order, jobs):
#     try:
#         order.status = enum_collection.OrderStatus.PROCESSING.value
#         # db.session.commit()
#         first = True
#         dock_id = order.maintanence_dock_id

#         while order.status != enum_collection.OrderStatus.COMPLETED.value:

#             is_completed = db.check_all_md_job_completed(order.id)

#             if first is False and is_completed is False:
#                 eventlet.sleep(5)

#             jobs = db.db.get_md_jobs(order.id, True)
#             for job in jobs:
#                 job = db.get_md_job(job)
#                 job_id = job.id
#                 if db.check_pred_md_job_completed(job):

#                     if job.job_status == enum_collection.OrderStatus.AVAILABLE.value:
#                         db.processing_md_job(job.id)

#                     job = db.get_md_job(job_id)

#                     if job.job_type == enum_collection.MdJobType.REQUEST.value:
#                         msg_to_send = f'MD,{dock_id},RE;'
#                         maint_dock_server.send_md_msg(f'{enum_collection.ModuleCode.MAINTANENCE_DOCK.value}{dock_id}', msg_to_send)
#             first = False
#     except Exception as e:
#         md_handler_log.error(redBright(f'process_md_orders error. Exception thrown:{e}'))

# def md_job_processor():
#     try:
#         while True:
#             try:
#                 eventlet.sleep(3)
#                 jobs = db.get_available_md_jobs()
#                 for job in jobs:
#                     if job.ack is False:
#                         if job.job_type == enum_collection.MdJobType.DOOR_CLOSE.value:
#                             msg_to_send = f'MD,{job.station_id},CD,{job.id};'
#                         elif job.job_type == enum_collection.MdJobType.DOOR_OPEN.value:
#                             msg_to_send = f'MD,{job.station_id},OD,{job.id};'
#                         elif job.job_type == enum_collection.MdJobType.REQUEST.value:
#                             msg_to_send = f'MD,{job.station_id},RE,{job.id};'
#                         maint_dock_server.send_md_msg(f'{enum_collection.ModuleCode.MAINTANENCE_DOCK.value}{job.station_id}',msg_to_send)
#             except Exception as e:
#                  md_handler_log.error(redBright(f'md_job_processor error. Exception thrown: {e}'))
                
#     except Exception as e:
#         md_handler_log.error(redBright(f'md_job_processor error. Exception thrown: {e}'))


# def md_loop():
#     try:
#         while True:
#             try:
#                 eventlet.sleep(1)
#                 if len(maint_dock_server.md_msg_queue.queue) > 0:
#                     reply = maint_dock_server.md_msg_queue.get()
#                     msg = reply[1].replace(';', '')
#                     splited_msg = msg.split(',')

#                     if splited_msg[2] in ['A', 'D']:
#                         station_id = int(splited_msg[1])
#                         job_id = int(splited_msg[3])
#                         md_station = db.get_md_station(station_id)
#                         entity = f'{enum_collection.ExternalEntities.TC.value}_{md_station.zone}'
#                         job = db.get_md_job(job_id)
#                         if job != None:
#                             if splited_msg[2] == 'A':
#                                 if db.get_md_station_processing(station_id) is False:      
#                                     md_handler_log.info(f'Maintanence dock {station_id} has accepted skycar.')
#                                     db.station_processing_skycar(md_station.station_id)
#                                     data = common.JsonPayload.jsonFormat({
#                                         'job_id': job.order_id,
#                                         'station_id': station_id,
#                                         'skycar_id': job.skycar_id,
#                                         'requested_by': job.requested_by,
#                                         'message': enum_collection.MdMessages.APPROVED.value,
#                                         'type': job.action
#                                     })
#                                 else:
#                                     md_handler_log.warning(yellowBright(f'Maintanence dock {station_id} is already processing a skycar. Reject TC for now.'))
#                                     data = common.JsonPayload.jsonFormat({
#                                         'job_id': job.order_id,
#                                         'station_id': station_id,
#                                         'skycar_id': job.skycar_id,
#                                         'requested_by': job.requested_by,
#                                         'message': enum_collection.MdMessages.HWX_REJECT.value,
#                                         'type': job.action
#                                     })
#                                 db.add_md_ws_request(entity,data,'sensor-approval',job.id,namespace='station')
#                                 # socketio.emit('sensor-approval', data, broadcast=True, namespace='/station')
#                                 # print(yellowBright('Broadcasting sensor-approval to TC: ') + f'{data}')

#                             elif splited_msg[2] == 'D':
#                                 md_handler_log.warning(yellowBright(f'Maintanence dock {station_id} sensor has rejected Skycar. Rejecting TC.'))
#                                 data = common.JsonPayload.jsonFormat({
#                                     'job_id': job.order_id,
#                                     'station_id': station_id,
#                                     'skycar_id': job.skycar_id,
#                                     'requested_by': job.requested_by,
#                                     'message': enum_collection.MdMessages.PLC_REJECT.value,
#                                     'type': job.action
#                                 })
#                                 # print(yellowBright('Broadcasting sensor-approval to TC: ') + f'{data}')
#                                 db.add_md_ws_request(entity,data,'sensor-approval',job.id,namespace='station')
#                                 # socketio.emit('sensor-approval', data, broadcast=True, namespace='/station')
#                             db.complete_md_job(job.order_id)

#                     if splited_msg[2] == 'J':
#                         station_id = int(splited_msg[1])
#                         md_station = db.get_md_station(station_id)
#                         entity = f'{enum_collection.ExternalEntities.TC.value}_{md_station.zone}'
#                         job_id = int(splited_msg[3])
#                         job = db.get_md_job(job_id)
#                         type = None
#                         if job.job_type == enum_collection.MdJobType.DOOR_OPEN.value:
#                             type = enum_collection.MdActionTypes.OPEN.value
#                         elif job.job_type == enum_collection.MdJobType.DOOR_CLOSE.value:
#                             type = enum_collection.MdActionTypes.CLOSE.value
#                         data = common.JsonPayload.jsonFormat({
#                             'job_id': job.order_id,
#                             'station_id': job.station_id,
#                             'skycar_id': job.skycar_id,
#                             'requested_by': job.requested_by,
#                             'message': enum_collection.MdMessages.APPROVED.value,
#                             'type': type
#                         })
#                         db.add_md_ws_request(entity,data,'door-approval',job.id,namespace='station')
#                         db.complete_md_job(job.order_id)

#                     # Handle MD ACK Messages
#                     if splited_msg[3] in ['OD', 'CD']:
#                         order_id = int(splited_msg[4])
#                         db.ack_md_job(order_id)
                        
#             except Exception as e:
#                 md_handler_log.error(redBright(f'md_loop error. Exception thrown:{e}'))           

#     except Exception as e:
#         md_handler_log.error(redBright(f'md_loop error. Exception thrown:{e}'))

# def md_ws_processor():
#     try:
#         import app.communications_provider.socketio as sio
        
#         requests_session = Session()
        
#         while True:
#             try:
#                 eventlet.sleep(3)
#                 requests = db.get_md_requests()

#                 for request in requests:
#                     json_data = request.data
#                     if 'data' in json_data.keys():
#                         json_data['data']['request_id'] = request.id
#                     else:
#                         json_data['request_id'] = request.id
#                     if request.entity is None:
#                         if request.namespace is None:
#                             socketio.emit(request.event, json_data, broadcast=True)
#                         else:
#                             socketio.emit(request.event, json_data, broadcast=True, namespace=f'/{request.namespace}')
#                         sio.md_ws_log.info(magentaBright(f'Sent | {request.event} - ')+f'{json_data}')
#                     else:
#                         client = sio.md_ws_clients[request.entity]

#                         if client != None:
#                             with app.test_request_context():
#                                 if request.namespace is None:
#                                     socketio.emit(request.event, json_data, room=client, callback=sio.ack_md_ws)
#                                 else:
#                                     socketio.emit(request.event, json_data, room=client, namespace=f'/{request.namespace}', callback=sio.ack_md_ws)
#                                 sio.md_ws_log.info(magentaBright(f'Sent | {request.event} to {request.entity} - ')+f'{json_data}')

                                
#                         else:
#                             md_handler_log.warning(yellowBright(f'request_executor() unable to find websocket client: {request.entity}'))
#                     if request.retry == 0:
#                         request.status = enum_collection.OrderStatus.PROCESSING.value

#                     request.retry = request.retry + 1
#                     request.updated_at = datetime.now()
#                     requests_session.commit()
#             except Exception as e:
#                  md_handler_log.error(redBright(f'md_ws_processor error. Exception thrown: {e}'))
#     except Exception as e:
#         md_handler_log.error(redBright(f'md_ws_processor error. Exception thrown: {e}'))
# import eventlet

# from flask import request
# from simple_chalk import redBright, greenBright, yellowBright,magentaBright

# import app.database as db
# import app.common as common
# import app.enum_collections as enum_collection

# from app import flasksocketio as socketio

# md_ws_log = common.get_logger_console('md_wsroute')

# md_ws_clients = {   enum_collection.TcSubEntities.TC_A.value: None,
#                     enum_collection.TcSubEntities.TC_B.value: None,
#                     enum_collection.TcSubEntities.TC_C.value: None,}

# def ack_md_ws(data = None):
#     try:
#         if data != None:
#             id = int(data['request_id'])
#             md_ws_log.info(greenBright(f'Received | WEBSOCKET CALLBACK - request id: {id}'))
#             db.complete_md_request(id)
#     except Exception as e:
#         print(redBright(f'ack_md_ws error. Exception thrown: {e}'))

# if socketio:
#     @socketio.on('tc-init', namespace ='/station')
#     def md_init(data):
#         try:
#             global md_ws_clients
#             md_ws_log.info(magentaBright(f'Received | tc-init - ')+f'{data}')
#             zone = data['data']['zone'].upper()
#             print(greenBright(f'TC {zone} has init into MD'))

#             active_mds = db.get_all_active_md()
            
#             sub_entity = f'{enum_collection.ExternalEntities.TC.value}_{zone}'
            

#             if sub_entity in [enum_collection.TcSubEntities.TC_A.value, enum_collection.TcSubEntities.TC_B.value, enum_collection.TcSubEntities.TC_C.value]:
#                 db.clear_md_status_requests(zone)
#                 md_ws_clients[sub_entity] = request.sid
#                 eventlet.sleep(3)
#                 json_reply = {
#                     'active_stations': active_mds
#                 }
#                 md_ws_log.info(magentaBright(f'Sent | tc-init CALLBACK to TC : ')+f'Init status {json_reply}.')
#                 return json_reply
#             else:
#                 md_ws_log.warning(yellowBright(f'WARNING: Tc Init sub_entity {sub_entity} is invalid. Ignoring.'))

#         except Exception as e:
#             md_ws_log.error(redBright(f'md_init error. Exception thrown: {e}'))

#     @socketio.on('sensor-request', namespace='/station')
#     def md_order(data):
#         try:
#             md_ws_log.info(magentaBright(f'Received | sensor-request - ')+f'{data}')
#             json_data = data['data']
#             job_id = json_data['job_id']
#             station_id = int(json_data['station_id'])
#             action = json_data['type']
#             if json_data['skycar_id'] != None:
#                 skycar_id = int(json_data['skycar_id'])
#             else:
#                 skycar_id = None
#             # order = create_md_order(json_data)

#             # if order is None:
#             #     print(redBright(f'Md order already exists for order {job_id}. Ignoring this one.'))
#             #     md_ws_log.warn(f'Md order already exists for order {job_id}. Ignoring this one.')
#             # else:
#             #     jobs = generate_md_jobs(order)
#             #     socketio.start_background_task(target=process_md_orders(order, jobs))

#             job = db.create_md_job(job_id, station_id, skycar_id, json_data['request_by'], action)
#             # msg_to_send = f'MD,{station_id},RE,{job.id};'
#             # send_md_msg(f'{ModuleCode.MAINTANENCE_DOCK.value}{station_id}', msg_to_send)

#         except Exception as e:
#             md_ws_log.error(redBright(f'md_order error. Exception thrown: {e}'))


#     @socketio.on('door-request', namespace='/station')
#     def door_request(data):
#         try:
#             md_ws_log.info(magentaBright(f'Received | door_request - ')+f'{data}')
#             json_data = data['data']
#             station_id = int(json_data['station_id'])
#             action = json_data['type']
#             job_id = json_data['job_id']

#             if json_data['skycar_id'] != None:
#                 skycar_id = int(json_data['skycar_id'])
#             else:
#                 skycar_id = None

#             if action == enum_collection.MdActionTypes.OPEN.value:
#                 action_code = 'OD'
#             elif action == enum_collection.MdActionTypes.CLOSE.value:
#                 action_code = 'CD'

#             job = db.create_md_job(job_id, station_id, skycar_id, json_data['request_by'], action)
#             # msg_to_send = f'MD,{station_id},{action_code},{job.id};'
#             # send_md_msg(f'{ModuleCode.MAINTANENCE_DOCK.value}{station_id}', msg_to_send)
#         except Exception as e:
#             md_ws_log.error(redBright(f'door_request error. Exception thrown:{e}'))
        
#     @socketio.on('md-order-complete', namespace='/station')
#     def md_order_complete(data):
#         try:
#             md_ws_log.info(magentaBright(f'Received | md_order_complete - ')+f'{data}')
#             json_data = data['data']
#             station_id = int(json_data['station_id'])
#             order_id = json_data['job_id']

#             db.complete_md_job(order_id)
#             db.clear_md_station_processing(station_id)

#             # Return call back to TC
#             return data
        
#         except Exception as e:
#             md_ws_log.error(redBright(f'md_order_complete error. Exception thrown: {e}'))
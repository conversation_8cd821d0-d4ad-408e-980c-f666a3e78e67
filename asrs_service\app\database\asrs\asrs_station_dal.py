from app import app,db
from ...models import AsrsStations
from ... import common as common

db_log = common.get_logger_console('asrs_db_error')

@common.decorateAllFunctionInClass(common.db_log_and_suppress_error(app,db_log))
class AsrsStationSQLAlchemyQueries:
    
    def get_station_by_id(self,id:int):
        curr_session = db.session
        station = curr_session.query(AsrsStations).filter_by(station_id=id).first()
        return station

# from simple_chalk import redBright
# from datetime import datetime as dt

# import app.models as model
# import app.common as common
# import app.models.classes as model_class
# import app.enum_collections as enum_collection

# from app import app, Session

# md_db_log = common.get_logger_console('md_db_error')


# # --------------------- MD Jobs --------------------------
# def get_available_md_jobs():
#     try:
#         curr_session = Session()
#         curr_session.commit()
#         jobs = curr_session.query(model.MdJob).filter(model.MdJob.job_status != enum_collection.OrderStatus.COMPLETED.value).all()
#         return jobs
#     except Exception as e:
#         md_db_log.error(redBright(f'get_available_md_jobs error. Exception thrown: {e}'))

# def create_md_job(job_id, station_id, skycar_id, requested_by, action):
#     try:
#         curr_session = Session()
#         job = curr_session.query(model.MdJob).filter_by(order_id=job_id).first()
#         if action == enum_collection.MdActionTypes.CLOSE.value:
#             job_type = enum_collection.MdJobType.DOOR_CLOSE.value
#         elif action == enum_collection.MdActionTypes.OPEN.value:
#             job_type = enum_collection.MdJobType.DOOR_OPEN.value
#         elif action == enum_collection.MdActionTypes.IN.value or action == enum_collection.MdActionTypes.OUT.value:
#             job_type = enum_collection.MdJobType.REQUEST.value
#         if job is None or job_id is None:
#             new_job = model.MdJob(
#                 order_id=job_id,
#                 job_type=job_type,
#                 job_status=enum_collection.OrderStatus.AVAILABLE.value,
#                 station_id=station_id,
#                 skycar_id=skycar_id,
#                 requested_by=requested_by,
#                 action=action,
#                 created_at=dt.now(),
#                 updated_at=dt.now()
#             )
#             curr_session.add(new_job)
#             curr_session.commit()
#             return new_job
#     except Exception as e:
#         md_db_log.error(redBright(f'create_request_job error. Exception thrown:{e}'))


# def generate_md_jobs(order):
#     try:
#         curr_session = Session()
#         order_id = order.id
#         jobs = []
#         request_job = model.MdJob(
#             order_id=order_id,
#             job_type=enum_collection.MdJobType.REQUEST.value,
#             job_status=enum_collection.OrderStatus.AVAILABLE.value,
#             created_at=dt.now(),
#             updated_at=dt.now()
#         )
#         jobs.append(request_job)
#         curr_session.add(request_job)
#         curr_session.commit()
#         open_door_job = model.MdJob(
#             order_id=order_id,
#             job_type=enum_collection.MdJobType.DOOR_OPEN.value,
#             job_status=enum_collection.OrderStatus.AVAILABLE.value,
#             pred_id=request_job.id,
#             created_at=dt.now(),
#             updated_at=dt.now()
#         )
#         jobs.append(open_door_job)
#         curr_session.add(open_door_job)
#         curr_session.commit()
#         close_door_job = model.MdJob(
#             order_id=order_id,
#             job_type=enum_collection.MdJobType.DOOR_CLOSE.value,
#             job_status=enum_collection.OrderStatus.AVAILABLE.value,
#             pred_id=open_door_job.id,
#             created_at=dt.now(),
#             updated_at=dt.now()
#         )
#         jobs.append(close_door_job)
#         curr_session.add(close_door_job)
#         curr_session.commit()
#         if order.type == enum_collection.MdActionTypes.IN.value:
#             inform_tc_job = model.MdJob(
#                 order_id=order_id,
#                 job_type=enum_collection.MdJobType.INFORM_TC.value,
#                 job_status=enum_collection.OrderStatus.AVAILABLE.value,
#                 pred_id=close_door_job.id,
#                 created_at=dt.now(),
#                 updated_at=dt.now()
#             )
#         jobs.append(inform_tc_job)
#         curr_session.add(inform_tc_job)
#         curr_session.commit()
#     except Exception as e:
#         md_db_log.error(redBright(f'generate_md_jobs error. Exception thrown: {e}'))


# def check_all_md_job_completed(order_id):
#     try:
#         curr_session = Session()
#         jobs = curr_session.query(model.MdJob).filter_by(order_id=order_id).all()
#         for job in jobs:
#             if job.job_status != enum_collection.OrderStatus.COMPLETED.value:
#                 return False
#         return True
#     except Exception as e:
#         md_db_log.error(redBright(f'check_all_md_job_completed error. Exception thrown:{e}'))


# def get_md_jobs(order_id, id_only=False):
#     try:
#         curr_session = Session()
#         jobs = curr_session.query(model.MdJob).filter_by(order_id=order_id).all()
#         if id_only:
#             job_ids = []
#             for job in jobs:
#                 job_ids.append(job.id)
#             return job_ids
#         return jobs
#     except Exception as e:
#         md_db_log.error(redBright(f'get_md_jobs error. Exception thrown:{e}'))


# def check_pred_md_job_completed(job):
#     try:
#         curr_session = Session()
#         pred_id = job.pred_id
#         if pred_id == None:
#             return True
#         else:
#             pred_job = curr_session.query(model.MdJob).filter_by(id=pred_id).first()
#             if pred_job.job_status == enum_collection.OrderStatus.COMPLETED.value:
#                 return True
#             else:
#                 return False
#     except Exception as e:
#         md_db_log.error(redBright(f'check_pred_md_job_completed error. Exception thrown:{e}'))


# def processing_md_job(job_id):
#     try:
#         curr_session = Session()
#         with app.app_context():
#             job_id = int(job_id)
#             job = curr_session.query(model.MdJob).filter_by(id=job_id).first()
#             job.job_status = enum_collection.OrderStatus.PROCESSING.value
#             job.updated_at = dt.now()
#             curr_session.commit()
#     except Exception as e:
#         md_db_log.error(redBright(f'processing_md_job error. Exception thrown:{e}'))


# def get_md_job(id):
#     try:
#         curr_session = Session()
#         md_job = curr_session.query(model.MdJob).filter_by(id=id).first()
#         wrapped_md_order = model_class.MdJobWrapper(md_job)
#         return wrapped_md_order
#     except Exception as e:
#         md_db_log.error(redBright(f'get_md_job error. Exception thrown:{e}'))



# def ack_md_job(job_id):
#     try:
#         curr_session = Session()
#         job = curr_session.query(model.MdJob).filter_by(id = job_id).first()
#         job.ack = True
#         job.updated_at = dt.now()
#         curr_session.commit()
#     except Exception as e:
#         md_db_log.error(redBright(f'ack_md_job error. Exception thrown: {e}'))

# def complete_md_job(order_id):
#     try:
#         curr_session = Session()
#         jobs = curr_session.query(model.MdJob).filter_by(order_id = order_id).all()
#         for job in jobs:
#             job.job_status = enum_collection.OrderStatus.COMPLETED.value
#             job.updated_at = dt.now()
#         curr_session.commit()
#     except Exception as e:
#         md_db_log.error(redBright(f'complete_md_job error. Exception thrown: {e}'))

# # -------------------- MD Orders --------------------


# def create_md_order(data):
#     try:
#         curr_session = Session()
#         order_id = data['job_id']
#         order = find_md_order(order_id)
#         if order is None:
#             order = model.MdOrder(
#                 ex_order_id=int(data['job_id']),
#                 skycar_id=int(data['skycar_id']),
#                 maintanence_dock_id=int(data['station_id']),
#                 type=data['type'],
#                 status=enum_collection.OrderStatus.AVAILABLE.value,
#                 created_at=dt.now()
#             )
#             curr_session.add(order)
#             curr_session.commit()
#             return order
#         else:
#             return None
#     except Exception as e:
#         md_db_log.error(redBright(f'create_md_order error. Exception thrown:{e}'))


# def find_md_order(id):
#     try:
#         curr_session = Session()
#         md_order = curr_session.query(model.MdOrder).filter_by(ex_order_id=id).first()
#         return md_order
#     except Exception as e:
#         md_db_log.error(redBright(f'find_md_order error. Exception thrown: {e}'))

# # -------------------- MD Stations --------------------
# def get_all_active_md():
#     try:
#         station_list= []
#         curr_session = Session()
#         station_ids = curr_session.query(model.MdStations.station_id).filter_by(pairing=True).all()
#         for station in station_ids:
#             station_list.append(station.station_id)
#         return station_list
#     except Exception as e:
#         md_db_log.error(redBright(f'get_all_active_md error. Exception thrown: {e}'))
    
# def set_md_pairing(id,status):
#     try:
#         curr_session = Session()
#         md = curr_session.query(model.MdStations).filter_by(station_id = id).one()
#         md.pairing = status
#         curr_session.commit()
#     except Exception as e:
#         md_db_log.error(redBright(f'set_md_pairing error. Exception thrown: {e}'))

# def clear_md_status():
#     try:
#         curr_session = Session()
#         stations = curr_session.query(model.MdStations).all()
#         for station in stations:
#             if station.pairing is True:
#                 station.pairing = False
#         curr_session.commit()
#     except Exception as e:
#         md_db_log.error(redBright(f'clear_md_status error. Exception thrown: {e}'))

# def get_md_station_processing(id:int)-> bool :
#     try :
#         curr_session = Session()

#         md_station = curr_session.query(model.MdStations).filter_by(station_id=id).first()
#         return md_station.processing_skycar
     
#     except Exception as e :
#         md_db_log.error(redBright(f'get_md_station_processing error. Exception thrown: {e}'))

# def get_md_station(id):
#     try:
#         curr_session = Session()
#         md_station = curr_session.query(model.MdStations).filter_by(station_id=id).first()
#         return md_station
#     except Exception as e:
#         md_db_log.error(redBright(f'get_md_station error. Exception thrown: {e}'))

# def station_processing_skycar(id):
#     try:
#         curr_session = Session()
#         station = curr_session.query(model.MdStations).filter_by(station_id=id).first()
#         station.processing_skycar = True
#         station.updated_at = dt.now()
#         curr_session.commit()
#     except Exception as e:
#         md_db_log.error(redBright(f'station_processing_skycar error. Exception thrown: {e}'))

# def clear_md_station_processing(id):
#     try:
#         curr_session = Session()
#         station = curr_session.query(model.MdStations).filter_by(station_id = id).first()
#         station.processing_skycar = False
#         station.updated_at = dt.now()
#         curr_session.commit()
#     except Exception as e:
#         md_db_log.error(redBright(f'clear_md_station_processing error. Exception thrown: {e}'))

# def get_md_requests():
#     try:
#         curr_session = Session()
#         requests = curr_session.query(model.MdWebsocketRequests).filter(model.MdWebsocketRequests.status != enum_collection.OrderStatus.COMPLETED.value).all()
#         return requests
#     except Exception as e:
#         md_db_log.error(redBright(f'get_md_requests error. Exception thrown: {e}'))

# def complete_md_request(id):
#     try:
#         curr_session = Session()
#         request = curr_session.query(model.MdWebsocketRequests).filter_by(id = id).first()
#         request.status = enum_collection.OrderStatus.COMPLETED.value
#         curr_session.commit()
#     except Exception as e:
#         md_db_log.error(redBright(f'complete_md_request error. Exception thrown: {e}'))

# def find_md_ws_by_jobid(job_id):
#     try:
#         curr_session = Session()
#         request = curr_session.query(model.MdWebsocketRequests).filter_by(job_id = job_id).first()
#         return request
#     except Exception as e:
#         md_db_log.error(redBright(f'find_md_ws_by_jobid error. Exception thrown: {e}'))

# def add_md_ws_request(entity, data, event,job_id= None, namespace=None):
#     try:
#         curr_session = Session()
#         with app.app_context():
#             if job_id != None: #prevent approval and door open/close to spawn multiple times if plc sends multiple messages
#                 duplicate_request = find_md_ws_by_jobid(job_id)
#             else:
#                 duplicate_request = None
#             if duplicate_request is None:
#                 if job_id is None:
#                     request = model.MdWebsocketRequests(
#                         entity=entity,
#                         data=data,
#                         event=event,
#                         namespace=namespace,
#                         status=enum_collection.OrderStatus.AVAILABLE.value,
#                         retry=0,
#                         created_at=dt.now(),
#                         updated_at=dt.now()
#                     )
#                 else:
#                     request = model.MdWebsocketRequests(
#                         job_id = job_id,
#                         entity=entity,
#                         data=data,
#                         event=event,
#                         namespace=namespace,
#                         status=enum_collection.OrderStatus.AVAILABLE.value,
#                         retry=0,
#                         created_at=dt.now(),
#                         updated_at=dt.now()
#                     )
#                 curr_session.add(request)
#                 curr_session.commit()

#     except Exception as e:
#         md_db_log.error(redBright(f'add_ws_request error. Exception thrown: {e}'))

# def clear_md_status_requests(zone):
#     try:
#         curr_session = Session()
#         entity = f'{enum_collection.ExternalEntities.TC.value}_{zone}'
#         curr_session.query(model.MdWebsocketRequests).filter_by(entity=entity, event='md-status').delete()
#         curr_session.commit()
#     except Exception as e:
#         md_db_log.error(redBright(f'clear_md_status_ws error. Exception thrown: {e}'))

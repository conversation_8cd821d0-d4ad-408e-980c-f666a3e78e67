FLASK_APP=wsgi.py
FLASK_ENV=development
SERVICE_AREA=Zone D
 
SECRET_KEY=secret!
SQLALCHEMY_TRACK_MODIFICATIONS=False
SQLALCHEMY_ECHO = True

# ------  Application port  ------
ASRS_FLASK_RUN_PORT = 4100


# ---- Asrs specific settings ----
DEV_ASRS_FLASK_HOST = 127.0.0.1

DEV_ASRS_TCPIP_HOST = 127.0.0.1
DEV_ASRS_TCPIP_PORT = 3535


# ------ General env settings -------
DEV_DB_HOST=127.0.0.1
DEV_DB_PORT=5432
DEV_DB_USER=postgres
DEV_DB_PASSWORD=password
DEV_DB_NAME=local_hwx
DEV_MEDIATOR = 127.0.0.1:5911
DEV_ASRS_RABBITMQ_HOST = localhost
DEV_ASRS_RABBITMQ_PORT = 5672
DEV_ASRS_RABBITMQ_USER = guest
DEV_ASRS_RABBITMQ_PASSWORD = guest

 
 
# ---- PROD ----
# ---- Asrs specific settings ----
PROD_ASRS_FLASK_HOST = 0.0.0.0
PROD_ASRS_TCPIP_HOST = 127.0.0.1
PROD_ASRS_TCPIP_PORT = 3535

PROD_DB_HOST=
PROD_DB_PORT=
PROD_DB_USER=pingofficial
PROD_DB_PASSWORD=pingofficial@2018
PROD_DB_NAME=hardwarex
PROD_MEDIATOR = 127.0.0.1:3000
PROD_ASRS_RABBITMQ_HOST = localhost
PROD_ASRS_RABBITMQ_PORT = 5672
PROD_ASRS_RABBITMQ_USER = guest
PROD_ASRS_RABBITMQ_PASSWORD = guest


# ---- Program behaviour settings ----
SKIP_ASRS_VALIDATION = True

# RMQ
ASRS_RABBITMQ = 
# RMQ QUEUE
WMS_QUEUE = 
TC_BT_QUEUE = 
TEAMS_WEBHOOK_QUEUE = 

# MUST NOT SET TO TRUE FOR PRODUCTION
DRYRUN_ASRS =

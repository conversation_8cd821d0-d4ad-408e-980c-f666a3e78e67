import datetime

from typing import List

from ... import models as model, common as common
from .http_request_dal import HttpRequestSQLAlchemyQueries


db_func_log = common.LogManager('cube_db_func',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_func_log))
class HttpRequestDBFunc:

    http_request_dal = HttpRequestSQLAlchemyQueries()

    def create_request(self,data:dict)->int:
        data['created_at'] =datetime.datetime.now(),
        post_id = self.http_request_dal.create_request(data)
        return post_id
    
    def find_request(self,filter_dict:dict)->model.HttpRequests:
        request = self.http_request_dal.find_request(filter_dict)
        return request
    
    def find_requests(self,filter_dict:dict)->List[model.HttpRequests]:
        requests = self.http_request_dal.find_requests(filter_dict)
        return requests
    
    def update_request(self,req_id:int,update:dict):
        self.http_request_dal.update_request(req_id,update)
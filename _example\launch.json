{
    "version": "0.2.0",
    "configurations": [
        
        {
            "name": "Python Debugger: Station Service",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/station_service/wsgi.py",  // Path to wsgi.py
            "console": "integratedTerminal",                         // Use VS Code's terminal
            "justMyCode": false,                                    // Debug all code, including libraries
            "cwd": "${workspaceFolder}/station_service",            // Set working directory to station_service
            "env": {}                                              // Optional environment variables
        },
        {
            "name": "Python Debugger: Asrs Service",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/asrs_service/wsgi.py",  // Path to wsgi.py
            "console": "integratedTerminal",                         // Use VS Code's terminal
            "justMyCode": false,                                    // Debug all code, including libraries
            "cwd": "${workspaceFolder}/asrs_service",            // Set working directory to asrs_service
            "env": {}                                              // Optional environment variables
        }
    ]
}
Step 1 - Create Virtual Environment
--------------------------------------------
`python -m venv venv`

Step 2 - Activate Virtual Environment
--------------------------------------------
`source venv/Scripts/activate`

Step 4 - Install the required dependencies 
--------------------------------------------
`pip install -r requirements.txt`

Step 5 - Create .env 
--------------------------------------------
Create a `.env` folder. Refer sample_env for the correct variable

Step 6 - Run the program 
--------------------------------------------
`python wsgi.py`

Optional - Seed Database
--------------------------------------------
Use `python seed.py {{project}}` to seed according to different project.

>{{project}} = any element from array ['penta'] 


Swagger OpenAPI Specification
--------------------------------------------
{{web-server-url}}/cube/doc\
e.g http://127.0.0.1:4000/cube/doc



DB Migration
-------------------------------------------
```
flask db init
flask db migrate
flask db upgrade
```
For migration with directory

```
flask db init --directory flask_migration/local
flask db migrate --directory flask_migration/local
flask db upgrade --directory flask_migration/local
```

Push Docker Image to AWS
-------------------------------------------
Run `./build_push_aws.sh {{tag}}` with the image tag to psuh to AWS ECR 

> Make sure you have the aws ecr username and password set up complete
import datetime
import pytz
import threading

from collections import defaultdict
from typing import List, Tuple,Dict

from ... import models as model,common as common,enum_collections as ec

from .station_mov_dal import StationMovementSQLAlchemyQueries

db_func_log = common.LogManager('cube_db_func',display_console=True)
utc = pytz.UTC    
station_mov_lock = threading.Lock()

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_func_log))
class StationMovementDBFunc:
    
    station_mov_dal = StationMovementSQLAlchemyQueries()
    # READ 

    def find_job(self,filter_dict:dict):
        return self.station_mov_dal.find_job(filter_dict=filter_dict)

    def find_job_join_st(self,filter_dict:dict)->List[Tuple[model.StationMovement,model.Stations]]:
        return self.station_mov_dal.find_job_join_st(filter_dict=filter_dict)

    def find_job_by_id(self,job_id:int)->model.StationMovement:
        return self.station_mov_dal.find_job(dict(id = job_id))
    
    def find_succ_job(self,job_id:int)->model.StationMovement:
        return self.station_mov_dal.find_job(dict(pred_id = job_id))
    
    def find_all_active_broadcast_jobs(self,station_id:int)->List[model.StationMovement]:
        return self.station_mov_dal.find_jobs(filter_dict=dict(station_id = station_id),not_filter_dict=dict(status = ec.OrderStatus.COMPLETED.value,plc_ack=None))
    
    def find_all_active_broadcast_jobs_join_st(self)->List[Tuple[model.StationMovement,model.Stations]]:
        return self.station_mov_dal.find_jobs_join_st(not_filter_dict=dict(status = ec.OrderStatus.COMPLETED.value,plc_ack=None))

    def find_all_to_broadcast_active_jobs(self)->List[Tuple[model.StationMovement,model.Stations]]:
        return self.station_mov_dal.find_jobs_join_st(filter_dict=dict(plc_ack=False),not_filter_dict=dict(status = ec.OrderStatus.COMPLETED.value))

    def find_latest_job(self,filter_dict:dict)->model.StationMovement:
        return self.station_mov_dal.find_latest_job(filter_dict)

    # def find_active_job(self,filter_dict:dict)->model.StationMovement:
        # return self.station_mov_dal.find_job_join_order(filter_dict=filter_dict, not_filter_dict=dict(status = ec.OrderStatus.COMPLETED.value))

    def find_next_light_up_job(self,matrix_code:int)->Tuple[model.StationMovement,model.Stations]|None:

        sis_station_list = common.MatrixConverter.get_matrix_station_list(matrix_code)
        if not sis_station_list:
            return None
        pending_light_up_job_list : List[Tuple[model.StationMovement,model.Stations]] = []
        for st in sis_station_list:
            light_up_job_join_st = self.find_job_join_st(dict(type=ec.CubeStationJobType.LIGHT_UP.value,
                                                        status=ec.OrderStatus.PENDING.value,
                                                        station_id=st.id))
            if light_up_job_join_st:
                pending_light_up_job_list.append(light_up_job_join_st)
        if not pending_light_up_job_list:
            return None

        return min(pending_light_up_job_list, key=lambda job_tuple: job_tuple[0].updated_at)


    def recover_regular_job(self,station_id:int, bin_no:str, from_index:int, to_index:int)->model.StationMovement:
        # more info, easier to find the job but most likely will be 3|5|C|bin_id command 
        # look for incomplete job to recover first
        jobs = self.station_mov_dal.find_all_recoverable_active_jobs(station_id,bin_no)
        if from_index:
            from_index = int(from_index)
            jobs = [j for j in jobs if j.from_index == from_index]
        if to_index:
            to_index = int(to_index)
            jobs = [j for j in jobs if j.to_index == to_index]
        job = jobs[0] if jobs else None                 
        # no incomplete job to recover, try to recover from complete job
        if not job:
            jobs = self.station_mov_dal.find_all_recoverable_jobs(station_id,bin_no)
            if from_index:
                from_index = int(from_index)
                jobs = [j for j in jobs if j.from_index == from_index]
            if to_index:
                to_index = int(to_index)
                jobs = [j for j in jobs if j.to_index == to_index]
            job = jobs[0] if jobs else None                                 
        return job
        
    def recover_bridge_job(self,bin_no:str, to_index:int,adj_station_id:int):
        """
        Recover bridge job done message without job id mostly will be "ST,11,J,,|6|C|{bin_no}"
        Need another way to recover because in db the bridge job is belong to ST 7 , Bridge type , 0 to 6 
        """
        jobs = self.station_mov_dal.find_all_recoverable_active_bridge_jobs(adj_station_id,bin_no)
        if to_index:
            to_index = int(to_index)
            jobs = [j for j in jobs if j.to_index == to_index]
        
        job = jobs[0] if jobs else None

        return job

    # CHECKING

    def check_job_completed(self,job_id:int)->bool:
        job  = self.find_job_by_id(job_id)
        if not job :
            db_func_log.error(f"No job found for id {job_id}")
            return False
        else:
            if job.status == ec.OrderStatus.COMPLETED.value:
                return True
            else:
                return False

    def check_job_exist(self,bin_no:str, station_id:int)->bool:
        job = self.station_mov_dal.find_job(filter_dict=dict(bin_no = bin_no,station_id = station_id),
                                            not_filter_dict=dict(status = ec.OrderStatus.COMPLETED.value))        
        if job:
            return True
        else:
            return False
        
    def check_move_to_pick_exist(self,order_id:int)->bool:  # Used to prevent sm spawning two move to pick for 12 cell station
        job = self.station_mov_dal.find_job(dict(type = ec.CubeStationJobType.MOVE_TO_PICK.value,
                                                 order_id=order_id))
        if job:
            return True
        else:
            return False

    def check_pred_job_completed(self,job:model.StationMovement)->bool:
        pred_job_id = job.pred_id
        if pred_job_id:
            pred_job = self.find_job_by_id(int(pred_job_id))
            if pred_job:
                if pred_job.status !=  ec.OrderStatus.COMPLETED.value:
                    return False
            else:
                error_msg = f"Pred job not found, pred job id : {pred_job_id}"
                db_func_log.error(f'check_pred_job_completed error. {error_msg}')
                return  False
        return True

    def check_job_broadcastable(self,job:model.StationMovement,station:model.Stations)->bool:
        if job.pred_id:
            if not self.check_pred_job_completed(job):
                return False
        if job.type==ec.CubeStationJobType.LIGHT_UP.value and station.bin_at_worker != job.bin_no:
            return False

        return True
    
    def is_cut_queue(self,job:model.StationMovement, station_code:int)->Tuple[bool,str]:
        """
        To check if a job is cutting the queue of the station 
        Use the queue in station to check cut queue

        Args:
            job (model.StationMovement): current job
            station_code (int): station code

        Returns:
            bool: boolean value if there is cut queue occur 
        """

        from . import st_db_func      
        
        station  = st_db_func.get_station_by_code(station_code)
        if job.type == ec.CubeStationJobType.BRIDGE.value:
            station  = st_db_func.get_station_by_code(station.adjacent)
        
        if station.type != ec.CubeStationType.I.value:
            st_queue = st_db_func.get_station_queue(station.code)
            if job.bin_no not in st_queue: # will not check if not in queue ( possible for large station MTW and NB ) 
                return False, ""

            index = st_queue.index(job.bin_no) # get index of current bin no 

            while index != 0 :
                index = index - 1
                checking_bin = st_queue[index]
                checking_job = self.station_mov_dal.find_latest_job(dict(type = job.type,
                                                         bin_no = checking_bin,
                                                         station_id = station.id))

                if not checking_job:
                    if station.type in [ec.CubeStationType.BRIDGE.value,ec.CubeStationType.QC.value]:
                        continue 
                    else:
                        return True, f'Bin before {job.bin_no} has not job type {job.type}'
                if checking_job.to_index != job.to_index:
                    continue
                if checking_job.status != ec.OrderStatus.COMPLETED.value:
                    return True, f"Bin {checking_job.bin_no} should come before {job.bin_no} in station queue"
                else:
                    return False, ""
    
        return False, ""
    
    def invalid_condition(self,job:model.StationMovement, station_code:int)->Tuple[bool,str]:
        """
        To check if there is an invalid situation occur in ihub 
        
        If an error occur, it means there is a job (Job A) which has the same type with current job,
        Job A is completed but the next job of the job A is not completed
        Which mean this  job should be not be completed since will have two same bin at same position

        MTW
            Normal station
            - just check the previous bin complete NEXT_BIN job or not
            Large station / QC station
            - since current bin is not in queue, and will be added to the end of the queue,
              so check the last bin in queue comeplte NEXT_BIN job or not
        PRE_MOVE
            Large station 
            - check the previous bin complete MTP job or not 
            - if yes, still need to check if the MTP job is at index 10, if yes, need to 
              check the UP of previous bin also 
            - only large station will have same PRE_MOVE to index and pick point index
            QC station ( currently follow large station, command code is the implementation below)
            - just check if the plc receive our move command, will assume index 20 is clear
              after plc receive move commad by checking plc_ack status
            - cause moving from index 20 to pick point take some time to complete
        MTP 
            - go through all the bin previous form last to first, checking the status of the 
              first UP job of the bin with same to_index , mutiple pick point station can complete 
              MTP by different order

        Args:
            job (model.StationMovement): current job 
            station_code (int): station code 

        Returns:
            Tuple[bool,str]: [is invalid,invalid_msg]
        """
        
        from . import st_db_func,st_order_db_func
            
        station = st_db_func.get_station_by_code(station_code)  
        if station.type != ec.CubeStationType.I.value:
            st_queue = st_db_func.get_station_queue(station_code)

            if len(st_queue) ==0 :
                return False, ""
            
            if job.bin_no in st_queue:
                index = st_queue.index(job.bin_no) # get index of current bin no
                if index == 0 :
                    return False, ""
                
                checking_index = index - 1
            
                                
            # move to work need to make sure next_bin job is done
            if job.type in  [ec.CubeStationJobType.MOVE_TO_WORK.value,ec.CubeStationJobType.BRIDGE.value]:
                if job.bin_no in st_queue: # will not check if not in queue ( possible for large station MTW and NB ) 
                    next_bin_job = self.station_mov_dal.find_latest_job(dict(type = ec.CubeStationJobType.NEXT_BIN.value,
                                                                             bin_no = st_queue[checking_index],
                                                                             station_id = station.id))
                    return next_bin_job.status != ec.OrderStatus.COMPLETED.value, f"{next_bin_job.type} Job ID {next_bin_job.id} with bin {next_bin_job.bin_no} need to be completed before handling this message :"

                # large and iqc station will only have bin in queue after work arrival 
                else:
                    next_bin_job = self.station_mov_dal.find_latest_job(dict(type = ec.CubeStationJobType.NEXT_BIN.value,
                                                                             bin_no = st_queue[-1],
                                                                             station_id = station.id))
                    return next_bin_job.status != ec.OrderStatus.COMPLETED.value, f"{next_bin_job.type} Job ID {next_bin_job.id} with bin {next_bin_job.bin_no} need to be completed before handling this message :"
            
            # pre movement job need to make sure move to pick is done
            # if job.type == ec.CubeStationJobType.PRE_MOVEMENT.value:
            #     move_to_pick_job = self.station_mov_dal.find_latest_job(dict(type = ec.CubeStationJobType.MOVE_TO_PICK.value,
            #                                                                  bin_no = st_queue[checking_index],
            #                                                                  station_id = station.id))
                
            #     # large st per_movement need to make sure the update pick in index 10 also need to be completed
            #     if move_to_pick_job.status == ec.OrderStatus.COMPLETED.value and move_to_pick_job.to_index == job.to_index:
            #         update_pick_job = self.station_mov_dal.find_latest_job(dict(type = ec.CubeStationJobType.UPDATE_PICK.value,
            #                                                                     bin_no = st_queue[checking_index],
            #                                                                     station_id = station.id))
            #         return update_pick_job.status != ec.OrderStatus.COMPLETED.value, f"{update_pick_job.type} Job ID {update_pick_job.id} with bin {update_pick_job.bin_no} need to be completed before handling this message :" 
            #     else:
            #         if station.type == ec.CubeStationType.LARGE.value or station.type == ec.CubeStationType.QC.value:
            #             return move_to_pick_job.status != ec.OrderStatus.COMPLETED.value, f"{move_to_pick_job.type} Job ID {move_to_pick_job.id} with bin {move_to_pick_job.bin_no} need to be completed before handling this message :"
                    # if station.type == ec.CubeStationType.QC.value:
                    #     # if waiting for MOVE_TO_PICK completed, bin will wait too long at zone 20
                    #     return not move_to_pick_job.plc_ack, f"{move_to_pick_job.type} Job ID {move_to_pick_job.id} with bin {move_to_pick_job.bin_no} need to be processed by plc before handling this message :"          
            
            #  move to pick need to make sure update pick job is done
            if job.type == ec.CubeStationJobType.MOVE_TO_PICK.value:
                while checking_index  >= 0 :
                    update_pick_job = self.station_mov_dal.find_latest_job(dict(type = ec.CubeStationJobType.UPDATE_PICK.value,
                                                                                bin_no = st_queue[checking_index],
                                                                                station_id = station.id))
                    if update_pick_job.to_index > job.to_index:
                        checking_index -= 1
                        continue
                    return update_pick_job.status != ec.OrderStatus.COMPLETED.value, f"{update_pick_job.type} Job ID {update_pick_job.id} with bin {update_pick_job.bin_no} need to be completed before handling this message :" 
                return False , ""
        elif station.type == ec.CubeStationType.I.value:
            if job.type == ec.CubeStationJobType.LIGHT_UP.value:
                # check if ligth up bin is similar to the bin at worker 
                return station.bin_at_worker != job.bin_no,f"ST{station.code} should not receive light up done job."               
        return False, ""
    
    def station_is_processing(self,station_code:int)->bool:
        from . import st_db_func

        is_processing = False

        station = st_db_func.get_station_by_code(station_code)

        processing_job = self.station_mov_dal.find_jobs(dict(status = ec.OrderStatus.PROCESSING.value,
                                                             station_id = station.id))
        if processing_job:
            is_processing = True

        if station.gw_operating:
            is_processing = True

        return is_processing

    def get_bin_queue_indexing(self,station_code:int)-> str :
        """ This function return PLC bin indexing string to allow mock PLC client resume the station movement from where it left.

        Current support 3,6,7, 12 cell conveyor, for other type conveyor require further enhancement.
        Other station type will only shoW the station queue 

        Work point expression 
            next_bin = A + move_to_work = C + move_to_pick = A
        
        On going work point expression
            move_to_work = P
        
        On going pick point expression
            move_to_pick = P 

        Pick point expression
            move_to_pick = C

        Possible Combinations  (nickson)
        0 to 4 on going work point bin and 0 to 2+n on going pick point bin
        0 to 4 on going work point bin and 0 to 1+n on going pick point bin and pick point bin
        0 to 3 on going work point bin and work point bin and 0 to 2+n on going pick point bin
        0 to 3 on going work point bin and work point bin and 0 to 1+n on going pick point bin and pick point bin

        Loop Through Station Queue (BO)
        - Start from earliest bin in station 
        - MTP
            - COMPLETED : bin must be at pick point
            - PROCESSING: bin is on the way to pick point, add it to the index before pick point
        - PRE
            * only for large station
            - COMPLETED : bin must be at decision point (inner pick point )
            - PROCESSING: bin is on the way to decision point , add it to the index before decision point
        - NEXT_BIN
            - COMPLETED : bin must be on worker point since the MTP job is not ack by station yet, else it will not fall into this clause 
            - AVAILABLE : Depends on the statuse of MTW
                -MTW
                    - COMPLETED : bin must be at worker point 
                    - PROCESSING: bin is on the way to work point, add it to the index before work point 
                    - AVAIALBLE : bin must be at drop point 

        * for all the step above, once I add a bin into the slot before work or before pick, will decrease the 
          index_to_fill_for_drop_to_work/index_to_fill_for_work_to_pick variable by 1 
          If they go below 0/work point, means there is error 

        Args:
            station_code (int): _description_

        Raises:
            NotImplementedError: _description_

        Returns:
            str: 1|2|3||4|5 or |||3||5 or others.

        """

        from . import st_db_func


        def _update_queue(queue:List[str],index:int,bin_no:str):
            if queue[index] != "":
                queue[index] = queue[index]+","+bin_no
            else:
                queue[index] = bin_no

        station = st_db_func.get_station_by_code(station_code)

        if station.type not in [ec.CubeStationType.REGULAR.value, ec.CubeStationType.I.value, ec.CubeStationType.LARGE.value]:
            return ','.join(station.queue)
        
        if station.type == ec.CubeStationType.I.value:
            queue = station.queue.copy()
            combined_queue = '|'.join(queue)
            return combined_queue
        
        if station.type == ec.CubeStationType.REGULAR.value:
            index_to_fill_for_drop_to_work = station.worker - 1         # start from index before work point
            index_to_fill_for_work_to_pick = station.inner_pick - 1     # start from index before pick point 
        
        else:
            index_to_fill_for_drop_to_work = station.worker - 1         # start from index before work point
            index_to_fill_for_work_to_pick = station.outer_pick - 1     # start from index before outer pick point
        expected_st_bin_queue = [""] * station.cell # initialize array with n slot 


        st_queue = station.queue.copy()
        jobs  = self.station_mov_dal.get_grouped_processing_order(station.id)
        bin_jobs : Dict[str,List[model.StationMovement]] = defaultdict(list)
        # set up bin jobs
        {bin_jobs[j.bin_no].append(j) for j in jobs}


        # compare bin_no in station to make sure station queue tally with station movements
        # large station will have differnt bin no since only add to st_queue after reaching work point
        # so for large station, all bin before work point will be append to first index
        if set(st_queue) - set(bin_jobs.keys()) or set(bin_jobs.keys())- set(st_queue):
            if station.type == ec.CubeStationType.LARGE.value:
                if set(bin_jobs.keys())- set(st_queue):
                    bin_before_large_work = set(bin_jobs.keys())- set(st_queue)
                    str_bin_before_large_work = ','.join(bin_before_large_work)
                    expected_st_bin_queue[0] = str_bin_before_large_work
                else:
                    raise Exception("Invalid Condition")
            else:
                raise Exception("Invalid Condition")

        # will be loop in order of station queue which mean start from earliest bin first 
        for bin in st_queue:
            current_bin_job = bin_jobs[bin]  

            for index,job in enumerate(current_bin_job):
                if job.type == ec.CubeStationJobType.UPDATE_PICK.value:
                    pass
                elif job.type == ec.CubeStationJobType.MOVE_TO_PICK.value:
                    if job.status == ec.OrderStatus.COMPLETED.value:
                        _update_queue(expected_st_bin_queue,job.to_index,bin)
                        break
                    elif job.status == ec.OrderStatus.PROCESSING.value:
                        if index_to_fill_for_work_to_pick<station.worker:
                            raise Exception("Invalid Condition in bin_zoning - index_to_fill_for_work_to_pick")
                        _update_queue(expected_st_bin_queue,index_to_fill_for_work_to_pick,bin)
                        index_to_fill_for_work_to_pick -= 1
                        break
                    else:
                        pass
                # elif job.type == ec.CubeStationJobType.PRE_MOVEMENT.value:
                #     if job.status == ec.OrderStatus.COMPLETED.value:
                #         _update_queue(expected_st_bin_queue,station.inner_pick,bin)
                #         index_to_fill_for_work_to_pick -= 1
                #         break
                #     if job.status == ec.OrderStatus.PROCESSING.value:
                #         if index_to_fill_for_work_to_pick<station.worker:
                #             raise Exception("Invalid Condition in bin_zoning - zone_qty_to_from_work_to_pick")
                #         elif index_to_fill_for_work_to_pick==station.inner_pick:
                #             index_to_fill_for_work_to_pick -= 1
                #         _update_queue(expected_st_bin_queue,index_to_fill_for_work_to_pick,bin)
                #         index_to_fill_for_work_to_pick -= 1
                #         break 
                elif job.type == ec.CubeStationJobType.NEXT_BIN.value:
                    if job.status == ec.OrderStatus.AVAILABLE.value:
                        # enroll bin no MTW job
                        if index == len(current_bin_job)-1:
                            _update_queue(expected_st_bin_queue,station.worker,bin)
                            break
                        mtw_job = current_bin_job[index+1]
                        if mtw_job.status == ec.OrderStatus.COMPLETED.value:
                            _update_queue(expected_st_bin_queue,station.worker,bin)
                            break
                        if station.type == ec.CubeStationType.LARGE.value:
                            break
                        if mtw_job.status == ec.OrderStatus.PROCESSING.value:
                            if index_to_fill_for_drop_to_work <0:
                                raise Exception("Invalid Condition in bin_zoning - index_to_fill_for_drop_to_work")
                            _update_queue(expected_st_bin_queue,index_to_fill_for_drop_to_work,bin)
                            index_to_fill_for_drop_to_work -= 1
                            break
                        elif mtw_job.status == ec.OrderStatus.AVAILABLE.value:
                            _update_queue(expected_st_bin_queue,mtw_job.from_index,bin)

                    if job.status == ec.OrderStatus.COMPLETED.value:
                        _update_queue(expected_st_bin_queue,station.worker,bin)
                        break

        result = '|'.join(expected_st_bin_queue)
        return result
        
        # region nickson implementation 
        # pre_work_queue = []
        # worker = str()
        # post_work_queue = []
        # pick_point_queue = str()
    
        # bins = set()
        # movements :List[model.StationMovement] = []
       
        # # Work point bin
        # for bin_no in bins :
        #     jobs: List[model.StationMovement] = [j for j in movements if j.bin_no == bin_no]

        #     # find work point bin, the bin pending next bin, if found, slice all jobs before matched job to fill in pre_work_queue , use created_at to sort asc
        #     for j in jobs :

        #         # multi criteria validation , if match any one of the clause ,the bin is not at worker point point.
        #         if j.type == ec.CubeStationJobType.NEXT_BIN.value :  
        #             if j.status != ec.OrderStatus.AVAILABLE.value :
        #                 break
        #         if j.type == ec.CubeStationJobType.MOVE_TO_WORK.value :
        #             if j.status != ec.OrderStatus.COMPLETED.value :
        #                 break
        #         if j.type == ec.CubeStationJobType.MOVE_TO_PICK.value :
        #             if j.status != ec.OrderStatus.AVAILABLE.value :
        #                 break
        #     else :
                
        #         worker = bin_no
        #         current_workpoint_jobs = [j for j in movements if j.type == ec.CubeStationJobType.NEXT_BIN.value and j.bin_no == bin_no ]
        #         current_job = current_workpoint_jobs[0] if current_workpoint_jobs else None
        #         if current_job :
        #             other_next_bins_jobs = [j for j in movements if j.type == ec.CubeStationJobType.NEXT_BIN.value and j.created_at > current_job.created_at]
        #             sorted_other_next_bins_jobs = sorted(other_next_bins_jobs,key=lambda x : x.created_at)
        #             sorted_bin_no = [j.bin_no for j in sorted_other_next_bins_jobs]
        #             pre_work_queue = sorted_bin_no[::-1]

        #     continue

        # # On going work point bin
        # #TODO 

        # # Pick point bin
        # for bin_no in bins :
        #     if bin_no in pre_work_queue or bin_no in worker :
        #         continue

        #     jobs: List[model.StationMovement] = [j for j in movements if j.bin_no == bin_no]
        #     for j in jobs : 
        #         if j.type == ec.CubeStationJobType.MOVE_TO_PICK.value :
        #             if j.status == ec.OrderStatus.COMPLETED.value :
        #                 pick_point_queue = bin_no
                    
        
        # # On going pick point bin
        # on_going_pick_point_jobs = [j for j in movements if j.type == ec.CubeStationJobType.MOVE_TO_PICK.value and j.status == ec.OrderStatus.PROCESSING.value ]
        # sorted_on_going_pick_point_jobs = sorted(on_going_pick_point_jobs,key=lambda x : x.created_at)
        # sorted_bin_no = [j.bin_no for j in sorted_on_going_pick_point_jobs]
        # post_work_queue = sorted_bin_no[::-1]


        # while len(pre_work_queue)< zone_qty_to_from_drop_to_work:
        #     pre_work_queue.insert(0,str())

        # # while len(post_work_queue)< zone_qty_to_from_work_to_pick :
        # #     post_work_queue.insert(0,str())

        # pre = '|'.join(pre_work_queue)
        # post = '|'.join(post_work_queue)

        # if worker == '' and pick_point_queue == '':
        #     # if worker point no bin and pick point no bin, might be got 2 bin || 3 bin on going pick point.
        #     if len(pre_work_queue) > zone_qty_to_from_drop_to_work :
        #         # 4 bins on going work point , max 2 bins for post_work_queue
        #         while len(post_work_queue) < zone_qty_to_from_work_to_pick  :
        #              post_work_queue.insert(0,str())


        #         pre = '|'.join(pre_work_queue)
        #         post = '|'.join(post_work_queue)
        #         full_queue = f'{pre}|{post}'
        #     else :
        #     # if on going worker queue is not > 3 , max 3 bins for post_work_queue
        #         while len(pre_work_queue)< zone_qty_to_from_drop_to_work:
        #             pre_work_queue.insert(0,str())

        #         while len(post_work_queue)< zone_qty_to_from_work_to_pick + 1 :
        #             post_work_queue.insert(0,str())

        #         pre = '|'.join(pre_work_queue)
        #         post = '|'.join(post_work_queue)
        #         full_queue = f'{pre}|{post}'

        # elif worker == '' and pick_point_queue != '' :
        #     # maximun 2 bins for post_work_queue or 1 bins if len of pre_work_queue > 3
        #     if len(pre_work_queue) > zone_qty_to_from_drop_to_work :
        #         while len(post_work_queue) < zone_qty_to_from_work_to_pick -1  :
        #              post_work_queue.insert(0,str())

        #         pre = '|'.join(pre_work_queue)
        #         post = '|'.join(post_work_queue)
        #         full_queue = f'{pre}|{post}|{pick_point_queue}'

        #     else :
        #     # post_work_queue max is 2 use case where pick point got 1 bin , pre_work_queue max is 3 bins
        #         while len(pre_work_queue)< zone_qty_to_from_drop_to_work:
        #             pre_work_queue.insert(0,str())
                
        #         while len(post_work_queue) < zone_qty_to_from_work_to_pick   :
        #              post_work_queue.insert(0,str())

        #         pre = '|'.join(pre_work_queue)
        #         post = '|'.join(post_work_queue)
        #         full_queue = f'{pre}|{post}|{pick_point_queue}'

        # elif worker != '' and pick_point_queue == '' :
        #     # maximum 2 bins for post work queue
        #     # maximum 3 bins for pre work queue
        #     while len(post_work_queue) < zone_qty_to_from_work_to_pick   :
        #              post_work_queue.insert(0,str())

        #     while len(pre_work_queue)< zone_qty_to_from_drop_to_work:
        #             pre_work_queue.insert(0,str())

        #     pre = '|'.join(pre_work_queue)
        #     post = '|'.join(post_work_queue)
        #     full_queue = f'{pre}|{worker}|{post}'

       
        # elif worker != '' and pick_point_queue != '' :
        #     # pick point got bin , work point got bin
        #     # maximum 3 bins for pre work queue
        #     # maximum 1 bins for post work queue
        #     while len(pre_work_queue)< zone_qty_to_from_drop_to_work:
        #             pre_work_queue.insert(0,str())

        #     while len(post_work_queue) < zone_qty_to_from_work_to_pick -1  :
        #              post_work_queue.insert(0,str())
            
        #     full_queue = f'{pre}|{worker}|{post}|{pick_point_queue}'
        # else :
        #     data = {
        #         'worker_point' : worker,
        #         'pick_point' : pick_point_queue
        #     }
        #     raise NotImplementedError(f"get_bin_queue_indexing() use case not handle. {data}")
        
        # return full_queue

        #endregion
    def check_st_queue(self,station_code:int,cube:str)->List[str]:
        from . import st_db_func,st_order_db_func

        station = st_db_func.get_station_by_code(station_code)

        bin_list = st_db_func.get_station_queue(station_code)
        filtered_bin_list = []
        if station.type != ec.CubeStationType.I.value:
            for bin in bin_list:
                # make sure already next bin
                if not st_order_db_func.check_bin_is_processed(station.code,bin):
                    continue
                
                if cube:
                    to_index = st_db_func.get_pick_index_by_cube(station_code,cube)
                    order = st_order_db_func.find_active_order(station.code,bin)
                    update_pick_job = self.find_job(dict(type=ec.CubeStationJobType.UPDATE_PICK.value,
                                                         order_id=order.id))
                    if not update_pick_job:
                        continue

                    if update_pick_job.to_index != to_index:
                        continue
                    
                filtered_bin_list.append(bin)
        else:
            processed_bin_time = []

            for bin in bin_list:

                if not bin:
                    continue

                #check if already next bin and save the next bin time 
                if st_order_db_func.check_bin_is_processed(station.code,bin):
                    order = st_order_db_func.find_active_order(station.code,bin)
                    next_bin_job = self.find_job(dict(type=ec.CubeStationJobType.NEXT_BIN.value,
                                                      order_id=order.id))
                    processed_bin_time.append((bin,next_bin_job.updated_at))
            
            filtered_bin_list = [bin[0] for bin in sorted(processed_bin_time,key = lambda x: x[1])]
            
        return filtered_bin_list
    
    # CREATE / MODIFY

    def on_station_order(self,station_code:int,storage_code:str,tc_order_id:int,start_index:int,qc:bool=False,bridge:bool=False,enroll=False,to_index:int=None)->bool:
        from . import st_db_func,st_order_db_func
        
        station = st_db_func.get_station_by_code(station_code)        

        if self.check_job_exist(storage_code,station.id):
            return False

        if enroll:
            st_order_id = st_order_db_func.add_order(station_code,storage_code,None,ec.OrderType.ENROLL.value,is_enroll=True)
            self.create_station_jobs(station_code,storage_code,st_order_id,start_index,enroll=True,to_index=to_index)
            return True

        elif bridge:
            if not station.type in [ec.CubeStationType.BRIDGE.value,ec.CubeStationType.LARGE.value]:
                db_func_log.error(f'Bridge order for non bridge feature station ST{station.code}')
                return False
            
            st_order_id = st_order_db_func.add_order(station.code,storage_code,tc_order_id,ec.OrderType.BRIDGE.value)
            self.create_station_jobs(station_code,storage_code, st_order_id, start_index,bridge=True)
            return True
        elif qc:
            if station.type != ec.CubeStationType.QC.value:
                db_func_log.error(f'QC order for non QC station ST{station.code}')
                return False
            st_order_id = st_order_db_func.add_order(station.code,storage_code,tc_order_id,ec.OrderType.QC.value)
            self.create_station_jobs(station_code,storage_code, st_order_id, start_index,qc=True)
            return True
        else:
            st_order_id = st_order_db_func.add_order(station.code,storage_code,tc_order_id,ec.OrderType.NORMAL.value)
            self.create_station_jobs(station_code,storage_code, st_order_id, start_index)
            return True

    def create_station_jobs(self,station_code:int,storage_code:str,order_id:int,start_index:int,qc:bool=False,bridge:bool=False,enroll:bool=False,bridge_job_id:int=None,to_index:int=None):
        
        from . import st_db_func
        station = st_db_func.get_station_by_code(station_code)

        if bridge:
            if station.type == ec.CubeStationType.BRIDGE.value:
                bridge_job_id = self.station_mov_dal.create_station_job(
                    station.id,
                    ec.CubeStationJobType.BRIDGE.value,
                    order_id,
                    start_index,
                    station.cell,
                    storage_code,
                    ec.OrderStatus.PROCESSING.value,
                    plc_ack=None,
                )
                #update station queue
                # st_db_func.update_station_queue(station.code,storage_code)
            else:
                if start_index == station.worker:
                    """Adj station bridge flow"""
                    next_bin_job_id = self.station_mov_dal.create_station_job(
                        station.id,
                        ec.CubeStationJobType.NEXT_BIN.value,
                        order_id,
                        station.worker,
                        station.worker,
                        storage_code,
                        ec.OrderStatus.AVAILABLE.value,
                        plc_ack=None,
                        pred_id=bridge_job_id,
                    )

                    if station.type == ec.CubeStationType.REGULAR.value:
                        to_pick_job_id = self.station_mov_dal.create_station_job(
                            station.id,
                            ec.CubeStationJobType.MOVE_TO_PICK.value,
                            order_id,
                            station.worker,
                            station.inner_pick,
                            storage_code,
                            ec.OrderStatus.AVAILABLE.value,
                            pred_id=next_bin_job_id,
                        )
                        self.station_mov_dal.create_station_job(
                            station.id,
                            ec.CubeStationJobType.UPDATE_PICK.value,
                            order_id,
                            station.inner_pick,
                            station.inner_pick,
                            storage_code,
                            ec.OrderStatus.AVAILABLE.value,
                            plc_ack=None,
                            pred_id=to_pick_job_id
                        )

                elif start_index in [station.inner_drop,station.outer_drop]:
                    """U station bridge flow"""
                    # when large sation receive bridde job with index droppin gindex, it means is a cross side job
                    if start_index == station.inner_drop:
                        pick_index = station.outer_pick
                    else:
                        pick_index = station.inner_pick

                    to_work_job_id = self.station_mov_dal.create_station_job(
                        station.id,
                        ec.CubeStationJobType.MOVE_TO_WORK.value,
                        order_id,
                        start_index,
                        station.worker,
                        storage_code,
                        ec.OrderStatus.AVAILABLE.value
                    )

                    next_bin_job_id = self.station_mov_dal.create_station_job(
                        station.id,
                        ec.CubeStationJobType.NEXT_BIN.value,
                        order_id,
                        station.worker,
                        station.worker,
                        storage_code,
                        ec.OrderStatus.COMPLETED.value,
                        plc_ack=None,
                        pred_id=to_work_job_id,
                    )

                    to_pick_job_id = self.station_mov_dal.create_station_job(
                        station.id,
                        ec.CubeStationJobType.MOVE_TO_PICK.value,
                        order_id,
                        station.worker,
                        pick_index,
                        storage_code,
                        ec.OrderStatus.AVAILABLE.value,
                        pred_id=to_work_job_id,
                    )

                    self.station_mov_dal.create_station_job(
                        station.id,
                        ec.CubeStationJobType.UPDATE_PICK.value,
                        order_id,
                        pick_index,
                        pick_index,
                        storage_code,
                        ec.OrderStatus.AVAILABLE.value,
                        plc_ack=None,
                        pred_id=to_pick_job_id,
                    )
            return 
        if station.type in [ec.CubeStationType.REGULAR.value,ec.CubeStationType.BRIDGE.value]:
            if not enroll:
                mtw_status, plc_ack = (ec.OrderStatus.PROCESSING.value,None) if station.type == ec.CubeStationType.BRIDGE.value else (ec.OrderStatus.AVAILABLE.value, False)
                to_work_job_id = self.station_mov_dal.create_station_job(
                    station.id,
                    ec.CubeStationJobType.MOVE_TO_WORK.value,
                    order_id,
                    station.inner_drop,
                    station.worker,
                    storage_code,
                    mtw_status,
                    plc_ack = plc_ack
                )
            
                next_bin_job_id = self.station_mov_dal.create_station_job(
                    station.id,
                    ec.CubeStationJobType.NEXT_BIN.value,
                    order_id,
                    station.worker,
                    station.worker,
                    storage_code,
                    ec.OrderStatus.AVAILABLE.value,
                    plc_ack=None,
                    pred_id=to_work_job_id,
                )
                prev_id = next_bin_job_id
            else:
                prev_id = None

            to_pick_job_id = self.station_mov_dal.create_station_job(
                station.id,
                ec.CubeStationJobType.MOVE_TO_PICK.value,
                order_id,
                station.worker,
                station.inner_pick,
                storage_code,
                ec.OrderStatus.AVAILABLE.value,
                pred_id=prev_id
            )

            self.station_mov_dal.create_station_job(
                station.id,
                ec.CubeStationJobType.UPDATE_PICK.value,
                order_id,
                station.inner_pick,
                station.inner_pick,
                storage_code,
                ec.OrderStatus.AVAILABLE.value,
                plc_ack=None,
                pred_id=to_pick_job_id,
            )
            #update station queue
            st_db_func.update_station_queue(station.code,storage_code)

            return 
        if station.type == ec.CubeStationType.LARGE.value:
            if not enroll:
                to_work_job_id = self.station_mov_dal.create_station_job(
                    station.id,
                    ec.CubeStationJobType.MOVE_TO_WORK.value,
                    order_id,
                    start_index,
                    station.worker,
                    storage_code,
                    ec.OrderStatus.AVAILABLE.value
                )

                next_bin_job_id = self.station_mov_dal.create_station_job(
                    station.id,
                    ec.CubeStationJobType.NEXT_BIN.value,
                    order_id,
                    station.worker,
                    station.worker,
                    storage_code,
                    ec.OrderStatus.AVAILABLE.value,
                    plc_ack=None,
                    pred_id=to_work_job_id,
                )

            else:
                to_pick_job_id = self.station_mov_dal.create_station_job(
                    station.id,
                    ec.CubeStationJobType.MOVE_TO_PICK.value,
                    order_id,
                    station.worker,
                    to_index,
                    storage_code,
                    ec.OrderStatus.AVAILABLE.value
                )

                update_job_id = self.station_mov_dal.create_station_job(
                    station.id,
                    ec.CubeStationJobType.UPDATE_PICK.value,
                    order_id,
                    to_index,
                    to_index,
                    storage_code,
                    ec.OrderStatus.AVAILABLE.value,
                    plc_ack=None,
                    pred_id=to_pick_job_id,
                )
                #update station queue
                st_db_func.update_station_queue(station.code,storage_code)
            return   
        if station.type == ec.CubeStationType.QC.value:
            if qc:
                to_work_job_id = self.station_mov_dal.create_station_job(
                    station.id,
                    ec.CubeStationJobType.MOVE_TO_WORK.value,
                    order_id,
                    start_index,
                    station.worker,
                    storage_code,
                    ec.OrderStatus.AVAILABLE.value
                )
                prev_id = to_work_job_id
                from_index = station.worker

            else:
                to_junction_job_id = self.station_mov_dal.create_station_job(
                    station.id,
                    ec.CubeStationJobType.MOVE_TO_JUNCTION.value,
                    order_id,
                    start_index,
                    20,
                    storage_code,
                    ec.OrderStatus.AVAILABLE.value
                )
                prev_id = to_junction_job_id
                from_index = 20

            self.station_mov_dal.create_station_job(
                station.id,
                ec.CubeStationJobType.NEXT_BIN.value,
                order_id,
                from_index,
                from_index,
                storage_code,
                ec.OrderStatus.AVAILABLE.value,
                plc_ack=None,
                pred_id=prev_id
            )
            return 
        if station.type == ec.CubeStationType.I.value:

            self.station_mov_dal.create_station_job(
                station.id,
                ec.CubeStationJobType.UPDATE_PICK.value,
                order_id,
                station.inner_pick,
                station.inner_pick,
                storage_code,
                ec.OrderStatus.AVAILABLE.value,
                plc_ack=None
            )

            # update station queue
            st_db_func.update_station_queue(station.code,storage_code,station.inner_drop)
            if enroll is False:
                # update station gw opearting status
                st_db_func.update_station(station.code,dict(gw_operating = False))
            return
        
    def inject_door_open(self,next_bin_job:model.StationMovement, bridge_job:model.StationMovement):
        
        door_open_id = self.station_mov_dal.create_station_job(
            next_bin_job.station_id,
            ec.CubeStationJobType.DOOR_OPEN.value,
            next_bin_job.order_id,
            next_bin_job.from_index,
            next_bin_job.to_index,
            next_bin_job.bin_no,
            ec.OrderStatus.AVAILABLE.value,
            pred_id = bridge_job.id,
        )

        self.station_mov_dal.update_job(next_bin_job.id,dict(pred_id=door_open_id))
        
    def spawn_move_to_work(self,station_code:int,bin_no:str,from_index:int,to_index:int)->int:
        from . import st_order_db_func,st_db_func

        st_order = st_order_db_func.find_active_order(station_code,bin_no)
        station = st_db_func.get_station_by_code(station_code)

        to_work_job_id = self.station_mov_dal.create_station_job(
            station.id,
            ec.CubeStationJobType.MOVE_TO_WORK.value,
            st_order.id,
            from_index,
            to_index,
            bin_no,
            ec.OrderStatus.PROCESSING.value,
        )
        
        light_up_job_id = self.station_mov_dal.create_station_job(
            station.id,
            ec.CubeStationJobType.LIGHT_UP.value,
            st_order.id,
            to_index,
            to_index,
            bin_no,
            ec.OrderStatus.AVAILABLE.value,
            pred_id=to_work_job_id,
        )

        next_bin_job_id = self.station_mov_dal.create_station_job(
            station.id,
            ec.CubeStationJobType.NEXT_BIN.value,
            st_order.id,
            to_index,
            to_index,
            bin_no,
            ec.OrderStatus.AVAILABLE.value,
            plc_ack=None,
            pred_id=light_up_job_id,
        )
        
        self.station_mov_dal.create_station_job(
            station.id,
            ec.CubeStationJobType.LIGHT_DOWN.value,
            st_order.id,
            to_index,
            to_index,
            bin_no,
            ec.OrderStatus.AVAILABLE.value,
            pred_id=next_bin_job_id
        )

        return to_work_job_id
            
    def spawn_move_to_pick_job(self,station_code:int, bin_no:str, from_index:int ,to_index:int)->int:
        
        from . import st_db_func, st_order_db_func
        station = st_db_func.get_station_by_code(station_code)
        
        if station.type in [ec.CubeStationType.LARGE.value,ec.CubeStationType.QC.value]:
            order = st_order_db_func.find_active_order(station_code,bin_no)
            next_bin_job = self.find_job(dict(type=ec.CubeStationJobType.NEXT_BIN.value,
                                            order_id=order.id))

            if not self.check_move_to_pick_exist(order.id):
                if station.type == ec.CubeStationType.QC.value and from_index != 20:
                    to_junction_job_id = self.station_mov_dal.create_station_job(
                        station.id,
                        ec.CubeStationJobType.MOVE_TO_JUNCTION.value,
                        order.id,
                        station.worker,
                        20,
                        bin_no,
                        ec.OrderStatus.AVAILABLE.value,
                        pred_id=next_bin_job.id
                    )
                    prev_id = to_junction_job_id
                    from_index = 20
                else:
                    prev_id = next_bin_job.id

                to_pick_job_id = self.station_mov_dal.create_station_job(
                    station.id,
                    ec.CubeStationJobType.MOVE_TO_PICK.value,
                    order.id,
                    from_index,
                    to_index,
                    bin_no,
                    ec.OrderStatus.AVAILABLE.value,
                    pred_id=prev_id,
                )

                update_job_id = self.station_mov_dal.create_station_job(
                    station.id,
                    ec.CubeStationJobType.UPDATE_PICK.value,
                    order.id,
                    to_index,
                    to_index,
                    bin_no,
                    ec.OrderStatus.AVAILABLE.value,
                    plc_ack=None,
                    pred_id=to_pick_job_id,
                )

                return to_pick_job_id
            return None
        
        elif station.type == ec.CubeStationType.I.value:
            st_order = st_order_db_func.find_active_order(station_code,bin_no)                    
            
            to_pick_job_id = self.station_mov_dal.create_station_job(
                    station.id,
                    ec.CubeStationJobType.MOVE_TO_PICK.value,
                    st_order.id,
                    from_index,
                    to_index,
                    bin_no,
                    ec.OrderStatus.PROCESSING.value,
                )

            return to_pick_job_id
    
    def spawn_transit(self,station_code:int,bin_no:str,from_index:int,to_index:int)->int:

        from . import st_order_db_func, st_db_func
        st_order = st_order_db_func.find_active_order(station_code,bin_no)
        
        station = st_db_func.get_station_by_code(station_code)

        transit_job_id = self.station_mov_dal.create_station_job(
            station.id,
            ec.CubeStationJobType.TRANSIT.value,
            st_order.id,
            from_index,
            to_index,
            bin_no,
            ec.OrderStatus.PROCESSING.value,
        )
        
        return transit_job_id
    
    def set_processing(self,job_id:int):
        self.station_mov_dal.update_job(job_id,dict(status = ec.OrderStatus.PROCESSING.value))
        return 
    
    def set_plc_ack(self,job_id:int,plc_ack:bool)->Tuple[bool,str]:
        with station_mov_lock:
            job = self.find_job_by_id(job_id)
            if not job:
                return False, "Job not found"
            if job.status == ec.OrderStatus.COMPLETED.value:
                return False,f"Job {job_id} has already completed, cannot set processing"
            self.station_mov_dal.update_job(job_id,dict(plc_ack = plc_ack))
            return True,""
    
    def reset_job(self,job_id:int):
        self.station_mov_dal.update_job(job_id,dict(status = ec.OrderStatus.AVAILABLE.value,
                                                    plc_ack = False))
    def set_complete(self,job_id:int,ack:bool=False):
        with station_mov_lock:
            update = dict(status = ec.OrderStatus.COMPLETED.value)
            if ack:
                update['plc_ack'] = True
            self.station_mov_dal.update_job(job_id,update)
    
    def set_pending(self,job_id:int):
        with station_mov_lock:
            update = dict(status = ec.OrderStatus.PENDING.value)
            self.station_mov_dal.update_job(job_id,update)

    def set_update_complete(self,bin_no:str, station_code:int):  
        
        from . import st_db_func, st_order_db_func

        with station_mov_lock:
            station = st_db_func.get_station_by_code(station_code)
            order = st_order_db_func.find_active_order(station_code,bin_no)

            update_pick_job = None
            update_pick_job = self.station_mov_dal.find_job(filter_dict=dict(type=ec.CubeStationJobType.UPDATE_PICK.value,
                                                                            order_id=order.id))

            if update_pick_job == None:
                db_func_log.warning(f'There is no available UPDATE_PICK job for bin {bin_no} at ST{station_code}. Unable to flag update complete.')
                return 
        
            if not self.check_pred_job_completed(update_pick_job):
                db_func_log.warning(f'Pred job has not complete yet. Unable to complete UPDATE_PICK.')
                return 
            
            update = dict(status = ec.OrderStatus.COMPLETED.value,plc_ack=True)
            self.station_mov_dal.update_job(update_pick_job.id,update)
            
            st_db_func.update_st_queue_after_picking(station_code,bin_no)
            # mark order complete
            st_order_db_func.update_order(dict(id =update_pick_job.order_id),dict(status=ec.OrderStatus.COMPLETED.value))
            # update station gw opearting status
            st_db_func.update_station(station.code,dict(gw_operating = False))

            # inform wcs
            common.notify_wcs(f"Bin {bin_no} has picked away by SC at ST{station_code}")

    def patch_bin_no_by_order_id(self,order_id:int, new_bin_no:str):
        
        st_jobs = self.station_mov_dal.find_jobs(dict(order_id=order_id))
        for job in st_jobs:
            self.station_mov_dal.update_job(job.id,dict(bin_no=new_bin_no))
                    
        return True
    
    def complete_next_bin_job(self,station_code:int,storage_code:str,to_index:int=None)->Tuple[bool,str]:
        
        from . import st_db_func,st_order_db_func,st_event_db_func,st_gw_req_db_func

        station = st_db_func.get_station_by_code(station_code)

        if station.type in [ec.CubeStationType.LARGE.value, ec.CubeStationType.QC.value]:
            if not to_index in [station.inner_pick,station.outer_pick]:
                return (False,"Invalid to_index")
            
        order = st_order_db_func.find_active_order(station.code,storage_code)

        if not order:
            return (False,f"No order for bin {storage_code} in ST{station_code}, please check HCC module.")
        
        if order.is_processed:
            return (False,f"Bin {storage_code} in ST{station_code} already next bin.")


        next_bin_job = self.find_job(dict(type=ec.CubeStationJobType.NEXT_BIN.value,
                                                       order_id=order.id))
        if not next_bin_job:  
            return (False,f"No NEXT_BIN job found for bin {storage_code} in ST{station_code}, please check HCC module.")
                
        if not self.check_pred_job_completed(next_bin_job): #    return false
            return (False,f"Bin {storage_code} has not arrived at work point yet")

        invalid, _ = self.is_cut_queue(next_bin_job,station_code)
        if invalid:  
            return (False,f"Bin {storage_code} is cutting the queue of ST{station_code}")
        
        if station.is_overweight:
            return (False,f"Bin {storage_code} is overweight, please remove some items in the bin")

        self.set_complete(next_bin_job.id)
        st_order_db_func.update_order(dict(id=order.id),dict(is_processed=True,processed_at=datetime.datetime.now()))
        st_db_func.update_station_after_job_done(next_bin_job,station.code,str(next_bin_job.bin_no))
        #If it is a 12 cell or QC station, when SM nextbin need to start requesting check cube
        if station.type in [ec.CubeStationType.LARGE.value, ec.CubeStationType.QC.value]:
            self.spawn_move_to_pick_job(station_code, storage_code, next_bin_job.to_index,to_index)     
        elif station.type == ec.CubeStationType.I.value:
            st_event_db_func.create_st_event(ec.CubesIStationEvent.NEXT_BIN.value,station_code,storage_code,is_enroll= True if order.type==ec.OrderType.ENROLL.value else False,request_id=next_bin_job.id)
            st_gw_req_db_func.start_pending_gw_request(station_code)
        common.notify_wcs(f"Bin {storage_code} stored at ST{station_code}.")
        
        return (True,'')
        
    def light_up_next_bin(self,matrix_code:int):
        from . import st_db_func

        if light_up_job_join_st := self.find_next_light_up_job(matrix_code):
            light_up_job = light_up_job_join_st[0]
            station = light_up_job_join_st[1]
            st_db_func.update_station(station.code,dict(bin_at_worker=light_up_job.bin_no))
    # TC_DASHBOARD
    
    def get_station_movement(self,station_code: int)->dict:
        
        from . import st_db_func, st_order_db_func
        
        station = st_db_func.get_station_by_code(station_code)
        processing_order_movement = st_order_db_func.find_all_active_order_join_movement(station.code)
        runtime_data = [
            {**data[1].as_dict(), 'order_type': data[0].type} for data in processing_order_movement]
        return runtime_data
    

    # DELETE
    def delete_job(self,job_id:int):
        self.station_mov_dal.delete_job(job_id)
    
    def clear_jobs_with_order_id(self,order_id:int)->List[int]:
         return self.station_mov_dal.clear_jobs_with_order_id(order_id)
    
    def retrieve_station_job_data(self,station_id:int,date:str)->List[model.StationMovement]:
        date_object = datetime.datetime.strptime(date, '%Y-%m-%d').date()
        return self.station_mov_dal.find_jobs_by_date(station_id,date_object)
    
    
    
    
    
    

    # def get_station_storage(self,station_id: int) -> List:
    #     curr_session = Session()
    #     runtime_data: Dict[int, dict] = dict()
    #     db_data = curr_session.query(model.StationMovement).filter(
    #         model.StationMovement.station_id == station_id,
    #         model.StationMovement.status != ec.OrderStatus.COMPLETED.value
    #     ).distinct(model.StationMovement.bin_no)
    #     for data in db_data:
    #         data: model.StationMovement
    #         index_list = runtime_data.setdefault(data.from_index, dict(
    #             index = data.from_index,
    #             storage = list()
    #         ))
    #         index_list['storage'].append(data.bin_no)
    #     return sorted(list(runtime_data.values()), key=lambda x: x['index'])


# Obsolete function 

# def manual_override_station_jobs(station_id):
#     try:
#         start_time = time.time()
#         curr_session = Session()
#         curr_session.query(model.StationMovement).filter(model.StationMovement.status != ec.OrderStatus.COMPLETED.value, model.StationMovement.station_id == station_id).update({"status": ec.OrderStatus.COMPLETED.value})
#         curr_session.commit()
#         end_time = time.time()
#         metrics.register_func_average_time(start_time,end_time,sys._getframe().f_code.co_name)
#     except Exception as e:
#         db_log.error(redBright(f'manual_override_station_jobs error. Exception thrown: {e}'))
#         metrics.register_func_error(sys._getframe().f_code.co_name)

# def recover_job_by_time_6cell(station_id):
#     try:
#         start_time = time.time()
#         curr_session = Session()
#         with app.app_context():
#             job = curr_session.query(model.StationMovement).filter_by(station_id=station_id, from_index = 3).filter(model.StationMovement.status !=
#                                                                                                ec.OrderStatus.COMPLETED.value).order_by('created_at').first()
#             end_time = time.time()
#             metrics.register_func_average_time(start_time,end_time,sys._getframe().f_code.co_name)
#             return job
#     except Exception as e:
#         db_log.error(redBright(f'recover_job_by_time error. Exception thrown: {e}'))
#         metrics.register_func_error(sys._getframe().f_code.co_name)


# def check_completed(id):
#     try:
#         start_time = time.time()
#         curr_session = Session()
#         with app.app_context():
#             job = curr_session.query(model.StationMovement.status).filter(model.StationMovement.id == id).first()
#             end_time = time.time()
#             metrics.register_func_average_time(start_time,end_time,sys._getframe().f_code.co_name)
#             if job.status == ec.OrderStatus.COMPLETED.value:
#                 return True
#             else:
#                 return False
#     except Exception as e:
#         db_log.error(redBright(f'check_completed error. Exception thrown: {e}'))
#         metrics.register_func_error(sys._getframe().f_code.co_name)

# def find_bin_processing_job(bin_id, type = None):
#     try:
#         start_time = time.time()
#         curr_session = Session()
#         if type:
#             job = curr_session.query(model.StationMovement).filter_by(bin_no= bin_id,type = type, status = ec.OrderStatus.PROCESSING.value).first()
#         else:
#             job = curr_session.query(model.StationMovement).filter_by(bin_no = bin_id, status = ec.OrderStatus.PROCESSING.value).first()
#         end_time = time.time()
#         metrics.register_func_average_time(start_time,end_time,sys._getframe().f_code.co_name)
#         return job
#     except Exception as e:
#         db_log.error(redBright(f'find_bin_processing_job error. Exception thrown: {e}'))
#         metrics.register_func_error(sys._getframe().f_code.co_name)

    # def sync_job(self,old_job,status,station):
    #     if old_job != None:
    #         if old_job.type == ec.CubeStationJobType.NEXT_BIN.value and station.type == ec.CubeStationType.LARGE.value and old_job.status == ec.OrderStatus.AVAILABLE.value and status== ec.OrderStatus.COMPLETED.value:
    #             old_job_station = st_db_func.get_station_by_id(old_job.station_id)
    #             request_cube ={
    #                     'storage_code': int(old_job.bin_no),
    #                     'station_code': old_job_station.code
    #                 }
    #             websocket_db_func.websocket_db_func.add_ws_request(ec.ExternalEntities.SM.value,request_cube, 'check_bin_cube', namespace='station')
    #         curr_session = Session()
    #         old_job.status = status
    #         old_job.created_at = datetime.datetime.now()
    #         old_job.updated_at = datetime.datetime.now()

    #         if status == ec.OrderStatus.AVAILABLE.value:
    #             old_job.plc_ack = False
    #         elif status == ec.OrderStatus.PROCESSING.value or status == ec.OrderStatus.COMPLETED.value:
    #             old_job.plc_ack = True

    #         curr_session.commit()
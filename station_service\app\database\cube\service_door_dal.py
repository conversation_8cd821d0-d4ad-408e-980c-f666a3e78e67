from typing import List
from datetime import datetime as dt

from ... import app,DBMonitor,db
from ... import common as common,models as model

db_log = common.LogManager('cube_db_error',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_log))
class ServiceDoorSQLAlchemyQueries:

    @DBMonitor.retry_session
    def get_sd_by_id(self,id:int):
        return
    
    @DBMonitor.retry_session
    def add_new_sd(self,name:str,type:str,status:str,station_list:str)->int:
        with app.app_context():
            new_sd = model.ServiceDoor(
                name=name,
                type=type,
                status=status,
                station_list=station_list,
                created_at=dt.now(),
                updated_at=dt.now()
            )
            curr_session = db.session
            curr_session.add(new_sd)
            curr_session.commit()

            new_sd_id = new_sd.id

            return new_sd_id


    @DBMonitor.retry_session
    def find_sd(self,filter:dict)->model.ServiceDoor:
        with app.app_context():
            curr_session = db.session
            sd = curr_session.query(model.ServiceDoor).filter_by(**filter).first()
            return sd
    
    @DBMonitor.retry_session
    def find_sds(self,filter:dict=None)->List[model.ServiceDoor]:
        with app.app_context():
            curr_session = db.session
            if filter is None:
                sds = curr_session.query(model.ServiceDoor).all()
            else:
                sds = curr_session.query(model.ServiceDoor).filter_by(**filter).all()
            return sds
        
    @DBMonitor.retry_session
    def del_sd(self,id:int):
        with app.app_context():
            curr_session = db.session
            sd = curr_session.query(model.ServiceDoor).filter(model.ServiceDoor.id == id).delete()
            if not sd:
                db_log.error(f'No service door with id {id} found')
                return None
            curr_session.commit()
                
    @DBMonitor.retry_session
    def update_sd(self,id:int,update:dict):
        with app.app_context():
            curr_session = db.session
            sd:model.ServiceDoor= curr_session.query(model.ServiceDoor).filter(model.ServiceDoor.id == id).first()
            if sd:
                for k,v in update.items():
                    sd.__setattr__(k,v)
                sd.updated_at = dt.now()
                curr_session.commit()
            else:
                db_log.error(f'No service doors with station code {id} found')
                return None

    @DBMonitor.retry_session
    def reset_active_sd(self):
        with app.app_context():
            curr_session = db.session
            curr_session.query(model.ServiceDoor).update({"is_active": False,
                                                        "is_connected":False})
            curr_session.commit()
from dataclasses import dataclass
from app import db


@dataclass
class MdStations(db.Model):
    id: int
    station_id: int
    processing_skycar: bool
    pairing: bool
    zone: str
    created_at: str
    updated_at: str

    __tablename__ = 'md_stations'

    id = db.Column(
        db.Integer,
        primary_key=True
    )

    station_id = db.Column(
        db.Integer,
        index=False,
        unique=True,
        nullable=False
    )

    processing_skycar = db.Column(
        db.<PERSON>,
        index=False,
        default=False
    )

    pairing = db.Column(
        db.<PERSON>,
        index = False,
        default = False
    )
    zone = db.Column(
        db.String(128),
        index = False,
        unique = False,
        nullable = False
    )

    created_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

    updated_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

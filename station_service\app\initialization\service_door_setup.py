from ..runtime import runtime
from .. import database as db

from ..adapter.service_door_adapter import RedisServiceDoor


def service_door_initilization():
    '''
    reinitialize redis from db 
    '''
    runtime.runtime_sd.clear()
    sds = db.sd_db_function.get_all_sds_from_db()
    for sd in sds:
        runtime.runtime_sd[sd.id] = RedisServiceDoor(sd)

    db.sd_db_function.reset_active_sd()



        



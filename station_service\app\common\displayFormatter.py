from datetime import datetime
from termcolor import colored

def formatString(level:str,message:str):
    
    current_time = datetime.now().strftime('%H:%M:%S')

    match level:
        case 'ERROR':
            color_str = 'light_red'
        case 'WARNING':
            color_str = 'light_yellow'
        case _:
            color_str = None

    return f"{current_time} - {level} - {colored(message,color_str)}"

def messageColor(msg:str):
    if 'SUCCESS' in msg:
        return colored(msg,'light_green')
    elif 'ERR' in msg:
        return colored(msg,'light_red')
    else:
        return msg
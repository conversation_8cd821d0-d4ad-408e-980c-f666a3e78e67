from dataclasses import dataclass
from .. import db
from sqlalchemy.sql import func


@dataclass
class FlowTracking(db.Model):
    """Model to track request-response flows between TC, HCC, and PLC"""
    
    id: int
    request_id: str
    station_code: int
    storage_code: str
    tc_job_id: int
    position: int
    action: str
    current_stage: str
    is_stuck: bool
    stuck_reason: str
    is_waiting: bool
    waiting_reason: str
    retry_count: int
    total_elapsed_minutes: float
    stage_elapsed_minutes: float
    created_at: str
    updated_at: str
    completed_at: str

    __tablename__ = "flow_tracking"

    id = db.Column(
        db.Integer,
        primary_key=True
    )

    request_id = db.Column(
        db.String(128),
        index=True,
        unique=True,
        nullable=False
    )

    station_code = db.Column(
        db.Integer,
        index=True,
        nullable=False
    )

    storage_code = db.Column(
        db.String(128),
        index=True,
        nullable=False
    )

    tc_job_id = db.Column(
        db.Integer,
        index=True,
        nullable=False
    )

    position = db.Column(
        db.Integer,
        nullable=False
    )

    action = db.Column(
        db.String(32),
        nullable=False
    )

    current_stage = db.Column(
        db.String(128),
        nullable=False
    )

    is_stuck = db.Column(
        db.Boolean,
        default=False,
        nullable=False
    )

    stuck_reason = db.Column(
        db.Text,
        nullable=True
    )

    is_waiting = db.Column(
        db.Boolean,
        default=False,
        nullable=False
    )

    waiting_reason = db.Column(
        db.Text,
        nullable=True
    )

    retry_count = db.Column(
        db.Integer,
        default=0,
        nullable=False
    )

    total_elapsed_minutes = db.Column(
        db.Float,
        default=0.0,
        nullable=False
    )

    stage_elapsed_minutes = db.Column(
        db.Float,
        default=0.0,
        nullable=False
    )

    created_at = db.Column(
        db.DateTime(timezone=True),
        index=False,
        unique=False,
        nullable=True,
        default=func.clock_timestamp()
    )

    updated_at = db.Column(
        db.DateTime(timezone=True),
        index=False,
        unique=False,
        nullable=True,
        default=func.clock_timestamp(),
        onupdate=func.clock_timestamp()
    )

    completed_at = db.Column(
        db.DateTime(timezone=True),
        index=False,
        unique=False,
        nullable=True
    )

    def as_dict(self):
        import datetime
        
        result = {}
        for c in self.__table__.columns:
            value = getattr(self, c.name)
            if isinstance(value, datetime.datetime):
                result[c.name] = value.isoformat()
            else:
                result[c.name] = value

        return result


@dataclass
class FlowStageHistory(db.Model):
    """Model to track stage transitions in request flows"""
    
    id: int
    request_id: str
    stage: str
    entered_at: str
    timeout_at: str
    notes: str

    __tablename__ = "flow_stage_history"

    id = db.Column(
        db.Integer,
        primary_key=True
    )

    request_id = db.Column(
        db.String(128),
        index=True,
        nullable=False
    )

    stage = db.Column(
        db.String(128),
        nullable=False
    )

    entered_at = db.Column(
        db.DateTime(timezone=True),
        nullable=False,
        default=func.clock_timestamp()
    )

    timeout_at = db.Column(
        db.DateTime(timezone=True),
        nullable=True
    )

    notes = db.Column(
        db.Text,
        nullable=True
    )

    def as_dict(self):
        import datetime
        
        result = {}
        for c in self.__table__.columns:
            value = getattr(self, c.name)
            if isinstance(value, datetime.datetime):
                result[c.name] = value.isoformat()
            else:
                result[c.name] = value

        return result

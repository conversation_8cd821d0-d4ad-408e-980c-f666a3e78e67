
# from logfile import log
# import eventlet, datetime
# from simple_chalk import redBright
# from app.models.station_movement import Station_movement
# from app.enum_collections import CubeStationType, OrderStatus,CubeStationJobType
# from app.database.cube.station_mov_db_func import station_mov_db_func

# cube_eh_log = log.get_logger_console('cube_eh')

# class StationEHServices():
#     def sync_station(self,bins,station, jobs):
#         try:
#             pick_points = [pick for pick in [station.outer_pick,station.inner_pick] if pick != None]
#             reversed_bins = dict(reversed(list(bins.items()))) #reverse the keys to handle bins in reverse order so that bin arriving earlier will sync up
#             # for index in index_keys:
#             for index, bin in reversed_bins.items():
#                 # bin = bins[index]
#                 index= int(index)
#                 if bin != '':
#                     eventlet.sleep(0.5)

#                     if index < station.worker:
#                         drop_to_work_status = OrderStatus.PROCESSING.value
#                         next_bin_status = OrderStatus.AVAILABLE.value
#                         move_to_pick_status = OrderStatus.AVAILABLE.value
#                         if station.type == CubeStationType.LARGE.value:
#                             pre_move_status = OrderStatus.AVAILABLE.value
#                     elif index == station.worker:
#                         drop_to_work_status = OrderStatus.COMPLETED.value
#                         next_bin_status = OrderStatus.AVAILABLE.value
#                         move_to_pick_status = OrderStatus.AVAILABLE.value
#                         if station.type == CubeStationType.LARGE.value:
#                             pre_move_status = OrderStatus.AVAILABLE.value
#                     elif index in pick_points:
#                         drop_to_work_status = OrderStatus.COMPLETED.value
#                         next_bin_status = OrderStatus.COMPLETED.value
#                         move_to_pick_status = OrderStatus.COMPLETED.value
#                         if station.type == CubeStationType.LARGE.value:
#                             pre_move_status = OrderStatus.COMPLETED.value
#                     else:
#                         drop_to_work_status = OrderStatus.COMPLETED.value
#                         next_bin_status = OrderStatus.COMPLETED.value
#                         if station.type == CubeStationType.LARGE.value:
#                             move_to_pick_status = OrderStatus.AVAILABLE.value
#                             pre_move_status = OrderStatus.PROCESSING.value
#                         elif station.type == CubeStationType.REGULAR.value:
#                             move_to_pick_status = OrderStatus.PROCESSING.value

#                     old_drop_job = self.find_old_job(jobs,bin,CubeStationJobType.MOVE_TO_WORK.value)
#                     old_next_bin_job = self.find_old_job(jobs,bin,CubeStationJobType.NEXT_BIN.value)
#                     old_move_to_pick_job = self.find_old_job(jobs,bin,CubeStationJobType.MOVE_TO_PICK.value)
#                     old_update_job = self.find_old_job(jobs,bin,CubeStationJobType.UPDATE_PICK.value)

#                     if station.type == CubeStationType.LARGE.value:
#                         old_pre_move_job = self.find_old_job(jobs,bin,CubeStationJobType.PRE_MOVEMENT.value)

#                     station_mov_db_func.sync_job(old_drop_job,drop_to_work_status,station)
#                     station_mov_db_func.sync_job(old_next_bin_job,next_bin_status,station)
#                     station_mov_db_func.sync_job(old_move_to_pick_job,move_to_pick_status,station)
#                     if station.type == CubeStationType.LARGE.value:
#                         if old_pre_move_job != None:
#                             station_mov_db_func.sync_job(old_pre_move_job, pre_move_status,station)
#                     station_mov_db_func.sync_job(old_update_job,OrderStatus.AVAILABLE.value,station)
#         except Exception as e:
#             cube_eh_log.error(redBright(f'sync_station error. Exception: {e}'))
        
#     def realign_bridge_job(self,bridge_station,bin_code, adj_station):
#         try:
#             job = station_mov_db_func.find_active_job_by_type(adj_station.id, CubeStationJobType.UPDATE_PICK.value, bin_code)
#             if job is None:
#                 job = station_mov_db_func.find_active_job_by_type(adj_station.id, CubeStationJobType.PRE_MOVEMENT.value, bin_code)

#             tc_order_id = job.order_id
#             created_time = job.created_at
        
#             station_mov_db_func.clear_jobs_with_order_id(tc_order_id)

#             move_to_work = Station_movement(
#                 station_id = bridge_station.id,
#                 type = CubeStationJobType.MOVE_TO_WORK.value,
#                 from_index = bridge_station.inner_drop,
#                 to_index = bridge_station.worker,
#                 bin_no= bin_code,
#                 order_id = tc_order_id,
#                 status=OrderStatus.COMPLETED.value,
#                 plc_ack = True,
#                 created_at=created_time,
#                 updated_at=datetime.datetime.now()
#             )
#             prev_id = station_mov_db_func.create_movement_job(move_to_work)

#             next_bin = Station_movement(
#                 station_id = bridge_station.id,
#                 type = CubeStationJobType.NEXT_BIN.value,
#                 from_index = bridge_station.worker,
#                 to_index = bridge_station.worker,
#                 bin_no = bin_code,
#                 order_id = tc_order_id,
#                 pred_id = prev_id,
#                 status = OrderStatus.AVAILABLE.value,
#                 created_at = created_time,
#                 updated_at = datetime.datetime.now()
#             )
#             prev_id = station_mov_db_func.create_movement_job(next_bin)

#             move_to_pick = Station_movement(
#                 station_id = bridge_station.id,
#                 type = CubeStationJobType.MOVE_TO_PICK.value,
#                 from_index = bridge_station.worker,
#                 to_index = bridge_station.inner_pick,
#                 bin_no = bin_code,
#                 order_id = tc_order_id,
#                 pred_id = prev_id,
#                 status = OrderStatus.AVAILABLE.value,
#                 created_at = created_time,
#                 updated_at = datetime.datetime.now()
#             )
#             prev_id = station_mov_db_func.create_movement_job(move_to_pick)

#             update_pick = Station_movement(
#                 station_id = bridge_station.id,
#                 type = CubeStationJobType.UPDATE_PICK.value,
#                 from_index = bridge_station.inner_pick,
#                 to_index = bridge_station.inner_pick,
#                 bin_no = bin_code,
#                 order_id = tc_order_id,
#                 pred_id = prev_id,
#                 status = OrderStatus.AVAILABLE.value,
#                 created_at = created_time,
#                 updated_at = datetime.datetime.now()
#             )
#             prev_id = station_mov_db_func.create_movement_job(update_pick)

#         except Exception as e:
#             cube_eh_log.error(redBright(f'cleanup_bridge_job error. Exception: {e}'))


#     def find_old_job(self,job_list, bin_no, job_type):
#         try:
#             for job in job_list:
#                 if job.bin_no == bin_no and job.type == job_type:
#                     return job
#                 else:
#                     None
#         except Exception as e:
#             cube_eh_log.error(redBright(f'find_old_job error. Exception: {e}'))
    
#     def generate_affected_jobs(self,station):
#         try:
#             job_list = []
#             jobs_update_pick = station_mov_db_func.find_active_job_by_type(station.id,CubeStationJobType.UPDATE_PICK.value)
#             if station.type == CubeStationType.REGULAR.value:  
#                 for job in jobs_update_pick:
#                     job_list.append(job)
#                     while job.pred_id != None:
#                         job = station_mov_db_func.find_job(job.pred_id)
#                         job_list.append(job)
#             elif station.type  == CubeStationType.LARGE.value:
#                 jobs_pre_move = station_mov_db_func.find_active_job_by_type(station.id, CubeStationJobType.PRE_MOVEMENT.value)
#                 all_jobs = jobs_update_pick + jobs_pre_move
#                 for job in all_jobs:
#                     if job not in(job_list):
#                         job_list.append(job)
#                     while job.pred_id != None:
#                         job = station_mov_db_func.find_job(job.pred_id)
#                         if job not in (job_list):
#                             job_list.append(job)

#             return job_list
#         except Exception as e:
#             cube_eh_log.error(redBright(f'generate_affected_jobs error. Exception: {e}'))
    
    
from __future__ import annotations

from typing import List

from ...runtime import runtime
from ... import adapter as adp,models as model,common as common
from .service_door_dal import ServiceDoorSQLAlchemyQueries


db_func_log = common.LogManager('cube_db_error',display_console=True)
@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_func_log))
class ServiceDoorDBFunc:
    
    sd_dal = ServiceDoorSQLAlchemyQueries()


    def add_new_sd(self,name:str,type:str,status:str,station_list:str,host:str=None,port:int=None)->int:
        new_sd_id = self.sd_dal.add_new_sd(name,type,status,station_list)

        return new_sd_id
    
    def get_sd_by_id(self,id:int)->adp.RedisServiceDoor:

        sd = runtime.runtime_sd[id] 

        if not sd:
            db_func_log.error(f'Could not find SD with id {id}')
            return None
        
        return sd

    def get_sds(self)->List[adp.RedisServiceDoor]:

        sd_list = runtime.get_runtime_sd_list()

        return sd_list

    def get_all_sds_from_db(self)->List[model.ServiceDoor]:
        sds = self.sd_dal.find_sds()
        return sds

    def get_sd_by_host_and_port(self,host:str,port:int)->adp.RedisServiceDoor:

        sd = self.sd_dal.find_sd(dict(host=host))

        if not sd :
            db_func_log.error(f"No station with host {host} and port {port} found in rt")
            return None
        
        return runtime.runtime_sd[sd.id]
    
    def get_sd_by_conn(self,conn:int)->adp.RedisServiceDoor:
        
        for sd in runtime.get_runtime_sd_list():
            sd:adp.RedisServiceDoor
            if sd.connection == conn:
                return sd
        return None

    
    def del_sd(self,id:int):
        self.sd_dal.del_sd(id)

    def update(self,id:int,update:dict):
        sd = runtime.runtime_sd[id]
        for key, value in update.items():
            if hasattr(sd, key):
                setattr(sd, key, value)
        update.pop('connection',None)
        self.sd_dal.update_sd(id,update)

    def sd_connecting(self,id:int,conn:int):
        self.update(id,dict(
            is_connected=True,
            connection=conn
        ))
    
    def sd_disconnecting(self,id:int):
        self.update(id,dict(
            is_connected=False,
            connection=None
        ))

    def reset_active_sd(self):
        for sd in runtime.get_runtime_sd_list():
            setattr(sd,'is_active',False)
            setattr(sd,'is_connected',False)
        self.sd_dal.reset_active_sd()

    def change_related_st_status(self,sd_id:int,is_maint:bool):

        from . import st_db_func
        from ...blueprints.route.client import HttpClient

        from ...service_handler.cube_handler import handler_log
        from ...communications_provider.socketio.cube import CubeWSEmit

        sd = self.get_sd_by_id(sd_id)
        st_db_func.update_list_of_st_maint_status([int(st) for st in sd.station_list],is_maint)
        [HttpClient.update_st_status(int(st_code)) for st_code in sd.station_list]
        CubeWSEmit.emit_active_station()
        handler_log.info(f'Setting {sd.station_list} maintenance status to {is_maint}',color='light_yellow')
import os
import json

from typing import List, Tuple
from termcolor import colored
from datetime import datetime
from urllib.parse import quote_plus
from pingspace_python_packages.redis import ConnectionPoolKwargs


try:
    class Config:
        FLASK_APP = os.getenv('FLASK_APP')
        FLASK_ENV = os.getenv('FLASK_ENV')
        SECRET_KEY = os.getenv('SECRET_KEY')
        SERVICE_AREA = os.getenv('SERVICE_AREA')
        HEARTBEAT = bool(os.getenv('HEARTBEAT'))
        SUPPORT_V2 = bool(os.getenv('SUPPORT_V2'))
        RECOVERY_MODE = bool(os.getenv('RECOVERY_MODE'))
        SIMULATION_PLC = bool(os.getenv('SIMULATION_PLC'))
        TC_DRY_RUN_MODE = bool(os.getenv('TC_DRYRUN_MODE'))
        MOCK_STATION_MSG = bool(os.getenv('MOCK_STATION_MSG'))
        SERVICE_START_DATE = datetime.today().strftime('%Y-%m-%d')
        ST_DOWN_THRESHOLD = int(os.getenv('STATION_DOWN_THRESHOLD'))
        RANDOM_STATION_ADDRESS = bool(os.getenv('RANDOM_STATION_ADDRESS'))
        HEARTBEAT_LOSS_THRESHOLD = int(os.getenv('HEARTBEAT_LOSS_THRESHOLD'))
        ERROR_CODE_VERSION=int(os.getenv('ERROR_CODE_VERSION',2))
        # http broadcast 
        WMS_MODE_CHANGE_ENABLED = bool(os.getenv('WMS_MODE_CHANGE_ENABLED'))
        WMS_MODE_CHANGE_NAMESPACE = os.getenv('WMS_MODE_CHANGE_NAMESPACE')
        MAINT_DASH_MODE_CHANGE_ENABLED = os.getenv('MAINT_DASH_MODE_CHANGE_ENABLED')
        MAINT_DASH_MODE_CHANGE_NAMESPACE = os.getenv('MAINT_DASH_MODE_CHANGE_NAMESPACE')
        MAINT_DASH_ERROR_REPORT_ENABLED = os.getenv('MAINT_DASH_ERROR_REPORT_ENABLED')
        MAINT_DASH_ERROR_REPORT_NAMESPACE = os.getenv('MAINT_DASH_ERROR_REPORT_NAMESPACE')
        # RMQ
        CUBE_RMQ = bool(os.getenv('CUBE_RABBITMQ'))
        HCC_INFO_Q = bool(os.getenv('WMS_QUEUE'))
        TCBT_WS_Q = bool(os.getenv('TC_BT_QUEUE'))
        WEBHOOK_Q = bool(os.getenv('TEAMS_WEBHOOK_QUEUE'))
        CENTRALIZED_LOGGING_Q = bool(os.getenv('CENTRALIZED_LOGGING_QUEUE'))
        CENTRALIZED_LOGGING_Q_NAME = os.getenv('CENTRALIZED_LOGGING_QUEUE_NAME')
        CENTRALIZED_LOGGING_Q_TTL = int(os.getenv('CENTRALIZED_LOGGING_QUEUE_TTL'))
        CENTRALIZED_LOGGING_Q_MSG_TYPE = os.getenv('CENTRALIZED_LOGGING_QUEUE_MSG_TYPE')
        # MONGO
        MONGO = bool(os.getenv('MONGO'))
        MONGO_REPLICA_SET = os.getenv('MONGO_REPLICA_SET')
        # SQL
        SQLALCHEMY_TRACK_MODIFICATIONS = os.getenv('SQLALCHEMY_TRACK_MODIFICATIONS')
        SQLALCHEMY_ECHO = 0

        @classmethod
        def get_env_type(cls):
            from app.enum_collections import EnvironmentType

            if cls.FLASK_ENV == EnvironmentType.DEVELOPMENT.value:
                return "DEV"
            elif cls.FLASK_ENV == EnvironmentType.PRODUCTION.value:
                return "PROD"
        
        @classmethod
        def get_auth_address(cls):
            cls.AUTH_URL = f'http://{os.getenv("AUTH_HOST")}:{os.getenv("AUTH_PORT")}'
            return cls.AUTH_URL
        
        @classmethod
        def get_client_ids(cls):
            if (os.getenv('CLIENT_ID')):
                cls.CLIENT_IDS = json.loads(os.getenv('CLIENT_ID'))
            return cls.CLIENT_IDS

        @classmethod
        def get_flask_conn(cls):
            env_type = cls.get_env_type()
            cls.FLASK_CONN = os.getenv(f'{env_type}_CUBE_FLASK_HOST'),os.getenv(f'CUBE_FLASK_RUN_PORT')
            return cls.FLASK_CONN
        
        # --------- ws ------------
        @classmethod
        def get_cube_ws_conn(cls):
            from app.communications_provider.socketio import WSConnetion
            env_type = cls.get_env_type()
            cls.CUBE_WS_CONN = WSConnetion(
                os.getenv(f'{env_type}_CUBE_WS_HOST'),
                int(os.getenv(f'CUBE_WS_PORT'))
            )
            return cls.CUBE_WS_CONN
        
        # --------- db -------------
        @classmethod
        def get_sqlalchemy_db_uri(cls):
            
            env_type = cls.get_env_type()
            cls.SQLALCHEMY_DATABASE_URI = 'postgresql://' + os.getenv(f'{env_type}_DB_USER') + ":" + quote_plus(os.getenv(f'{env_type}_DB_PASSWORD')) + "@" + \
                os.getenv(f'{env_type}_DB_HOST') + ":" + os.getenv(f'{env_type}_DB_PORT') + "/" + os.getenv(f'{env_type}_DB_NAME')
            return  cls.SQLALCHEMY_DATABASE_URI
        
        # --------- tcpip -------------
        @classmethod
        def get_cube_tcpip(cls):
            from app.communications_provider.tcpip import TCPIPConnection
        
            env_type = cls.get_env_type()
            cls.CUBE_TCPIP = TCPIPConnection(
                os.getenv(f'{env_type}_CUBE_TCPIP_HOST'),
                int(os.getenv(f'{env_type}_CUBE_TCPIP_PORT'))
            )
            return cls.CUBE_TCPIP
        
        # ------------- client -------------
        @classmethod
        def get_sm_host(cls):
            
            env_type = cls.get_env_type()
            cls.SM_HOST = 'http://'+os.getenv(f'{env_type}_SM')
            return cls.SM_HOST
        
        @classmethod
        def get_wms_host(cls):
            
            env_type = cls.get_env_type()
            cls.WMS_HOST = 'http://'+os.getenv(f'{env_type}_WMS')
            return cls.WMS_HOST
        
        @classmethod
        def get_maint_dash_host(cls):
            
            env_type = cls.get_env_type()
            cls.MAINT_DASH_HOST = 'http://'+os.getenv(f'{env_type}_MAINT_DASH')
            return cls.MAINT_DASH_HOST
        # ------------- mongo ------------
        @classmethod
        def get_mongo_conn(cls)->Tuple[List[str],str,str,str]:
            env_type = cls.get_env_type()
            cls.MONGO_HOST = json.loads(os.getenv(f'{env_type}_MONGO_HOST'))
            cls.MONGO_NAME = os.getenv(f'{env_type}_MONGO_NAME')
            cls.MONGO_USER = os.getenv(f'{env_type}_MONGO_USER')
            cls.MONGO_PASSWORD = os.getenv(f'{env_type}_MONGO_PASSWORD')

            return cls.MONGO_HOST, cls.MONGO_NAME, cls.MONGO_USER,cls.MONGO_PASSWORD
        # ------------- redis -------------
        @classmethod
        def get_redis_connection(cls):
            env_type = cls.get_env_type()
            cls.REDIS_CON =  os.getenv(f'{env_type}_REDIS_MODE'), \
                [ConnectionPoolKwargs(
                    host = conn["host"],
                    port = int(conn["port"]),
                    password = conn["password"],
                    db = int(conn["db"])
                ) for conn in json.loads(os.getenv(f"{env_type}_REDIS_CONN"))]
            return cls.REDIS_CON
        # ------------- rabbitmq -------------
        @classmethod
        def get_cube_rmq_con(cls):
            env_type = cls.get_env_type()
            cls.CUBE_RMQ_CON =  \
                os.getenv(f'{env_type}_CUBE_RABBITMQ_HOST'),\
                int(os.getenv(f'{env_type}_CUBE_RABBITMQ_PORT')),\
                os.getenv(f'{env_type}_CUBE_RABBITMQ_USER'),\
                os.getenv(f'{env_type}_CUBE_RABBITMQ_PASSWORD')
            return cls.CUBE_RMQ_CON

except Exception as e:
    print(colored(f'Please check your cube .env config', 'light_red'))

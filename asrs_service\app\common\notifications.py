from typing import TypedDict

from config import Config
from .. import common as common
from .. import enum_collections as ec
from pingspace_python_packages.rabbitmq import RabbitMQ as _RMQ, TeamsNotification, WebsocketNotification
from pingspace_python_packages.notifications.webhook import WebhookType, WebhookMessage, message_card, MessageCardSection, Fact



from .displayFormatter import formatString

# This class mainly use to send to RMQ 

class ShellMessage(TypedDict):
    """
    Will be used at tc dashbaord layer to decide the format of message to be displayed
    """
    message: str
    from_station: bool
    status: bool

class ServiceDoorMessage(TypedDict):
    """
    Information need to inform change on Service Door UI
    """
    sd_id:int
    event_name:str


def saveNotification(title:str,module:ec.ModuleCode,text:str=None,**kwargs):
    """
    For TEAMS Webhook 

    Args:
        id (str): device id 
        group (str): device module
        msg (str): msg
        desc (str): msg description
    """
    try:
        if Config.WEBHOOK_Q: 
            if module == ec.ModuleCode.STATION.value:
                type = [WebhookType.ST.value]
                title = f"{Config.SERVICE_AREA} {title}"
            else:
                type = [WebhookType.ASRS.value]
                title = f"{title}"
            _RMQ.put_to_background_queue_threadsafe(
                TeamsNotification.name,
                WebhookMessage(
                    type = type,
                    body = message_card(
                            title = title,
                            text = text,
                            sections = [MessageCardSection(facts = [
                                Fact(name=name, value=value)
                                for name, value in kwargs.items()
                            ])]
                        )
                    )
                )
            
    except Exception as e :
        print(formatString('Error',f'{saveNotification.__qualname__}() error : {e}'))


def notify_wcs(msg:str):
    """
    To update the bin status or staiton status to WCS

    Args:
        msg (str): the msg that wanted to send to wcs
    """
    try:
        if Config.HCC_INFO_Q:
            msg = common.RMQMessage.hcc_bin_info_messager(msg)
            _RMQ.put_to_background_queue_threadsafe(ec.RabbitMQQueue.HCC_MSG.value,msg)
    except Exception as e:
        print(formatString('Error',f'{notify_wcs.__qualname__}() error : {e}'))

def notify_shell(shell_command:str,status:bool,from_station:bool):
    """
    Send shell command to TMQ for TC_BT to consume

    Args:
        shell_command (str): the shell command received
        status (bool): send successful or not
        from_station (bool): shell command for station or to station 
    """
    try:
        if Config.TCBT_WS_Q:
            msg = common.RMQMessage.ws_server_messager('STATION',ShellMessage(message=shell_command,from_station=from_station,status=status))
            _RMQ.put_to_background_queue_threadsafe(WebsocketNotification.name, msg)
    except Exception as e:
        print(formatString('Error',f'{notify_shell.__qualname__}() error : {e}'))

def notify_service_door(sd_id:int,event_name:str):
    """
    Send service door status to FRONTEND
    """

    try:
        if Config.TCBT_WS_Q:
            msg = common.RMQMessage.ws_server_messager('SERVICE_DOOR',ServiceDoorMessage(sd_id=sd_id,event_name=event_name))
            _RMQ.put_to_background_queue_threadsafe(WebsocketNotification.name, msg)
    except Exception as e:
        print(formatString('Error',f'{notify_service_door.__qualname__}() error : {e}'))

def notify_centralized_logging(msg:str,log_type:str,log_name:str):
    try:
        if Config.CENTRALIZED_LOGGING_Q:
            msg = common.RMQMessage.centralized_logging_messager(msg,log_type,log_name)
            _RMQ.put_to_background_queue_threadsafe(Config.CENTRALIZED_LOGGING_Q_NAME,msg)
    except Exception as e:
        print(formatString('Error',f'{notify_centralized_logging.__qualname__}() error : {e}'))

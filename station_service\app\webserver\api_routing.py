class UrlFolder:
    db = "db"
    connection = "connection"
    mock = "mock"
    operation = "operation"
    runtime = "runtime"
    station = "station"
    testing = "testing"
    key = "key"
    service_door = "service_door"
    wms = "wms"


class UrlPath:
    test_log = "/test_log"
    # key
    new_key_pair = "/new_key_pair"
    hcc_public_key = "/hcc_public_key"
    oauth_public_key = "/public_key"
    # db
    query = "/query"
    # connection
    connection_list = "/connection_list"
    connection_ip_list = "/connection_ip_list"
    disconnect_st_connection = "/disconnect_st_connection"
    # station
    seed_stations = "/seed_stations"
    station_plc_status = "/station_plc_status"
    station_details = "/station_details"
    station_errors = "/station_errors"
    station_gw_status = "/station_gw_status"
    station_movements = "/station_movements"
    station_history = "/station_history"
    station_movements_index = "/station_movements/index"
    station_records = "/station_records"
    station_error_records = "/station_error_records"
    data_retrieval = "/data_retrieval"
    station_job_done_error_log = "/station_job_done_error_log"
    station_log = "/station_log"
    shell_command = "/shell_command"
    # sd
    service_door = "/sd"
    sd_open = "/open"
    sd_close ="/close"
    reset = "/reset"
    # runtime
    station = "/station"
    http_post = "/http_post"
    sio_req = "/sio_req"
    st_event = "/st_event"
    st_event_manual_flag = "/st_event/manual_flag"
    st_event_queue = "/st_event_queue"
    rearrnge_event_queue = "/st_event_queue/rearrange"
    pause_st_event_queue = "/st_event_queue/pause"
    rabbit_mq = "/rabbit_mq"
    # mock
    mock_station_msg = "/mock_station_msg"
    send_station_msg = "/send_station_msg"
    mock_ws_request = "/mock_ws_request"
    mock_station_order = "/mock_station_order"
    mock_inform_sm_enroll = "/mock_inform_sm_enroll"
    mock_complete_next_job = "/mock_complete_next_job"
    job_able_to_mock = "/job_able_to_mock"
    mock_emo ="/mock_emo"
    mock_sm_work_arrival_callback = "/mock_sm_work_arrival_callback"
    #operation
    next_bin = "/next_bin"
    update_bin_no = "/update_bin_no"
    undo_station_order = "/undo_station_order"
    check_queue = "/check_queue"
    gw_request = "/gw_request"
    station_enroll = "/station_enroll"
    station_maintenance = "/station_maint"
    charge_out_bin = "/charge_out_bin"
    dummy_recovery = "/dummy_recovery"
    skydrop_recovery = "/skydrop_recovery"
    skypick_recovery = "/skypick_recovery"
    bin_history = "/bin_history"
    # wms
    wms_dash = "/wms_dashboard"
    wms_st_enroll = "/station_enroll"
    wms_st_trasnfer = "/station_transfer"
    # healthcheck
    station_healthcheck = "/station_healthcheck"

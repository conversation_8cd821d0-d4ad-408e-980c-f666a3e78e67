import os
import datetime
import glob
import re
import shutil

from dateutil.relativedelta import relativedelta

from .logger_manager import LogManager
from .log import PathConfiguration

clear_log = LogManager('cube_clear_log',display_console=True)

    
class DelLog:
    """
    A class to perform log cleaning 

    Will remove the file that is not modified for more than 1 month in the directory 
    """
    retention = 1
    log_dir = PathConfiguration.get_directory_str()
    
    @classmethod
    def execute(cls):
        try:
            current_day  = datetime.datetime.today()
            retention_day = current_day - relativedelta(months=cls.retention)
            
            search_log = os.path.join(cls.log_dir, '*')
    
            logfile_list = [f for f in glob.glob(search_log) if re.search(r'[0-9]{4}-[0-9]{2}-[0-9]{2}',f)] #filter out non-log file
    
            for file in logfile_list:
                t_mod = os.path.getmtime(file)  # use modified time 
                t_mod = datetime.datetime.fromtimestamp(t_mod) 
                clear_log.info(file)
                
                if retention_day > t_mod:
                    try:
                        shutil.rmtree(file)
                        clear_log.info('Deleted : Yes')
                    except Exception as e:
                        clear_log.warning('Deleted: Failed')
                        clear_log.warning(f"Error: {e}")
                else:
                    clear_log.info('Deleted : Not over date limit')
        except Exception as e:
            clear_log.error(f'{cls.execute.__qualname__}() error: {e}')
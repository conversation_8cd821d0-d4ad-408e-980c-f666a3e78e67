import json

from typing import List
from termcolor import colored

from .. import database as db,models as model,common as common,enum_collections as ec

event_log = common.LogManager('event',display_console=True)

class EventAdapter:
    """
    Adapter class for event 

    When an event adapter is created, will also be added to db to update db 
    """
    def __init__(self,event : model.StationEvent):
        self.id :int = event.id
        self.event_name :str = event.event_name
        self.station_code : int = event.station_code
        self.storage_code : str = event.storage_code
        self.is_processed : bool = event.is_processed
        self.is_enroll : bool = event.is_enroll
        self.status : ec.OrderStatus = event.status
        self.request_id : int = event.request_id
        self.err_msg :str = event.err_msg
        self.job_to_complete_event : List[int] = json.loads(event.job_to_complete_event)

        # add to runtime station 
        station = db.st_db_func.get_station_by_code(self.station_code)
        station.event_queue.add_event(self)

    @common.log_and_suppress_error(event_log)
    def process(self):
        """
        Function to process the event when it is pop up from the event queue. 
        
        Different event have different flow

        5 event in total [REQ_DROP,DROP_DONE, NEXT_BIN, REQ_PICK, PICK_DONE]
        """
        from ..communications_provider.socketio import CubeWSEmit 


        station = db.st_db_func.get_station_by_code(self.station_code)

        self.start_processing()

        job_id_list:List[int] =list()

        if self.event_name == ec.CubesIStationEvent.REQ_DROP.value:
            drop_req : model.StationGatewayReq = db.st_gw_req_db_func.get_gateway_req_by_id(self.request_id)

            if drop_req.status == ec.OrderStatus.CANCELED.value:
                self.cancel_event()
                return 
            if db.st_order_db_func.get_num_of_bin_in_station(station.code) >= 2:
                CubeWSEmit.emit_staiton_request_event(station.code,drop_req.tc_job_id,drop_req.type,False,f"ST{station.code} has reach limit of 2 bins.")
                self.trigger_error(f"There is already at least 2 bin in station")
                return 
        
            # check condition of station
            st_queue = db.st_db_func.get_station_queue(station.code)
            
            # case: _ X _ 
            if bin_no := st_queue[station.inner_drop]:
                if db.st_order_db_func.check_bin_is_processed(station.code,bin_no):
                    transit_job_id = db.st_mov_db_func.spawn_transit(station.code,bin_no,from_index = station.inner_drop, to_index = station.buffer)
                    job_id_list.append(transit_job_id)
                else:
                    self.trigger_error("Bin unprocessed should not be at drop point, please check the error.")

        elif self.event_name == ec.CubesIStationEvent.DROP_DONE.value:
            st_queue = db.st_db_func.get_station_queue(station.code)
            
            if st_queue[station.inner_drop] != self.storage_code:
                self.trigger_error(f"DROP_DONE event but queue[drop point] does not have bin {self.storage_code}.")
                return
            # case: _ X _ or  _ X Y  
            if st_queue[station.worker] == "":
                to_work_job_id = db.st_mov_db_func.spawn_move_to_work(station.code,self.storage_code,station.inner_drop,station.worker)
                job_id_list.append(to_work_job_id)

                if st_queue[station.buffer] != "":
                    to_pick_job_id = db.st_mov_db_func.spawn_move_to_pick_job(station.code,st_queue[station.buffer],station.buffer,station.inner_pick)
                    job_id_list.append(to_pick_job_id)
            # case: Y X _   
            else:
                transit_job_id = db.st_mov_db_func.spawn_transit(station.code,self.storage_code,station.inner_drop,station.buffer)
                job_id_list.append(transit_job_id)
        
        elif self.event_name == ec.CubesIStationEvent.NEXT_BIN.value:

            st_queue = db.st_db_func.get_station_queue(station.code)

            if st_queue[station.worker] != self.storage_code:
                # will have a case where after pick done the event queue will move the processed bin to buffer/pick point first 
                if st_queue[station.inner_pick if self.is_enroll else station.buffer] != self.storage_code:
                    self.trigger_error(f"{self.event_name} but bin at worker point is not {self.storage_code}.")
                    return 
               
            # case: X _ _ 
            if st_queue[station.inner_drop] == "" and st_queue[station.buffer] == "":
                if not self.is_enroll:
                    transit_job_id = db.st_mov_db_func.spawn_transit(station.code,self.storage_code,station.worker,station.buffer)
                    job_id_list.append(transit_job_id)
                else:
                    to_pick_job_id = db.st_mov_db_func.spawn_move_to_pick_job(station.code,self.storage_code,station.worker,station.inner_pick)
                    job_id_list.append(to_pick_job_id)
            # case: X Y _ 
            elif st_queue[station.inner_drop] != "" and st_queue[station.buffer] == "":
                # check if bin Y is processed or not 
                decider_bin_no = st_queue[station.inner_drop]
                if not db.st_order_db_func.check_bin_is_processed(station.code,decider_bin_no):
                    if self.is_enroll:
                        self.trigger_error("Bin not processed should not be at pick point during enrollment")
                    else:
                        # self.trigger_error(f"Bin {st_queue[station.inner_drop]} should not be at pick point if it is not processed.")
                        transit_job_id = db.st_mov_db_func.spawn_transit(station.code,decider_bin_no,station.inner_drop,station.buffer)
                        job_id_list.append(transit_job_id)
                        to_pick_job_id = db.st_mov_db_func.spawn_move_to_pick_job(station.code,self.storage_code,station.worker,station.inner_pick)
                        job_id_list.append(to_pick_job_id)

                else:
                    pass
            # case: X _ Y 
            elif st_queue[station.inner_drop] == "" and st_queue[station.buffer] != "":
                # check if bin Y is processed or not 
                decider_bin_no = st_queue[station.buffer]
                if not db.st_order_db_func.check_bin_is_processed(station.code,decider_bin_no):
                    if self.is_enroll:
                        self.trigger_error("Bin not processed should not be at buffer point during enrollment")
                    else:
                        to_pick_job_id = db.st_mov_db_func.spawn_move_to_pick_job(station.code,self.storage_code,station.worker,station.inner_pick)
                        job_id_list.append(to_pick_job_id)
                else:
                    if self.storage_code != decider_bin_no:
                        self.trigger_error("Bin processed should not be at buffer point when there is bin at worker.")
  
            # case: X Y Z
            else:
                self.trigger_error(f'Station has more than 2 bin, please check station condition')
                return
               
        elif self.event_name == ec.CubesIStationEvent.REQ_PICK.value:
            pick_req : model.StationGatewayReq = db.st_gw_req_db_func.get_gateway_req_by_id(self.request_id)

            if pick_req.status == ec.OrderStatus.CANCELED.value:
                self.cancel_event()
                return 

            # check condition of station
            st_queue = db.st_db_func.get_station_queue(station.code)

            try:
                index = st_queue.index(self.storage_code)
            except ValueError:
                event_log.error(f'No bin {self.storage_code} in ST{station.code} queue.')
            if index == station.buffer:
                if st_queue[station.inner_pick] == "":
                    move_to_pick_job_id = db.st_mov_db_func.spawn_move_to_pick_job(station.code,self.storage_code,station.buffer,station.inner_pick)
                    job_id_list.append(move_to_pick_job_id)
                else:
                    self.trigger_error(f"Invalid situation occur, pick point should be clear")
                    return 
            elif index == station.inner_pick:
                # TODO check if the bin already processed 
                pass
            elif index == station.worker:
                self.trigger_error(f"Invalid situation occur, request to pick bin {self.storage_code} still at worker point, should move after next bin")
                return 

        elif self.event_name == ec.CubesIStationEvent.PICK_DONE.value:
            
            st_queue = db.st_db_func.get_station_queue(station.code)

            if st_queue[station.inner_pick] != "":
                self.trigger_error("PICK_DONE event but queue[pick point] still have bin.")
                return 
            
            #case: X _ _ 
            if st_queue[station.worker] != "":
                decider_bin_no = st_queue[station.worker]
                if db.st_order_db_func.check_bin_is_processed(station.code,decider_bin_no):
                    if not self.is_enroll:
                        if st_queue[station.buffer] != "":
                            self.trigger_error(f"Invalid situation occur, after picking there is still 2 bin in station")
                            return 
                        else:
                            transit_job_id = db.st_mov_db_func.spawn_transit(station.code,decider_bin_no,station.worker,station.buffer)
                            job_id_list.append(transit_job_id)
                    else:
                        to_pick_job_id = db.st_mov_db_func.spawn_move_to_pick_job(station.code,decider_bin_no,station.worker,station.inner_pick)
                        job_id_list.append(to_pick_job_id)

                else:
                    pass
            #case: _ _ X
            elif st_queue[station.buffer] != "":
                decider_bin_no = st_queue[station.buffer]
                if db.st_order_db_func.check_bin_is_processed(station.code,decider_bin_no):
                    self.trigger_error(f"Invalid situation occur, after picking, unprocessed bin should not be at buffer point")
                    return 
                else:
                    if st_queue[station.worker] != "":
                        self.trigger_error(f"Invalid situation occur, after picking there is still 2 bin in station")
                        return
                    else:
                        to_work_job_id = db.st_mov_db_func.spawn_move_to_work(station.code,decider_bin_no,station.buffer,station.worker)
                        job_id_list.append(to_work_job_id) 
        
        elif self.event_name == ec.CubesIStationEvent.RECOVERY.value: #special for recvoery after bin chargeout

            st_queue = db.st_db_func.get_station_queue(station.code)
            
            # case: _ Y _ where X was at worker point but chargeout manually left Y without any job
            if st_queue[station.inner_drop] != "" and st_queue[station.worker] == "" and st_queue[station.buffer] == "" and not db.st_order_db_func.check_bin_is_processed(station.code,st_queue[station.inner_drop]):
                to_work_job_id = db.st_mov_db_func.spawn_move_to_work(station.code,st_queue[station.inner_drop],station.inner_drop,station.worker)
                job_id_list.append(to_work_job_id)
            
            # case: _ _ Y where X was at worker/drop point but chargeout manually left Y wihtout any job
            if st_queue[station.buffer] != "" and st_queue[station.worker] == "" and st_queue[station.inner_drop] == "" and not db.st_order_db_func.check_bin_is_processed(station.code,st_queue[station.buffer]):
                to_work_job_id = db.st_mov_db_func.spawn_move_to_work(station.code,st_queue[station.buffer],station.buffer,station.worker)
                job_id_list.append(to_work_job_id)
            
            # case: Y _ _ where X was at pick point but chargeout manually left Y wihtout any job cause already next bin but cant move previously
            if st_queue[station.worker] != "" and st_queue[station.buffer] == "" and st_queue[station.inner_drop] == "" and db.st_order_db_func.check_bin_is_processed(station.code,st_queue[station.worker]):
                if not self.is_enroll:
                    transit_job_id = db.st_mov_db_func.spawn_transit(station.code,st_queue[station.worker],station.worker,station.buffer)
                    job_id_list.append(transit_job_id)
                else:
                    to_pick_job_id = db.st_mov_db_func.spawn_move_to_pick_job(station.code,st_queue[station.worker],station.worker,station.inner_pick)
                    job_id_list.append(to_pick_job_id)

        self.update_job_list(job_id_list)    
    

    @common.log_and_suppress_error(event_log)
    def update_job_list(self,job_id_list:List[int]):
        update = dict()
        update.update({"job_to_complete_event":job_id_list})
        db.st_event_db_func.update_st_event(self,update)

    @common.log_and_suppress_error(event_log)
    def start_processing(self):
        update = dict()
        update.update({"is_processed":True})
        db.st_event_db_func.update_st_event(self,update)
        event_log.info(colored(f"ST{self.station_code} Start processing - {self.event_name}",'light_green',attrs=["underline"]))

    
    @common.log_and_suppress_error(event_log)
    def trigger_error(self,err_msg:str):
        update = dict()
        update.update({"status":ec.OrderStatus.CRITICAL_ERROR.value,
                       "err_msg":err_msg})
        db.st_event_db_func.update_st_event(self,update)
        event_log.error(colored(f"Event - {self.event_name} for ST{self.station_code} is error : {self.err_msg}",attrs=["underline"]))


    @common.log_and_suppress_error(event_log)
    def cancel_event(self):
        update = dict()
        update.update({"status":ec.OrderStatus.CANCELED.value})
        db.st_event_db_func.update_st_event(self,update)
        event_log.error(colored(f"ST{self.station_code} Cancel event - {self.event_name}",attrs=["underline"]))
        
    @common.log_and_suppress_error(event_log)
    def complete_event(self):
        update = dict()
        update.update({"status":ec.OrderStatus.COMPLETED.value})
        db.st_event_db_func.update_st_event(self,update)
        event_log.info(colored(f"ST{self.station_code} Complete event - {self.event_name}",'light_green',attrs=["underline"]))


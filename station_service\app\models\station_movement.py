from .. import db

from sqlalchemy import func

class StationMovement(db.Model):
    id: int
    station_id: int
    type: str
    order_id: int
    from_index: int
    to_index: str
    bin_no: str
    data: str
    status: str
    plc_ack: bool
    pred_id: int
    created_at: str
    updated_at: str

    __tablename__ = 'station_movement'
    id = db.Column(
        db.Integer,
        primary_key=True)

    station_id = db.Column(
        db.Integer,
        db.ForeignKey('stations.id'))

    type = db.Column(
        db.String(128),
        index = False,
        nullable=False
    )

    order_id = db.Column(
        db.Integer,
        db.ForeignKey('station_order.id')
    )

    from_index = db.Column(
        db.Integer,
        index=False,
        unique=False
    )

    to_index = db.Column(
        db.Integer,
        index=False,
        unique=False
    )

    bin_no = db.Column(
        db.String(128),
        index=False,
        unique=False
    )

    data = db.Column(
        db.String(128),
        index=False,
        unique=False,
        nullable=True
    )

    status = db.Column(
        db.String(128),
        index=False,
        unique=False
    )

    plc_ack = db.Column(
        db.Boolean,
        index=False
    )

    pred_id = db.Column(
        db.Integer,
        index = False,
        nullable = True
    )

    created_at = db.Column(
        db.DateTime(timezone=True),
        index=False,
        unique=False,
        nullable=True,
        default=func.clock_timestamp()
    )

    updated_at = db.Column(
        db.DateTime(timezone=True),
        index=False,
        unique=False,
        nullable=True,
        default=func.clock_timestamp(),
        onupdate=func.clock_timestamp()
    )

    def as_dict(self):

        import datetime
        
        result = {}
        for c in self.__table__.columns:
            value = getattr(self, c.name)
            if isinstance(value,datetime.datetime):
                result[c.name] = value.isoformat()
            else:
                result[c.name] = value

        return result
    
    def __repr__(self):
        return f"ST{self.station_id} Bin {self.bin_no} {self.type} {self.status}"
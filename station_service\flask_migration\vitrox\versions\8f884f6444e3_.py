"""empty message

Revision ID: 8f884f6444e3
Revises: 
Create Date: 2024-04-30 16:30:29.453713

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8f884f6444e3'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('stations', schema=None) as batch_op:
        batch_op.add_column(sa.Column('service_door', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('is_maintenance', sa.<PERSON>(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('stations', schema=None) as batch_op:
        batch_op.drop_column('is_maintenance')
        batch_op.drop_column('service_door')

    # ### end Alembic commands ###

import datetime
import threading 
import asyncio

from termcolor import colored
from contextlib import suppress
from dataclasses import dataclass
from asyncio import Abstract<PERSON>vent<PERSON>oop
from typing import Set,Optional, Tuple, Dict, List
from pingspace_python_packages.rabbitmq import RabbitMQ as _RMQ, TeamsNotification

from config import Config
from .. import database as db,models as model,common as common,server as asrs_server,enum_collections as ec
from ..blueprints.route.client import MedHttpClient
from ..error_handling import asrs_eh

asrs_logic_log = common.get_logger_console('asrs_logic')
asrs_handler_log = common.get_logger_console('asrs_handler')

asrs_in_recovery : Set[int] = set()
asrs_in_use : Set[int] = set()

@common.decorateAllFunctionInClass(common.log_and_suppress_error(asrs_logic_log))
class ASRSLogic:
    
    @staticmethod
    def map_asrs_id(y : int)->int:
        '''Each ASRS serves 2 y axis, So if we half + 0.5 we can get asrs ID'''
        asrs_id = int(y/2 + 0.5)
        return asrs_id

    @staticmethod
    def bay_level_mapper(x :int, y :int, z:int)->Tuple[int,int,str]:
        bay = z
        level = y
        if (x % 2) == 0:
            # even numbers represent left side of ASRS
            side = 'L'
        else:
            side = 'R'
        return bay, level, side

    @staticmethod
    def get_pc_id( asrs_id: str, breakbulb_pc : bool = True)->int:
        if breakbulb_pc:
            if asrs_id in ['1','2']:
                return int(asrs_id)
            else: 
                return 0 
        else :
            return 3
      
@common.decorateAllFunctionInClass(common.log_and_suppress_error(asrs_handler_log))
class MsgQueue(asyncio.Queue):
        
    def __init__(self):
        super().__init__()
        
    def put(self, msg:str):
        self._loop.call_soon_threadsafe(
            super().put_nowait,msg
        )
        
    def get(self)->str:
        return super().get()
    
class MessageHandler:
    
    @classmethod
    @common.async_log_and_suppress_error(asrs_handler_log)
    async def process_msg(cls,msg:str):
        msg = msg.replace(';', '')
        splited_msg = msg.split(',')
        
        # get module and plc id
        if splited_msg[0] == 'ACK':
            index = 1
            plc_id = int(splited_msg[2])
        else:
            index = 0
            plc_id = int(splited_msg[1])

        if 'PC' in splited_msg[index]:
            module = ec.ModuleCode.PalletConveyor.value
        else:
            module = ec.ModuleCode.ASRS.value

        if (splited_msg[2] == 'Pairing'):
            # When ASRS pairing, inform mediator, so they can reset any e-stop notification at fe.
            await MedHttpClient.reset_error(station_id=int(splited_msg[1]), domain=splited_msg[index] )

            # Request station status
            if module == ec.ModuleCode.ASRS.value:
                requestStatus = f"{module},{plc_id},S;"
                asrs_server.send_asrs_msg(f'{module}{plc_id}', requestStatus)

        elif (splited_msg[2] == 'S'):
            status_code  = int(splited_msg[4])

            machine_status_warning = list(ec.MachineStatusWarning)
            machine_status_action = list(ec.MachineStatusAction)

            if status_code in [ec.MachineStatus.RUNNING.value,ec.MachineStatus.IDLE.value]: #1 and 2 
                # need to clear error
                await MedHttpClient.reset_error(station_id=plc_id, domain=module)
            else:
                # need to show error 
                jsonPayload = common.JsonPayload.informErrorPayload(
                    msg,
                    ec.AsrsRecoveryTriggers.ERR3.value,
                    machine_status_warning[status_code].value,
                    plc_id,
                    domain=module,
                    action=machine_status_action[status_code].value
                )
            
                await MedHttpClient.update_error(data=jsonPayload)                   
        
        elif ('ERR' in msg):                        
            msg_to_reply = f'ACK,{msg};'
            asrs_server.send_asrs_msg(f'{splited_msg[0]}{splited_msg[1]}', msg_to_reply)
            await asrs_eh.handle_asrs_error_code(msg)

        elif (splited_msg[0] == 'ACK'):
            job = db.as_job_db_func.ack_job(int(splited_msg[4]))
            if splited_msg[3] == 'P':
                if int(splited_msg[2]) not in asrs_in_use:
                    use_asrs(int(splited_msg[2]))

        elif (splited_msg[2] in ['P', 'D', 'R']):
            
            # PC lost data
            if splited_msg[3] =='null' and splited_msg[2] == 'R':
                asrs_handler_log.warning(colored(f'PC has lost data, received: {msg}','light_yellow'))
                msg_to_reply = f'ACK,{msg};'
                asrs_server.send_asrs_msg(f'{splited_msg[0]}{splited_msg[1]}', msg_to_reply)
                eh_job_id = db.as_job_db_func.create_pc_lost_data_job()
                if eh_job_id is None:
                    asrs_handler_log.warning(colored(f'PC EH job already exist. Ignore creation','light_yellow'))
                else:                                
                    asrs_eh.process_eh_job(int(eh_job_id),int(splited_msg[1]))
                    
            elif splited_msg[3] == '' or splited_msg == 'null':
                # TODO can consider add a recover flow for asrs job done message
                asrs_handler_log.error(colored("Receive P/D/R message without job id, ignore the message",'light_red'))
            else:
                # valid_pallet = True
                asrs_id = splited_msg[1]
                job_id = int(splited_msg[3])
                job = db.as_job_db_func.get_job_by_id(job_id)

                if job:
                    msg_to_reply = f'ACK,{msg};'
                    asrs_server.send_asrs_msg(f'{splited_msg[0]}{splited_msg[1]}', msg_to_reply)
                    
                    if splited_msg[2] == 'P':  # AS,1,P,1,PL0001;
                        if job.job_type == ec.AsrsJobType.PC_LOST_DATA.value:
                            '''Function will return True if it is a get order to station. This one need extra handle. If it is a put job, sync up and resume.
                                If it is get order, trigger PLC error flow to request drop location and spawn drop job to store back into rack. Then cancel order.'''
                            is_get_order, order_id = db.as_job_db_func.sync_pc_lost_data_job(splited_msg[4],job.id) 

                            if is_get_order:
                                order = db.as_order_db_func.get_order(order_id, True)
                                await asrs_eh.recovery_pick(splited_msg[4],order.id, order.asrs_id,True)
                                    
                                    
                        if int(asrs_id) not in asrs_in_use:
                            use_asrs(int(asrs_id))
                        # elif Config.SKIP_ASRS_VALIDATION is False:
                        #     if job.pallet_id == splited_msg[4]:
                        #         valid_pallet = True
                        #     # wrong pallet id
                        #     else:
                        #         recovery = True
                        #         valid_pallet = False
                        #         order = get_asrs_order(job.order_id, recovery)
                        #         asrs_handler_log.warning(yellowBright(f'Pallet id from pick done mismatched. Original: {job.pallet_id}, plc on hand:{splited_msg[4]}. Trigger Recovery.'))
                        #         if f'{ec.ModuleCode.ASRS.value}{asrs_id}' not in asrs_in_recovery:
                        #             asrs_in_recovery.add(f'{ec.ModuleCode.ASRS.value}{asrs_id}')
                        #             # Pass false in to signify hwx self trigger wrong pallet, not plc throw error.
                        #             recovery_thread = threading.Thread(target=recovery_pick, args=(splited_msg[4], order, False), name = "recovery_pick")
                        #             recovery_thread.start()
                        #         else:
                        #             asrs_handler_log.warning(yellowBright(f'ASRS {asrs_id} already in recovery.'))

                    # if valid_pallet is True:
                    #     complete_asrs_job(splited_msg[3])
                        
                    if splited_msg[2] == 'D':

                        if job.job_type == ec.AsrsJobType.RECOVERY_DROP.value:
                            await MedHttpClient.cancel_job(job.pallet_id)

                            if job.triggered_by == ec.AsrsRecoveryTriggers.WRONG_PALLET.value:
                                db.as_job_db_func.redo_asrs_pick(job.order_id)
                            elif job.triggered_by == ec.AsrsRecoveryTriggers.ERR2.value:
                                db.as_job_db_func.set_job_error_for_order(job.order_id)
                            asrs_recovery_done(int(asrs_id))

                        free_asrs(int(splited_msg[1]))
                    
                    db.as_job_db_func.complete_job(int(splited_msg[3]))

                else:
                    # no job found
                    asrs_handler_log.error(colored(f"No job found for job id : {job_id}, please take a look on this issue. Will still reply ack to avoid spamming.",'light_red'))
                    msg_to_reply = f'ACK,{msg};'
                    asrs_server.send_asrs_msg(f'{splited_msg[0]}{splited_msg[1]}', msg_to_reply)


@dataclass
class IdleJob:
    last_noti_time : datetime.datetime
    interval : datetime.timedelta

@dataclass
class IdleJobLoop:
    
    event_loop : Optional[AbstractEventLoop]
    future = None
    recorded_idle_jobs: Dict[int, IdleJob] = None # map job id : [last_emit_time, interval time]
    
    def __post_init__(self):
        self.future = self.event_loop.create_future()
        self.recorded_idle_jobs= dict()
        
    @common.async_log_and_suppress_error(asrs_handler_log)
    async def run(self):
        while True:
            with suppress(asyncio.TimeoutError):
                idle_jobs = db.as_job_db_func.check_response_interval() # no response for job done message for 1 minutes
                idle_jobs_id : List[int]= []
                for job in idle_jobs:
                    job : model.Asrs_job
                    idle_jobs_id.append(job.id)
                    order = db.as_order_db_func.get_order(job.order_id, True)
                    # get group
                    if job.job_type in [ec.AsrsJobType.DROP.value,ec.AsrsJobType.PICK.value]:
                        module = "ASRS"
                    elif job.job_type == ec.AsrsJobType.MOVE.value:
                        module = "PC"
                    else:
                        module = "EH"
                    
                    interval : datetime.timedelta  = self.get_coressponsing_interval_time(job.updated_at)
                    if job.id in self.recorded_idle_jobs:
                        # update dict for the interval time
                        self.recorded_idle_jobs[job.id] = IdleJob(self.recorded_idle_jobs[job.id].last_noti_time,interval)
                        if (datetime.datetime.now() - self.recorded_idle_jobs[job.id].last_noti_time) > self.recorded_idle_jobs[job.id].interval:
                            common.saveNotification(title = f"ASRS Station Job Error",
                                                    module=ec.ModuleCode.ASRS.value,
                                                    Device = f'{module}{order.asrs_id}',
                                                    Job_Status = f"Job ID {job.id} of type {job.job_type} {ec.Notify.RESPONSE_INTERVAL.value}"
                                                    )
                            self.recorded_idle_jobs[job.id] = IdleJob(datetime.datetime.now(),interval)
                    else:
                        common.saveNotification(title = f"ASRS Station Job Error",
                                                module=ec.ModuleCode.ASRS.value,
                                                Device = f'{module}{order.asrs_id}',
                                                Job_Status = f"Job ID {job.id} of type {job.job_type} {ec.Notify.RESPONSE_INTERVAL.value}"
                                                )
                        self.recorded_idle_jobs[job.id] = IdleJob(datetime.datetime.now(),interval)
                    
                idle_client = db.as_job_db_func.check_ack_interval() # no reply ack for 1 minutes
                if idle_client: 
                    common.saveNotification(title = f"ASRS Station Idle",
                                                module=ec.ModuleCode.ASRS.value,
                                                Device = f'AS/PC',
                                                Message = ec.Notify.ACK_INTERVAL.value
                                                )
                    
                self.removed_cleared_job_id(idle_jobs_id)
                
                result = await asyncio.wait_for(asyncio.shield(self.future), 60)  

    @common.log_and_suppress_error(asrs_handler_log)
    def removed_cleared_job_id(self,idle_job_id:List[int]):
        for job_id in list(self.recorded_idle_jobs.keys()):
            if job_id not in idle_job_id:
                del self.recorded_idle_jobs[job_id]
    
    @common.log_and_suppress_error(asrs_handler_log)
    def get_coressponsing_interval_time(self,job_update_time:datetime.datetime)->datetime.timedelta:
        # 1-5 minutes -> every 1 minutes
        # 5-10 minutes -> every 3 minutes
        # 10-20 minutes -> every 5 minutes
        # 20-30 minutes -> every 10 minutes max
        current_time = datetime.datetime.now()
        if current_time - job_update_time < datetime.timedelta(minutes=5) :
            return datetime.timedelta(minutes =1)
        elif current_time - job_update_time < datetime.timedelta(minutes=10) :
            return datetime.timedelta(minutes =3)
        elif current_time - job_update_time < datetime.timedelta(minutes=20) :
            return datetime.timedelta(minutes =5)
        else:
            return datetime.timedelta(minutes =10)

class ASRSService:
    
    incoming_msg_queue : MsgQueue = None
    event_loop : Optional[AbstractEventLoop]= None
    message_handler = MessageHandler()
    idle_job_loop :IdleJobLoop = None

    
    @classmethod
    @common.log_and_suppress_error(asrs_handler_log)
    def run(cls):
        threading.current_thread().setName('asrs_async') 
        
        cls.event_loop = cls.get_or_create_eventloop()
        cls.incoming_msg_queue = MsgQueue()
        cls.idle_job_loop = IdleJobLoop(cls.event_loop)

        cls.start_server()
        cls.set_up_rmq()
        cls.run_asrs_msg_loop()
        cls.run_idle_job_loop()
        
        
        cls.event_loop.create_task(asrs_server.resume_asrs(),name= 'resume_asrs')
        cls.event_loop.run_forever()

    @classmethod
    def get_or_create_eventloop(cls)->Optional[AbstractEventLoop]:
        try:
            return asyncio.get_event_loop()
        except RuntimeError as ex:
            if "There is no current event loop in thread" in str(ex):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                return asyncio.get_event_loop()                   
    
    @classmethod
    @common.log_and_suppress_error(asrs_handler_log)
    def add_message(cls,msg:str):
        cls.incoming_msg_queue.put(msg)
    
    @classmethod 
    @common.log_and_suppress_error(asrs_handler_log)
    def start_server(cls):
        
        cls.event_loop.create_task(asrs_server.ASRSServer.start_server(),name= 'asrs_server')
    
    @classmethod
    @common.log_and_suppress_error(asrs_handler_log)
    def set_up_rmq(cls):

        def _init_queue(name: str, queue_size: int = 500, **kwargs):
            if _RMQ.queues.get(name):
                return

            _RMQ.init_background_queue(name, queue_size)
            _RMQ.declare_queue_robust(name, **kwargs)
        if Config.ASRS_RMQ:
            rmq_host, rmq_port, rmq_user, rmq_password = Config.get_asrs_rmq_con()

            _RMQ.initialize(
                rmq_user,
                rmq_password,
                rmq_host,
                rmq_port,
                '/',
                'ASRS',
                cls.event_loop)
            _RMQ.connect()

            if Config.WEBHOOK_Q: _init_queue(
                name = TeamsNotification.name,
                durable = True,
                arguments = TeamsNotification.arguments)
    
    @classmethod
    @common.log_and_suppress_error(asrs_handler_log)
    def run_asrs_msg_loop(cls):
        cls.event_loop.create_task(cls.asrs_msg_loop(),name= 'asrs_msg_loop')
    
    @classmethod
    @common.log_and_suppress_error(asrs_handler_log)
    def run_idle_job_loop(cls):
        if Config.WEBHOOK_Q:
            cls.event_loop.create_task(cls.idle_job_loop.run(),name= 'asrs_idle_job_loop')
    
    @classmethod
    @common.async_log_and_suppress_error(asrs_handler_log)
    async def asrs_msg_loop(cls):
        while True:
            msg = await cls.incoming_msg_queue.get()
            await cls.message_handler.process_msg(msg)
            
            
 
    
@dataclass
class OrderHandler:
    
    future = None
    event_loop = None
    order_id:int
    asrs_order_id : int
    
    def __post_init__(self):
        self.event_loop : Optional[AbstractEventLoop] = ASRSService.event_loop
        self.future = self.event_loop.create_future()
    
    @common.log_and_suppress_error(asrs_handler_log)
    def start_order(self):
        db.as_order_db_func.processing_order(self.asrs_order_id)
        self.event_loop.call_soon(
            self.event_loop.create_task,
            self.retry()
        )
        
    @common.log_and_suppress_error(asrs_handler_log)
    def start_order_ts(self):
        db.as_order_db_func.processing_order(self.asrs_order_id)
        self.event_loop.call_soon_threadsafe(
            self.event_loop.create_task,
            self.retry()
        )
    
    @common.log_and_suppress_error(asrs_handler_log)
    def complete_order(self):
        self.future.set_result(True)
    
    @common.async_log_and_suppress_error(asrs_handler_log)   
    async def retry(self):
        while True:
            with suppress(asyncio.TimeoutError):
                msg = self.message_to_broadcast(self.asrs_order_id)
                if msg is None:
                    if db.as_job_db_func.check_all_job_completed_for_order(self.asrs_order_id):
                        self.complete_order()
                else:
                    asrs_server.send_asrs_msg(msg[0],msg[1])
                result = await asyncio.wait_for(asyncio.shield(self.future), 1)  
                
                if result:
                    jobs = db.as_job_db_func.get_jobs_by_order_id(self.asrs_order_id)
                    order = db.as_order_db_func.complete_order(self.asrs_order_id)
                    # if jobs[0].job_status != ec.OrderStatus.CRITICAL_ERROR.value: # and asrs_routes.starting_fresh is False:  # Only broadcast complete to Mediator if the jobs are not in error.
                    if not any(job.job_status == ec.OrderStatus.CRITICAL_ERROR.value for job in jobs): #Only broadcast when there is no critical error or all jobs are completed
                        await MedHttpClient.emit_asrs_order_complete(order.pallet_id, order.station_id, order.order_id, order.method)
                    break
    
    @common.log_and_suppress_error(asrs_handler_log)                   
    def message_to_broadcast(self,order_id:int)->Tuple[str,str]:
        order : model.Asrs_order = db.as_order_db_func.get_order(order_id, True)
        jobs = db.as_job_db_func.get_active_jobs_by_order_id(order.id)
        
        for job in jobs:
            job : model.Asrs_job

            if not  db.as_job_db_func.check_pred_job_completed(job.pred_id):
                continue
            
            if job.job_status == ec.OrderStatus.AVAILABLE.value:  db.as_job_db_func.processing_job(job.id) # set job processing 
            
            if job.ack is False:
                msg = job.msg
                split_msg = msg.split(',')
                
                if job.job_type in [ec.AsrsJobType.MOVE.value, ec.AsrsJobType.DROP.value]:
                    return f'{split_msg[0]}{split_msg[1]}',msg
                elif job.job_type == ec.AsrsJobType.PICK.value:
                    if int(order.asrs_id) in asrs_in_use:
                        asrs_handler_log.warning(colored(f'There is more than 1 order processed by ASRS {order.asrs_id}. Waiting for it to complete first.','light_yellow'))
                        return None
                    return f'{split_msg[0]}{split_msg[1]}',msg
        return None
    
@dataclass
class ErrorJobHandler:
    
    future = None
    event_loop = None
    error_job_id : int
    asrs_id : int
    
    def __post_init__(self):
        self.event_loop : Optional[AbstractEventLoop] = ASRSService.event_loop
        self.future = self.event_loop.create_future()
    
    @common.log_and_suppress_error(asrs_handler_log)
    def start_error_job(self):
        db.as_job_db_func.processing_job(self.error_job_id)
        self.event_loop.call_soon(
            self.event_loop.create_task,
            self.retry()
        )
    
    @common.log_and_suppress_error(asrs_handler_log)
    def complete_error_job(self):
        self.future.set_result(True)
            
    @common.async_log_and_suppress_error(asrs_handler_log)   
    async def retry(self):
        while True:
            with suppress(asyncio.TimeoutError):
                error_job : model.Asrs_job = db.as_job_db_func.get_job_by_id(self.error_job_id)
                if error_job:
                    if error_job.ack or error_job.job_status == ec.OrderStatus.COMPLETED.value:
                        self.complete_error_job()
                    else:
                        if error_job.job_type == ec.AsrsJobType.PC_LOST_DATA.value and self.asrs_id in asrs_in_use:
                            asrs_handler_log.warning(colored(f'There is more than 1 order processed by ASRS {self.asrs_id}. Waiting for it to complete first. (EH)','light_yellow'))
                        else:
                            splited_err_msg = error_job.msg.split(',')
                            asrs_server.send_asrs_msg(f'{ec.ModuleCode.ASRS.value}{self.asrs_id}',error_job.msg)
                else:
                    asrs_handler_log.error(colored(f'No job found for job id {self.error_job_id}','light_red'))
                result = await asyncio.wait_for(asyncio.shield(self.future), 3)  
                
                if result:
                    break
        

    
def free_asrs(id : int = None ):
    try:
        global asrs_in_use
        if id == None:
            asrs_in_use.clear()
        else:
            asrs_in_use.remove(id)
            
        asrs_handler_log.info(colored(f"ASRS {id} is free ",'light_green'))
    except KeyError as e:
        asrs_handler_log.error(colored(f'ASRS {id} is not in use','light_red'))
    except Exception as e:
        asrs_handler_log.error(colored(f'free_asrs error. Exception thrown:{e}','light_red'))

def use_asrs(id: int):
    try:
        global asrs_in_use
        if id in asrs_in_use:
            raise KeyError()
        
        asrs_in_use.add(id)
            
        asrs_handler_log.info(colored(f"ASRS {id} is in use ",'light_green'))
    except KeyError as e:
        asrs_handler_log.error(colored(f'ASRS {id} is already in use','light_red'))
    except Exception as e:
        asrs_handler_log.error(colored(f'use_asrs error. Exception thrown:{e}','light_red'))

def asrs_recovery(id :int ):
    try:
        global asrs_in_recovery
        if id in asrs_in_recovery:
            raise KeyError()
        
        asrs_in_recovery.add(id)
            
        asrs_handler_log.info(colored(f"ASRS {id} is in recovery ",'light_green'))
    except KeyError as e:
        asrs_handler_log.error(colored(f'ASRS {id} is already in recovery','light_red'))
    except Exception as e:
        asrs_handler_log.error(colored(f'asrs_recovery error. Exception thrown:{e}','light_red'))
        
def asrs_recovery_done(id:int = None):
    try:
        global asrs_in_recovery
        if id == None:
            asrs_in_recovery.clear()
        else:
            asrs_in_recovery.remove(id)
            
        asrs_handler_log.info(colored(f"ASRS {id} done recovery ",'light_green'))
    except KeyError as e:
        asrs_handler_log.error(colored(f'ASRS {id} is not in recovery','light_red'))
    except Exception as e:
        asrs_handler_log.error(colored(f'asrs_recovery_done error. Exception thrown:{e}','light_red'))



from typing import Dict, List
from dataclasses import dataclass

from .. import db


@dataclass
class Stations(db.Model):
    id: int
    code: int
    matrix_code: int
    zone: str
    type: str
    host: str
    port: int
    cell: int 
    mode: str
    queue : str
    plc_status: str
    rotation: str
    adjacent: int
    inner_drop: int
    inner_pick: int
    outer_drop: int
    outer_pick: int
    worker: int
    bin_at_worker: str
    gw_operating : bool
    service_door : int
    is_connected: bool
    is_active: bool
    is_maintenance: bool
    is_overweight: bool
    is_deleted: bool
    is_recovery: bool
    created_at: str
    updated_at: str

    __tablename__ = 'stations'
    id = db.Column(
        db.Integer,
        primary_key=True)
    
    code = db.Column(
        db.Integer,
        index=False,
        unique=True,
        nullable=True)
   
    matrix_code = db.Column(
        db.Integer)

    host = db.Column(
        db.String(128))
    
    port = db.Column(
        db.Integer,
        index=False,
        unique=False,
        nullable=True,
        default=1000
        )

    zone = db.Column(
        db.String(20),
        index=False,
        unique=False,
        nullable=False)

    type = db.Column(
        db.String(20),
        index=False,
        unique=False,
        nullable=True)
    
    cell = db.Column(
        db.Integer,
        index=False,
        unique=False,
        nullable=False 
    )

    mode = db.Column(
        db.String(20),
        index=False,
        unique=False,
        nullable=False,
        default="NORMAL"
    )

    queue = db.Column(
        db.Text, 
        default = "[]",
        unique = False,
        index = False,
        nullable=True
    )

    plc_status = db.Column(
        db.String(128),
        index = False,
        nullable=True
    )

    rotation = db.Column(
        db.String(10),
        index=False,
        unique=False,
        nullable=False)

    adjacent = db.Column(
        db.Integer,
        index=False,
        unique=False,
        nullable=True
    )


    inner_drop = db.Column(
        db.Integer,
        unique=False,
        index=False,
        nullable=False
    )

    inner_pick = db.Column(
        db.Integer,
        unique=False,
        index=False,
        nullable=False
    )

    outer_drop = db.Column(
        db.Integer,
        unique=False,
        index=False,
        nullable=True
    )

    outer_pick = db.Column(
        db.Integer,
        unique=False,
        index=False,
        nullable=True
    )

    worker = db.Column(
        db.Integer,
        unique=False,
        index=False,
        nullable=False
    )

    bin_at_worker = db.Column(
        db.String(128),
    )

    service_door = db.Column(
        db.Integer,
        index=False,
        unique=False,
        nullable=True 
    )

    gw_operating = db.Column(
        db.Boolean,
        index=False,
        unique=False,
        default = False
    )

    is_connected = db.Column(
        db.Boolean, 
        default=False)

    
    is_active = db.Column(
        db.Boolean,
        index=False,
        unique=False,
        nullable=True,
        default = False
    )

    is_maintenance = db.Column(
        db.Boolean,
        index=False,
        unique=False,
        nullable=True,
        default = False
    )
    
    is_overweight = db.Column(
        db.Boolean,
        index=False,
        unique=False,
        nullable=True,
        default = False
    )
    
    is_recovery = db.Column(
        db.Boolean,
        index = False,
        nullable = True,
        default = False
    )

    is_deleted = db.Column(
        db.Boolean,
        index = False,
        nullable = True,
        default = False
    )

    created_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

    updated_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

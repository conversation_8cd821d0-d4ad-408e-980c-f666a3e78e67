from sqlalchemy import func

from .. import db

class StationOrder(db.Model):
    id:int
    tc_order_id:int 
    type:str 
    station_code:int
    storage_code:str
    status:str
    is_processed:bool
    created_at:str
    updated_at:str
    processed_at:str
    
    __tablename__ = 'station_order'

    id = db.Column(
        db.Integer,
        primary_key=True)
    
    tc_order_id = db.Column(
        db.Integer,
        index = False,
        nullable=True
    )

    type = db.Column(
        db.String(128),
        index=False,
        nullable=False,
    )
    
    station_code = db.Column(
        db.Integer,
        index=False,
    )

    storage_code = db.Column(
        db.String(128),
        index=False,
    )

    status = db.Column(
        db.String(128),
        index=False,
        unique=False
    )

    is_processed = db.Column(
        db.Boolean, 
        default=False
    )

    created_at = db.Column(
        db.DateTime(timezone=True),
        index=False,
        unique=False,
        nullable=True,
        default=func.clock_timestamp()
    )

    updated_at = db.Column(
        db.DateTime(timezone=True),
        index=False,
        unique=False,
        nullable=True,
        default=func.clock_timestamp(),
        onupdate=func.clock_timestamp()
    )

    processed_at = db.Column(
        db.DateTime(timezone=True),
        index=False,
        unique=False,
        nullable=True
    )
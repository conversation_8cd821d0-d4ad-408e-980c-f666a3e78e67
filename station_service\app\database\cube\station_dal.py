import datetime
import json

from typing import List

from ... import app,DBMonitor,db
from ... import common as common,models as model,enum_collections as ec

db_log = common.LogManager('cube_db_error',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_log))
class StationSQLAlchemyQueries:
    
    @DBMonitor.retry_session
    def bulk_seed_station(self, station_list: List[tuple]) -> List[model.Stations]:
        """
        Bulk seed stations into database
        
        Args:
            station_list (List[tuple]): List of tuples containing (station_code, matrix_code)
        
        Returns:
            List[model.Stations]: List of created station objects
        """
        with app.app_context():
            curr_session = db.session
            
            # Prepare bulk insert data
            stations_to_create = []
            empty_list = ["","",""]
            str_queue = json.dumps(empty_list)
            
            for station_code, matrix_code in station_list:
                station = model.Stations(
                    code=station_code,
                    matrix_code=matrix_code,
                    zone="C",
                    type=ec.CubeStationType.I.value,
                    rotation="",
                    plc_status="||",
                    inner_drop=1,
                    inner_pick=1,
                    worker=0,
                    cell=3,
                    queue=str_queue,
                    is_active=False,
                    created_at=datetime.datetime.now(),
                    updated_at=datetime.datetime.now()
                )
                stations_to_create.append(station)
            
            # Bulk insert all stations
            curr_session.bulk_save_objects(stations_to_create)
            curr_session.commit()
            
            return stations_to_create
    
    @DBMonitor.retry_session
    def get_all_station_list(self)->List[model.Stations]:
        with app.app_context():
            station_details = db.session.query(model.Stations).filter(model.Stations.is_deleted != True).order_by(model.Stations.code).all()
        return station_details
    
    @DBMonitor.retry_session
    def reset_active_stations(self):
        with app.app_context():
            curr_session = db.session
            curr_session.query(model.Stations).update({"is_active": False,
                                                        "is_connected":False})
            curr_session.commit()
    
    @DBMonitor.retry_session
    def set_active_stations(self):
        with app.app_context():
            curr_session = db.session
            curr_session.query(model.Stations).update({"is_active": True,
                                                        "is_connected":True})
            curr_session.commit()
    
    @DBMonitor.retry_session
    def update_station(self,station_code:int,update:dict)->model.Stations:
        with app.app_context():
            curr_session = db.session
            station:model.Stations= curr_session.query(model.Stations).filter(model.Stations.code == station_code).first()
            if station:
                for k,v in update.items():
                    station.__setattr__(k,v)
                station.updated_at = datetime.datetime.now()
                curr_session.commit()
            else:
                db_log.error(f'No station with station code {station_code} found')
                return None
    
    @DBMonitor.retry_session
    def update_list_of_station(self,st_list:List[int],maint_status:bool)->model.Stations:
        with app.app_context():
            curr_session = db.session
            stations_to_update:List[model.Stations]= curr_session.query(model.Stations).filter(model.Stations.code.in_(st_list)).all()

            for st in stations_to_update:
                st.is_maintenance = maint_status
                st.updated_at = datetime.datetime.now()
            
            curr_session.commit()
    

    # def create_station(self,code,zone,type,rotation,adjacent, inner_drop,inner_pick, outer_drop,outer_pick,worker):
    #     curr_session = db.session
    #     station = model.Stations(
    #         code = code,
    #         zone = zone,
    #         type = type,
    #         rotation = rotation,
    #         adjacent = adjacent,
    #         inner_drop = inner_drop,
    #         inner_pick = inner_pick,
    #         outer_drop = outer_drop,
    #         outer_pick = outer_pick,
    #         worker = worker
    #     )
    #     curr_session.add(station)
    #     curr_session.commit()

    # def delete_station(station_id):
    #     try:
    #         start_time = time.time()
    #         curr_session = db.session
            
    #         station = curr_session.query(model.Stations).filter_by(id = station_id).first()
    #         station.is_deleted = True
    #         curr_session.commit()
    #         end_time = time.time()
    #         metrics.register_func_average_time(start_time,end_time,sys._getframe().f_code.co_name)
    #     except Exception as e:
    #         db_log.error(redBright(f'delete_station error. Exception: {e}'))
    #         metrics.register_func_error(sys._getframe().f_code.co_name)

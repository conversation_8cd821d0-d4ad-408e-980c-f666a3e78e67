import json

from time import time
from http import HTTPStatus
from flask import Request, Response
from typing import Optional, TypedDict

from . import common as common
from .auth import AuthServices

http_log = common.LogManager('cube_http_handler',centralize_logging=True)


class RequestData(TypedDict):
    method: str
    path: str
    json: dict
    args: dict
    headers: dict
    ip:str
    port:int
    Authorization: Optional[int]


class ResponseData(TypedDict):
    status_code: int
    headers: dict
    data: dict
    json: dict


class LogData(TypedDict):
    request: RequestData
    response: ResponseData
    duration_ms: int


class LogHttp:

    request_headers = ['X-Correlation-ID', 'X-Idempotency-ID', 'X-Request-ID']

    @classmethod
    def log_at_request(cls, request: Request, auth: Optional[int]):
        request.start_time = time()
        request.auth = auth

    @classmethod
    def log_at_response(cls, request: Request, response: Response):
        if response.status_code == HTTPStatus.OK.value:
            c_log = http_log.info
            level = 'INFO'
        else:
            c_log = http_log.error
            level = 'ERROR'

        duration_ms = int((time() - request.start_time) * 1000)
        data = LogData(
            request = RequestData(
                method = request.method,
                path = request.path,
                json = cls.empty_byte(request.data) if request.is_json else None,
                args = dict(request.args),
                headers = {
                    key: request.headers.get(key)          
                    for key in cls.request_headers
                },
                ip=request.remote_addr,
                port=request.environ.get('REMOTE_PORT'),
                Authorization = request.auth
            ),
            response = ResponseData(
                status_code = response.status_code,
                headers = dict(response.headers),
                data = response.get_data().decode('utf-8'),
                json = response.json
            ),
            duration_ms = duration_ms
        )
        username = AuthServices.get_username(request.auth)
        print(common.formatString(level,f"{request.remote_addr} {request.environ.get('REMOTE_PORT'),} {request.method} {request.path} {response.status_code} {duration_ms}ms {username}"))
        try:
            json.dumps(data)
            c_log(f"{data} {request.environ.get('HTTP_ORIGIN', '')} {request.path} {request.method} {request.headers.get('X-Correlation-ID', '-')}")
        except (TypeError, ValueError) as e:
            print(common.formatString('ERROR', f"{cls.__name__} Serialization error {e.args}"))

    @common.log_and_suppress_error(http_log)
    def empty_byte(value):
        if isinstance(value, bytes) and value == b'' :
            return None
        if isinstance(value, bytes) and value != b'':
            return json.loads(value.decode('utf-8'))
        return value

from .. import adapter as adp,database as db
from ..runtime import runtime

def station_initialization():
    """
    To initialize the runtime station from db data according to their type 

    Run when program start running and creating app
    """
    st_list = db.st_db_func.get_all_station_list_db()

    runtime.clear_runtime_st()
    
    for st in st_list :
        runtime.add_runtime_st(adp.StationAdapter(st))

    event_list = db.st_event_db_func.get_all_active_event()

    for event in event_list:
        adp.EventAdapter(event)

    db.st_db_func.reset_active_stations()



from ... import common as common
from .asrs_station_dal import AsrsStationSQLAlchemyQueries

db_func_log = common.get_logger_console('asrs_db_func')

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_func_log))
class AsrsStationDBFunc:
    
    asrs_station_dal = AsrsStationSQLAlchemyQueries()
    
    def get_station_type_by_id(self,id:int)->str:
        station = self.asrs_station_dal.get_station_by_id(id)
        return station.type
        
    
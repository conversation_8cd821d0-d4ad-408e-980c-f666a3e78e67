import asyncio 
import socketio

from aiohttp import web
from collections import deque
from termcolor import colored
from threading import current_thread
from asyncio import Abstract<PERSON><PERSON><PERSON>oop
from typing import Deque, Optional,Coroutine

from config import Config
from ... import common as common

ws_server_log = common.LogManager('cube_ws_server',display_console=True,centralize_logging=True)

event_queue: Deque = deque()
iso_event_que : Deque = deque()


class WSConnetion:
    def __init__(self, host: str, port: int) -> None:
        self.id = hash((host, port))
        self.host = host
        self.port = port


    def __repr__(self) -> str:
        return f'{self.host}:{self.port}'
    
    def as_dict(self) -> dict:
        return dict(
            id= self.id,
            host= self.host,
            port= self.port
            )


sio = socketio.AsyncServer(cors_allowed_origins='*')

@common.decorateAllFunctionInClass(common.log_and_suppress_error(ws_server_log))
class SocketIOServer:
    """
    A scoketio server class that using aiohttp to perform asyncio and AsyncServer
    """
    loop = None

    @classmethod
    def run(cls):
        """
        A thread to handle socketio server and emit event
        Using aiohttp to run a server with eventloop so able to work with AsyncServer
        """
        thread = current_thread()
        thread.setName('socketio_server')

        cls.loop = cls.get_or_create_eventloop()

        app = web.Application(loop = cls.loop) 
        sio.attach(app)

        cls.start_handler()

        ws_conn:WSConnetion = Config.get_cube_ws_conn()
        ws_server_log.info(f"Starting up WS server at \tip: {ws_conn.host} port: {ws_conn.port}",color='light_green')
        web.run_app(app,host = ws_conn.host, port=ws_conn.port, loop = cls.loop, print=None)
    
    @classmethod
    def create_task(cls, coro: Coroutine, name: str = None):

        def _create_task():
            return cls.loop.create_task(coro,
                name = name or coro.__qualname__
            )
        return cls.loop.call_soon_threadsafe(
            _create_task
        )

    @classmethod
    def get_or_create_eventloop(cls)->Optional[AbstractEventLoop]:
        try:
            return asyncio.get_event_loop()
        except RuntimeError as ex:
            if "There is no current event loop in thread" in str(ex):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                return asyncio.get_event_loop() 

    @classmethod
    def start_handler(cls):
        from ...communications_provider.socketio.cube import SioBroadcastHandler

        SioBroadcastHandler.initialization(cls.loop)

        cls.create_task(SioBroadcastHandler.loop())

    @classmethod
    def emit_ws_event(cls,event_name:str, data, room = None, namespace = None, callback = None, entity:str = None):
        """
        Use this function to emit an event 

        Args:
            event_name (str): name of the event handler
            data (_type_): the data to bed sent
            room (_type_, optional): the sid to send the event to. Defaults to None.
            namespace (_type_, optional): the namespace of the event. Defaults to None.
            callback (_type_, optional): the function to run when there is a return/ACK after emitting the event . Defaults to None.
            entity (str, optional): the entity of the socketio client [TC_A, TC_B, TC_C,SM, MED_1....]. Defaults to None.

        Will not log for weight_changed event, will pollute the log 
        """
        cls.create_task(sio.emit(event_name,
                                 data=data,
                                 room=room,
                                 namespace=namespace,
                                 callback=callback
                                 ))
        
        if event_name != 'weight_changed': #do not log weight-changed will pollute the log 
            if entity is None:
                ws_server_log.info(colored(f'Sent | {event_name}','light_magenta') + f' - {data}')
            else:
                ws_server_log.info(colored(f'Sent | {event_name} to {entity}','light_magenta') + f' - {data}')
        else:
            print(common.formatString('INFO',colored(f'Sent | {event_name}','light_magenta') +f' - {data}'))

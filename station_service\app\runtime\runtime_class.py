import socket
import datetime
import threading
import uuid
from enum import Enum

from queue import Queue
from asyncio import Future
from typing import Dict, List,TYPE_CHECKING, Optional

from .. import adapter as adp

if TYPE_CHECKING:
    from ..service_handler.cube_handler import StationMessageHandler


class FlowStage(Enum):
    """Enum for tracking different stages in the request-response flow"""
    TC_REQUEST_RECEIVED = "tc_request_received"
    HCC_R_PROTOCOL_SENT = "hcc_r_protocol_sent"
    PLC_R_RESPONSE_RECEIVED = "plc_r_response_received"
    TC_APPROVAL_SENT = "tc_approval_sent"
    TC_UPDATE_RECEIVED = "tc_update_received"
    HCC_U_PROTOCOL_SENT = "hcc_u_protocol_sent"
    PLC_U_ACK_RECEIVED = "plc_u_ack_received"
    FLOW_COMPLETED = "flow_completed"

    # Waiting states
    WAITING_STATION_READY = "waiting_station_ready"
    WAITING_BIN_CRITERIA = "waiting_bin_criteria"


class FlowRequest:
    """Class to track individual request flow through the system"""

    def __init__(self, request_id: str, station_code: int, storage_code: str,
                 tc_job_id: int, position: int, action: str):
        self.request_id = request_id
        self.station_code = station_code
        self.storage_code = storage_code
        self.tc_job_id = tc_job_id
        self.position = position
        self.action = action  # 'drop' or 'pick'

        # Flow tracking
        self.current_stage = FlowStage.TC_REQUEST_RECEIVED
        self.stage_timestamps: Dict[FlowStage, datetime.datetime] = {}
        self.stage_timeouts: Dict[FlowStage, datetime.datetime] = {}

        # Error tracking
        self.is_stuck = False
        self.stuck_reason = ""
        self.retry_count = 0

        # Conditional logic tracking
        self.is_waiting = False
        self.waiting_reason = ""

        # Initialize with first timestamp
        self.stage_timestamps[FlowStage.TC_REQUEST_RECEIVED] = datetime.datetime.now()

    def update_stage(self, new_stage: FlowStage, timeout_minutes: int = 1):
        """Update the current stage and set timeout"""
        self.current_stage = new_stage
        self.stage_timestamps[new_stage] = datetime.datetime.now()
        self.stage_timeouts[new_stage] = datetime.datetime.now() + datetime.timedelta(minutes=timeout_minutes)

        # Clear stuck status when progressing
        if self.is_stuck:
            self.is_stuck = False
            self.stuck_reason = ""

    def set_waiting(self, reason: str):
        """Set the request to waiting state"""
        self.is_waiting = True
        self.waiting_reason = reason

    def clear_waiting(self):
        """Clear the waiting state"""
        self.is_waiting = False
        self.waiting_reason = ""

    def check_timeout(self) -> bool:
        """Check if current stage has timed out"""
        if self.current_stage in self.stage_timeouts:
            return datetime.datetime.now() > self.stage_timeouts[self.current_stage]
        return False

    def mark_stuck(self, reason: str):
        """Mark the request as stuck"""
        self.is_stuck = True
        self.stuck_reason = reason

    def get_elapsed_time(self) -> float:
        """Get total elapsed time since request started in minutes"""
        start_time = self.stage_timestamps.get(FlowStage.TC_REQUEST_RECEIVED)
        if start_time:
            return (datetime.datetime.now() - start_time).total_seconds() / 60
        return 0

    def get_stage_elapsed_time(self) -> float:
        """Get elapsed time for current stage in minutes"""
        stage_time = self.stage_timestamps.get(self.current_stage)
        if stage_time:
            return (datetime.datetime.now() - stage_time).total_seconds() / 60
        return 0


class FlowTracker:
    """Runtime flow tracking system for monitoring TC-HCC-PLC communication"""

    def __init__(self):
        self.active_requests: Dict[str, FlowRequest] = {}
        self.completed_requests: Dict[str, FlowRequest] = {}

        # Organize flows by station and position for easier tracking
        self.station_flows: Dict[int, Dict[int, str]] = {}  # station_code -> position -> request_id

        self.lock = threading.Lock()

        # Keep completed requests for a limited time for analysis
        self.max_completed_requests = 1000

    def create_request(self, station_code: int, storage_code: str, tc_job_id: int,
                      position: int, action: str) -> str:
        """Create a new flow request and return its ID"""
        request_id = str(uuid.uuid4())

        with self.lock:
            flow_request = FlowRequest(request_id, station_code, storage_code,
                                     tc_job_id, position, action)
            self.active_requests[request_id] = flow_request

            # Track by station and position
            if station_code not in self.station_flows:
                self.station_flows[station_code] = {}
            self.station_flows[station_code][position] = request_id

        return request_id

    def get_request_by_tc_job_id(self, tc_job_id: int) -> Optional[FlowRequest]:
        """Get request by TC job ID"""
        with self.lock:
            for request in self.active_requests.values():
                if request.tc_job_id == tc_job_id:
                    return request
        return None

    def update_request_stage(self, request_id: str, new_stage: FlowStage, timeout_minutes: int = 1):
        """Update request stage"""
        with self.lock:
            if request_id in self.active_requests:
                self.active_requests[request_id].update_stage(new_stage, timeout_minutes)

    def update_request_stage_by_tc_job_id(self, tc_job_id: int, new_stage: FlowStage, timeout_minutes: int = 1):
        """Update request stage by TC job ID"""
        request = self.get_request_by_tc_job_id(tc_job_id)
        if request:
            request.update_stage(new_stage, timeout_minutes)

    def set_request_waiting(self, request_id: str, reason: str):
        """Set request to waiting state"""
        with self.lock:
            if request_id in self.active_requests:
                self.active_requests[request_id].set_waiting(reason)

    def clear_request_waiting(self, request_id: str):
        """Clear request waiting state"""
        with self.lock:
            if request_id in self.active_requests:
                self.active_requests[request_id].clear_waiting()

    def complete_request(self, request_id: str):
        """Mark request as completed and move to completed list"""
        with self.lock:
            if request_id in self.active_requests:
                request = self.active_requests.pop(request_id)
                request.update_stage(FlowStage.FLOW_COMPLETED)

                # Remove from station flows tracking
                if request.station_code in self.station_flows:
                    if request.position in self.station_flows[request.station_code]:
                        del self.station_flows[request.station_code][request.position]

                    # Clean up empty station entries
                    if not self.station_flows[request.station_code]:
                        del self.station_flows[request.station_code]

                # Add to completed requests with size limit
                self.completed_requests[request_id] = request
                if len(self.completed_requests) > self.max_completed_requests:
                    # Remove oldest completed request
                    oldest_id = min(self.completed_requests.keys(),
                                  key=lambda x: self.completed_requests[x].stage_timestamps[FlowStage.TC_REQUEST_RECEIVED])
                    del self.completed_requests[oldest_id]

    def complete_request_by_tc_job_id(self, tc_job_id: int):
        """Complete request by TC job ID"""
        request = self.get_request_by_tc_job_id(tc_job_id)
        if request:
            self.complete_request(request.request_id)

    def get_stuck_requests(self) -> List[FlowRequest]:
        """Get all requests that are stuck (timed out)"""
        stuck_requests = []

        with self.lock:
            for request in self.active_requests.values():
                if request.check_timeout() and not request.is_waiting:
                    if not request.is_stuck:
                        # Mark as stuck with appropriate reason
                        stage_name = request.current_stage.value
                        request.mark_stuck(f"Timeout at stage: {stage_name}")
                    stuck_requests.append(request)

        return stuck_requests

    def get_waiting_requests(self) -> List[FlowRequest]:
        """Get all requests that are in waiting state"""
        with self.lock:
            return [req for req in self.active_requests.values() if req.is_waiting]

    def get_active_requests_by_station(self, station_code: int) -> List[FlowRequest]:
        """Get all active requests for a specific station"""
        with self.lock:
            return [req for req in self.active_requests.values() if req.station_code == station_code]

    def get_request_by_station_position(self, station_code: int, position: int) -> Optional[FlowRequest]:
        """Get active request for specific station and position"""
        with self.lock:
            if station_code in self.station_flows and position in self.station_flows[station_code]:
                request_id = self.station_flows[station_code][position]
                return self.active_requests.get(request_id)
        return None

    def get_station_flow_summary(self) -> Dict[int, Dict[int, Dict]]:
        """Get summary of flows organized by station and position"""
        summary = {}
        with self.lock:
            for station_code, positions in self.station_flows.items():
                summary[station_code] = {}
                for position, request_id in positions.items():
                    if request_id in self.active_requests:
                        request = self.active_requests[request_id]
                        summary[station_code][position] = {
                            'storage_code': request.storage_code,
                            'tc_job_id': request.tc_job_id,
                            'action': request.action,
                            'current_stage': request.current_stage.value,
                            'elapsed_minutes': request.get_elapsed_time(),
                            'stage_elapsed_minutes': request.get_stage_elapsed_time(),
                            'is_stuck': request.is_stuck,
                            'is_waiting': request.is_waiting,
                            'stuck_reason': request.stuck_reason,
                            'waiting_reason': request.waiting_reason
                        }
        return summary

    def cleanup_old_requests(self, max_age_hours: int = 24):
        """Clean up old requests that might be stuck"""
        cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=max_age_hours)

        with self.lock:
            to_remove = []
            for request_id, request in self.active_requests.items():
                start_time = request.stage_timestamps.get(FlowStage.TC_REQUEST_RECEIVED)
                if start_time and start_time < cutoff_time:
                    to_remove.append(request_id)

            for request_id in to_remove:
                del self.active_requests[request_id]


class TCPClient:
    def __init__(self,host:str,port:int,conn:socket.socket) -> None:
        self.host = host
        self.port = port
        self.conn = conn
        self.hash = hash((self.host, self.port))


class Runtime:
    """
    A runtime class to save all the runtime value

    runtime_st                  - save all the runtime station value
    connected_ips               - save all the runtime plc connection ip
    msg_received_queue          - save all the runtime msg ina queue for further execution
    enrolling_station           - save all the station currently in enrollment mode
    disconnect_threshold_list   - save all the disconnected station, and wait for the threshold time before emit to other services
    flow_tracker                - tracks request-response flows between TC, HCC, and PLC
    """

    runtime_st : Dict[int,adp.StationAdapter] = dict()
    runtime_sd : Dict[int,adp.RedisServiceDoor] = dict()
    runtime_tcp_client : Dict[int,TCPClient] = dict()
    msg_received_queue : 'Queue[StationMessageHandler]' = Queue()
    stations_in_mode : Dict[int,Future] = dict()
    disconnect_threshold_list : Dict[int,Future]= dict()
    opening_sd : Dict[int,Future] = dict()
    runtime_emo : Dict[int,adp.EMOAdapter] = dict()
    flow_tracker : FlowTracker = FlowTracker()
    

    def add_runtime_st(self,station :adp.StationAdapter):
        self.runtime_st[station.code] = station

    def clear_runtime_st(self):
        self.runtime_st.clear()
        
    def get_runtime_st_list(self)->List[adp.StationAdapter]:
        return list(self.runtime_st.values())
    
    def get_runtime_sd_list(self)->List[adp.RedisServiceDoor]:
        return list(self.runtime_sd.values())

    def add_station_in_mode(self,station_code:int,future:Future):
        self.stations_in_mode[station_code] = future

    def get_station_in_mode_future(self,station_code:int)->Future|None:
        return self.stations_in_mode.get(station_code,None)

    def remove_station_in_mode(self,station_code):
        self.stations_in_mode.pop(station_code,None)


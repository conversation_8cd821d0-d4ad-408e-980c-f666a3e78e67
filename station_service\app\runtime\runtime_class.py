import socket

from queue import Queue
from asyncio import Future
from typing import Dict, List,TYPE_CHECKING

from .. import adapter as adp

if TYPE_CHECKING:
    from ..service_handler.cube_handler import StationMessageHandler
    


class TCPClient:
    def __init__(self,host:str,port:int,conn:socket.socket) -> None:
        self.host = host
        self.port = port
        self.conn = conn
        self.hash = hash((self.host, self.port))


class Runtime:
    """
    A runtime class to save all the runtime value 

    runtime_st                  - save all the runtime station value 
    connected_ips               - save all the runtime plc connection ip 
    msg_received_queue          - save all the runtime msg ina queue for further execution
    enrolling_station           - save all the station currently in enrollment mode
    disconnect_threshold_list   - save all the disconnected station, and wait for the threshold time before emit to other services
    """

    runtime_st : Dict[int,adp.StationAdapter] = dict()
    runtime_sd : Dict[int,adp.RedisServiceDoor] = dict()
    runtime_tcp_client : Dict[int,TCPClient] = dict()
    msg_received_queue : 'Queue[StationMessageHandler]' = Queue()
    stations_in_mode : Dict[int,Future] = dict()
    disconnect_threshold_list : Dict[int,Future]= dict()
    opening_sd : Dict[int,Future] = dict()
    runtime_emo : Dict[int,adp.EMOAdapter] = dict()
    

    def add_runtime_st(self,station :adp.StationAdapter):
        self.runtime_st[station.code] = station

    def clear_runtime_st(self):
        self.runtime_st.clear()
        
    def get_runtime_st_list(self)->List[adp.StationAdapter]:
        return list(self.runtime_st.values())
    
    def get_runtime_sd_list(self)->List[adp.RedisServiceDoor]:
        return list(self.runtime_sd.values())

    def add_station_in_mode(self,station_code:int,future:Future):
        self.stations_in_mode[station_code] = future

    def get_station_in_mode_future(self,station_code:int)->Future|None:
        return self.stations_in_mode.get(station_code,None)

    def remove_station_in_mode(self,station_code):
        self.stations_in_mode.pop(station_code,None)


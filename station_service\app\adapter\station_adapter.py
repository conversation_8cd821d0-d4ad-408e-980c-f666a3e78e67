from __future__ import annotations

import json
import datetime
from queue import Queue
from typing import List


from config import Config
from .. import adapter as adp,models as model,common as common,enum_collections as ec
from ..communications_provider import mongo

event_queue_log = common.LogManager('event_queue',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(event_queue_log))
class EventQueue: 
    def __init__(self):
        self._is_paused: bool = False
        self.event_queue : 'Queue[adp.EventAdapter]' = Queue()
    
    def add_event(self,event : adp.EventAdapter):
        self.event_queue.put(event)

    def get_first_event(self)->adp.EventAdapter:
        return self.event_queue.queue[0]

    def next_event(self):
        return self.event_queue.get()
    
    def get_size(self):
        return self.event_queue.qsize()
    
    def queue(self):
        return self.event_queue.queue

    @property
    def is_paused(self):
        return self._is_paused
    
    def pause(self):
        self._is_paused = True

    def unpause(self):
        self._is_paused = False


class StationAdapter:
    """
    Adapter class for runtime station 

    For I station, there is few more field
        - event_queue 
        - buffer 
    """

    def __init__(self,st : model.Stations):
        self.id = st.id
        self.code = st.code
        self.matrix_code = st.matrix_code
        self.name = f'ST{st.code}'
        self.host = st.host
        self.port:int = st.port
        self.connection : int = None
        self.zone = st.zone
        self.type :ec.CubeStationType = st.type
        self.cell = st.cell
        self.mode = st.mode
        self.queue : List[str] = json.loads(st.queue)
        self.plc_status : str = st.plc_status
        self.rotation = st.rotation
        self.adjacent = st.adjacent
        self.inner_drop = st.inner_drop
        self.inner_pick = st.inner_pick
        self.outer_drop = st.outer_drop
        self.outer_pick = st.outer_pick
        self.worker = st.worker
        self.bin_at_worker:str = st.bin_at_worker
        self.gw_operating = st.gw_operating
        self.service_door = st.service_door
        self.is_connected = st.is_connected
        self.is_active: bool = st.is_active
        self.is_maintenance : bool = st.is_maintenance
        self.is_recovery : bool = st.is_recovery
        self.is_overweight: bool = st.is_overweight
        self.buffer = None
        self.event_queue: EventQueue = None
        self.last_ping_time = None
        # if mongo logging 
        if Config.MONGO:
            self.log = mongo.MongoClient
        else:
            self.log = common.LogManager(f'ST{self.code}',centralize_logging=True)
        if st.type == ec.CubeStationType.I.value:
            self.buffer = 2
            self.event_queue = EventQueue()
        
        self.gw_in_status = {st.inner_drop:['',''],st.outer_drop:['','']}
        self.gw_out_status = {st.inner_pick:['',''],st.outer_pick:['','']}
        self.latest_drop_msg = {st.inner_drop:'',st.outer_drop:''}
        self.latest_cancel_drop_msg = {st.inner_drop:'',st.outer_drop:''}
        self.latest_pick_msg = {st.inner_pick:'',st.outer_pick:''}
        self.latest_cancel_pick_msg = {st.inner_pick:'',st.outer_pick:''}

    def record_gw_status(self,is_drop:bool,position:int,message:str):
        if is_drop:
            self.gw_in_status[position] = [message,datetime.datetime.now(tz=datetime.timezone.utc).isoformat()]
        else:
            self.gw_out_status[position] = [message,datetime.datetime.now(tz=datetime.timezone.utc).isoformat()]
    def as_dict(self):
        exclude_attrs = ['event_queue','last_ping_time','log']
        return {attr: getattr(self, attr) for attr in self.__dict__ if attr not in exclude_attrs}
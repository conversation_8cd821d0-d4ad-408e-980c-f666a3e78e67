import pymongo 
import time

from termcolor import colored
from config import Config
from datetime import datetime, timezone

class MongoClient:
    
    mongo_client = None
    
    @classmethod
    def initialize(cls):
        # check if file logging or mongo logging 
        if Config.MONGO: 
            cls.host,cls.name,cls.user,cls.password = Config.get_mongo_conn()
            cls.mongo_client = pymongo.MongoClient(
                host = cls.host,
                username = cls.user,
                password = cls.password,
                replicaset = Config.MONGO_REPLICA_SET or None
            )

            cls.check_connection()
            cls.hcc_db = cls.mongo_client[cls.name]
            cls.plc_log = cls.hcc_db["plc_log"]

            cls.plc_log.create_index("expire_at", expireAfterSeconds=3 * 24 * 60 * 60 )  # Expires in 3 days
    @classmethod
    def check_connection(cls):
        while True:
            try:
                cls.mongo_client.server_info()
                return
            except Exception as e:
                print(colored(f'{cls.check_connection.__name__} raised {e}',color='light_red'))
                time.sleep(3)


    @classmethod
    def msg_log(cls,station_code:int,msg:str):
        data = {
            "station_code":station_code,
            "message":msg,
            "time":datetime.now().timestamp(),
            "expire_at":datetime.now(timezone.utc)
        }
        cls.plc_log.insert_one(data)

    @classmethod
    def get_plc_log(cls,station_code:int,from_timestamp:int,to_timestamp:int):
        query = { 
            "station_code" : station_code, 
            "time": { 
                "$gt": from_timestamp,
                "$lt": to_timestamp
                } 
            }
        result = cls.plc_log.find(query).sort("_id", pymongo.ASCENDING)

        return result 
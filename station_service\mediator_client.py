import time
from flask import Flask, jsonify
from flask import request,make_response

app = Flask(__name__)

@app.route('/v1/app/pallet-arrived', methods = ['PATCH'])
def pallet_arrived():
    print(request.get_json())
    return "OK", "200"

@app.route('/v1/app/order-completed', methods = ['PATCH'])
def order_completed():
    data = request.get_json()
    print(data)
    return "order-completed","200"

@app.route('/v1/app/cuba', methods = ['GET'])
def cuba():
    print(request.get_json())
    return "HELLO WORLD","200"

@app.route('/v1/app/err3-recovery', methods = ['PATCH'])
def reset_error():
    print(request.get_json())
    return "OK","200"

@app.route('/v1/app/order-error', methods = ['PATCH'])
def update_error():
    print(request.get_json())
    return "OK","200"

@app.route('/v1/app/request-location', methods = ['POST'])
def request_location():
    print(request.get_json())
    return {
        "data":{
                "x":1,
                "y":3,
                "z":5
        }     
    }

@app.route('/v1/app/cancel-job', methods = ['POST'])
def cancel_job():
    print(request.get_json())
    return {
        "data":{
            "lol":1
        }
    }
    
@app.route('/api/wcs/integrations/v1/matrix/errors', methods = ['POST'])
def errors():
    print(request.get_json())
    print(request.headers)
    return {
        "result":1,
        "resultCode":"success",
        "errors":"",
        "data":True,
        "pagination":"",
    }

# SM

@app.route('/v2/storages/enroll',methods=['POST'])
def bin_enroll():
    print(request.get_json())
    return {
        "messgae":"OK",        
    },200
    
@app.route('/v3/operations/enroll',methods=['POST'])
def bin_enrollv3():
    print(request.get_json())
    data = {
        "pagination": 1,
        "errors": [
                {
                "code": "storage_not_found",
                "message": "storage not found."
                },
                 {
                "code": "unhandled_exception",
                "message": "something went wrong, please report issue to us or retry again later."
                }
            ],
        "data": {
            "station": 1,
            "storage": 1,
            "toIndex": 4
        }
    }
    response = make_response(jsonify(data), 200)
    return response
    
@app.route('/asyncio/test',methods=['GET'])
def test():
    print(request.get_json())
    time.sleep(5)
    return {
        "message":"OK",        
    },200

if __name__ == "__main__":
    app.run(host = '127.0.0.1', port = '3032', debug = False)
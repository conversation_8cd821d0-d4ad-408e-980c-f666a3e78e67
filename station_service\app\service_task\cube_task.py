from typing import Callable, List
from concurrent.futures import Thread<PERSON>oolExecutor

from config import Config

class CubeTask:

    @classmethod
    def run(cls):
        thread_pool : List[Callable] = list()

        from ..auth import AuthServices
        AuthServices.initialization()

        from ..initialization import station_initialization,service_door_initilization,emo_initilization
        station_initialization()
        service_door_initilization()
        emo_initilization()
        
        from ..communications_provider.mongo import MongoClient
        MongoClient.initialize()

        from ..key import KeyGen
        KeyGen.initialize()

        from .. import common as common
        thread_pool.append(common.BackgroundTask.run)
    
        from ..http_router import HttpRequest
        thread_pool.append(HttpRequest.loop)

        from .. import server as server
        thread_pool.append(server.start_server)

        if Config.MOCK_STATION_MSG:
            from ..mock import MockStation
            thread_pool.append(MockStation.start)

        from ..service_handler import message_handler_loop,message_broadcast_loop,event_queue_handler
        thread_pool.append(message_handler_loop)
        thread_pool.append(message_broadcast_loop)
        thread_pool.append(event_queue_handler)

        if thread_pool:
            executor = ThreadPoolExecutor(len(thread_pool))

            for thread in thread_pool:
                executor.submit(thread)
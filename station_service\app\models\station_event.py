from dataclasses import dataclass
from .. import db

@dataclass
class StationEvent(db.Model):
    id:int
    event_name : str 
    station_code : int 
    storage_code : str
    is_enroll : bool
    is_processed : bool 
    status : str
    request_id : int 
    err_msg :str 
    job_to_complete_event : str  
    created_at:str
    updated_at:str

    __tablename__ = 'station_event'

    id = db.Column(
        db.Integer,
        primary_key=True)
    
    event_name = db.Column(
        db.String(128),
        index = False,
        nullable=False
    )
    
    station_code = db.Column(
        db.Integer,
        index=False,
    )

    storage_code = db.Column(
        db.String(128),
        index=False,
    )

    is_enroll = db.Column(
        db.Bo<PERSON>an,
        index=False,
        default=False
    )

    is_processed = db.Column(
        db.Boolean,
        index=False,
        default=False
    )

    status = db.Column(
        db.String(128),
        index=False,
        unique=False
    )

    request_id = db.Column(
        db.Integer,
        index=False,
        nullable=True
    )

    err_msg = db.Column(
        db.String(128),
        index=False,
        unique=False,
        nullable=True
    )

    job_to_complete_event = db.Column(
        db.Text, 
        default = "[]",
        unique = False,
        index = False,
        nullable = True
    )

    created_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

    updated_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )
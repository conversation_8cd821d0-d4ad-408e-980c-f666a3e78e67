# from simple_chalk import redBright
# from flask_socketio import emit
# from flask import jsonify, request
# import time

# from ... import socketio
# from ...server import server
# from ...database.cube import cube
# from ..general import checkRequest, emitActiveStation, requestallStatus
# from ...common.responseSerialize import JsonPayload

# # ---------------------  General Events ------------------------------


# @socketio.on('connect')
# def handleConnection():
#     try:
#         print('A socketio client has connected.')
#     except Exception as e:
#         print(redBright(e))


# @socketio.on('disconnect')
# def handleDisconnect():
#     try:
#         print('A socketio client has disconnected.')
#     except Exception as e:
#         print(redBright(e))

# # ---------------------  TC Events ------------------------------


# @socketio.on("init", namespace='/station')
# def init_tc(data):
#     print("TC init")
#     emitActiveStation()
#     requestallStatus()


# @socketio.on("status", namespace='/station')
# def requestStatus(data):
#     try:
#         msgToSend = "ST,"+str(data['station_code'])+",S;"
#         reply = server.sendMsg("ST"+str(data['station_code']), msgToSend)
#     except Exception as e:
#         print(redBright(e))


# @socketio.on('request', namespace="/station")
# def RequestStation(data):
#     try:
#         if (data['action'] == "drop"):
#             action = "D"
#         elif (data['action'] == "pick"):
#             action = "P"

#         msgToSend = "ST,"+str(data['station_code'])+",R," + str(data['job_id']) + ","+str(data['position'])+"|"+action+"|" + str(data['bin_id'])+";"
#         reply = checkRequest(data['station_code'], msgToSend)

#         # print(reply)
#         # if type(reply) is list:
#         #     if (reply[2] == 'A'):
#         #         requestReply = jsonFormat({
#         #             "station_id": data['station_id'],
#         #             "job_id": data['job_id'],
#         #             "status": True,
#         #             "message": "Approved"}, "")
#         #     else:
#         #         requestReply = jsonFormat({
#         #             "station_id": data['station_id'],
#         #             "job_id": data['job_id'],
#         #             "status": False,
#         #             "message": "Denied"}, "")
#         # else:
#         #     requestReply = jsonFormat({
#         #         "station_id": data['station_id'],
#         #         "job_id": data['job_id'],
#         #         "status": False,
#         #         "message": "Denied"}, reply)
#         # emit("request", requestReply)
#         # print(requestReply)
#     except Exception as e:
#         print(redBright(e))


# @socketio.on("update", namespace='/station')  # Add Bin at index 0
# def updateStation(data):
#     try:
#         if (data['action'] == "drop"):
#             action = 'D'
#         elif (data['action'] == "pick" or data['action'] == "Pick"):
#             action = 'P'

#         msgToSend = "ST,"+str(data['station_code'])+",U," + str(data['job_id']) + ","+str(data['position'])+"|"+action+"|" + data['bin_id']+";"
#         reply = checkRequest(data['station_code'], msgToSend)
#         # print("update: ", reply)
#         # if type(reply) is list:
#         #     if(reply[0] == "ACK"):
#         #         print('acknowledging update')
#         #         requestReply = jsonFormat({
#         #             "station_id": data['station_id'],
#         #             "job_id": reply[4],
#         #             "status": "true",
#         #             "message": "Station has successfully updated bin."}, "")
#         #         emit('update', requestReply)
#         #         cube.updateBin(str(data['bin_id']), int(data['position']), int(data['station_id']))
#         #         msgToSend = "ST,"+str(data['station_id'])+",S;"  # improve in future
#         #         server.sendMsg("ST"+str(data['station_id']), msgToSend)
#         # else:
#         #     requestReply = jsonFormat({
#         #         "station_id": data['station_id'],
#         #         "job_id": reply[4],
#         #         "status": "false",
#         #         "message": "Unable to update bin."}, reply)
#         #     print(requestReply)
#         #     emit('update', requestReply)
#     except Exception as e:
#         print("update socket error: ", redBright(e))


# @socketio.on("move", namespace='/station')
# def StationMoveBin(data):
#     try:
#         # current_position_list = cube.compileMoveFunc(int(data['from_pos']), int(data['to_pos']), int(data['station_id']), data['bin_id'])
#         # requestReply = jsonFormat({
#         #     "station_id": int(data['station_id']),
#         #     "bins": current_position_list
#         # }, "")
#         # print("emiting status :", requestReply)
#         # emit('status', requestReply, broadcast=True, namespace='/station')
#         station = cube.get_station_by_code(int(data['station_code']))
#         msgToSend = "ST," + str(data['station_code']) + ",M," + str(data['job_id']) + "," + str(data['from_pos']) + "|" + str(data['to_pos']) + "|" + station.rotation + "|" + str(data['bin_id']) + ";"
#         reply = checkRequest(int(data['station_code']), msgToSend)

#         if type(reply) is list:
#             requestReply = JsonPayload.jsonFormat({
#                 "station_code": int(data['station_code']),
#                 "job_id": data['job_id'],
#                 "status": "COMPLETED",
#                 "bin_id": data['bin_id']}, '')
#             if(reply[0] == "ACK"):
#                 print("Station has acknowledged move request")
#         else:
#             requestReply = JsonPayload.jsonFormat({
#                 "station_code": int(data['station_code']),
#                 "job_id": data['job_id'],
#                 "status": "ERROR",
#                 "bin_id": data['bin_id']}, reply)
#             emit('move', requestReply)
#     except Exception as e:
#         print(e)

# # ---------------------  WMS Events ------------------------------


# @socketio.on("weight", namespace="station")
# def stationWeight(data):
#     pass
#     # placeholder function for when WMS wants to call weight manually.

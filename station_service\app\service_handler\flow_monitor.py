"""
Flow monitoring service for tracking TC-HCC-PLC communication flows
"""
import datetime
from typing import Optional, Dict, Any
from ..runtime.runtime_class import FlowStage, FlowRequest
from ..runtime import runtime
from .. import database as db
from ..enum_collections import StationRequestType, CubeStationType
from ..common import log_and_suppress_error
from .. import common

# Create logger for flow monitoring
flow_monitor_log = common.get_logger('flow_monitor')


@log_and_suppress_error(flow_monitor_log)
class FlowMonitor:
    """Service for monitoring request-response flows"""
    
    @staticmethod
    def start_flow_tracking(station_code: int, storage_code: str, tc_job_id: int, 
                           position: int, action: str) -> str:
        """Start tracking a new flow"""
        try:
            # Create flow request in runtime
            request_id = runtime.flow_tracker.create_request(
                station_code, storage_code, tc_job_id, position, action
            )
            
            # Create database record
            db.flow_tracking_db_func.create_flow_record(
                request_id, station_code, storage_code, tc_job_id, 
                position, action, FlowStage.TC_REQUEST_RECEIVED.value
            )
            
            # Create stage history
            db.flow_tracking_db_func.create_stage_history(
                request_id, FlowStage.TC_REQUEST_RECEIVED.value,
                notes=f"TC {action} request received for bin {storage_code} at station {station_code}"
            )
            
            flow_monitor_log.info(f"Started flow tracking for TC job {tc_job_id}: {action} {storage_code} at ST{station_code}")
            return request_id
            
        except Exception as e:
            flow_monitor_log.error(f"Failed to start flow tracking: {e}")
            return ""
    
    @staticmethod
    def update_flow_stage(tc_job_id: int, new_stage: FlowStage, notes: str = "", timeout_minutes: int = 1):
        """Update flow stage by TC job ID"""
        try:
            # Update runtime
            runtime.flow_tracker.update_request_stage_by_tc_job_id(tc_job_id, new_stage, timeout_minutes)
            
            # Get the flow request to update database
            flow_request = runtime.flow_tracker.get_request_by_tc_job_id(tc_job_id)
            if flow_request:
                # Update database
                db.flow_tracking_db_func.update_flow_stage(
                    flow_request.request_id, 
                    new_stage.value,
                    flow_request.get_stage_elapsed_time(),
                    flow_request.get_elapsed_time()
                )
                
                # Create stage history
                timeout_at = datetime.datetime.now() + datetime.timedelta(minutes=timeout_minutes)
                db.flow_tracking_db_func.create_stage_history(
                    flow_request.request_id, new_stage.value, timeout_at, notes
                )
                
                flow_monitor_log.info(f"Updated flow stage for TC job {tc_job_id} to {new_stage.value}: {notes}")
            else:
                flow_monitor_log.warning(f"Flow request not found for TC job {tc_job_id}")
                
        except Exception as e:
            flow_monitor_log.error(f"Failed to update flow stage: {e}")
    
    @staticmethod
    def mark_flow_waiting(tc_job_id: int, reason: str):
        """Mark flow as waiting"""
        try:
            flow_request = runtime.flow_tracker.get_request_by_tc_job_id(tc_job_id)
            if flow_request:
                # Update runtime
                runtime.flow_tracker.set_request_waiting(flow_request.request_id, reason)
                
                # Update database
                db.flow_tracking_db_func.mark_flow_waiting(flow_request.request_id, reason)
                
                flow_monitor_log.info(f"Marked flow as waiting for TC job {tc_job_id}: {reason}")
            else:
                flow_monitor_log.warning(f"Flow request not found for TC job {tc_job_id}")
                
        except Exception as e:
            flow_monitor_log.error(f"Failed to mark flow as waiting: {e}")
    
    @staticmethod
    def clear_flow_waiting(tc_job_id: int):
        """Clear flow waiting state"""
        try:
            flow_request = runtime.flow_tracker.get_request_by_tc_job_id(tc_job_id)
            if flow_request:
                # Update runtime
                runtime.flow_tracker.clear_request_waiting(flow_request.request_id)
                
                # Update database
                db.flow_tracking_db_func.clear_flow_waiting(flow_request.request_id)
                
                flow_monitor_log.info(f"Cleared waiting state for TC job {tc_job_id}")
            else:
                flow_monitor_log.warning(f"Flow request not found for TC job {tc_job_id}")
                
        except Exception as e:
            flow_monitor_log.error(f"Failed to clear flow waiting: {e}")
    
    @staticmethod
    def complete_flow(tc_job_id: int):
        """Complete flow tracking"""
        try:
            # Complete in runtime
            runtime.flow_tracker.complete_request_by_tc_job_id(tc_job_id)
            
            # Get the flow request to complete in database
            flow_db = db.flow_tracking_db_func.get_flow_by_tc_job_id(tc_job_id)
            if flow_db:
                db.flow_tracking_db_func.complete_flow(flow_db.request_id)
                
                # Create final stage history
                db.flow_tracking_db_func.create_stage_history(
                    flow_db.request_id, FlowStage.FLOW_COMPLETED.value,
                    notes="Flow completed successfully"
                )
                
                flow_monitor_log.info(f"Completed flow tracking for TC job {tc_job_id}")
            else:
                flow_monitor_log.warning(f"Flow database record not found for TC job {tc_job_id}")
                
        except Exception as e:
            flow_monitor_log.error(f"Failed to complete flow: {e}")
    
    @staticmethod
    def handle_station_type_i_logic(station_code: int, tc_job_id: int, storage_code: str):
        """Handle special logic for station type I"""
        try:
            station = db.st_db_func.get_station_by_code(station_code)
            
            if station.type == CubeStationType.I.value:
                # Check if we need to create station_gw_date row and wait for station ready
                # This is where the conditional logic would be implemented
                
                # For now, mark as waiting for station ready
                FlowMonitor.mark_flow_waiting(
                    tc_job_id, 
                    f"Station type I: Waiting for station ready status before sending R protocol"
                )
                
                flow_monitor_log.info(f"Station type I logic applied for ST{station_code}, TC job {tc_job_id}")
                return True
                
        except Exception as e:
            flow_monitor_log.error(f"Failed to handle station type I logic: {e}")
            
        return False
    
    @staticmethod
    def handle_bin_criteria_check(station_code: int, tc_job_id: int, storage_code: str) -> bool:
        """Check if bin meets criteria for processing"""
        try:
            # Check if bin meets processing criteria
            # This would include checks like bin processing status, etc.
            
            # For now, check if there are unprocessed bins that need to be processed first
            unprocessed_count = db.st_order_db_func.get_num_of_bin_in_station_pre_processed(station_code)
            
            if unprocessed_count > 0:
                FlowMonitor.mark_flow_waiting(
                    tc_job_id,
                    f"Waiting for {unprocessed_count} bins to be processed before sending R protocol"
                )
                flow_monitor_log.info(f"Bin criteria check failed for ST{station_code}, TC job {tc_job_id}: {unprocessed_count} unprocessed bins")
                return False
                
            return True
            
        except Exception as e:
            flow_monitor_log.error(f"Failed to check bin criteria: {e}")
            return False
    
    @staticmethod
    def get_stuck_flows_summary() -> Dict[str, Any]:
        """Get summary of stuck flows for healthcheck"""
        try:
            stuck_flows = runtime.flow_tracker.get_stuck_requests()
            waiting_flows = runtime.flow_tracker.get_waiting_requests()
            
            summary = {
                'stuck_count': len(stuck_flows),
                'waiting_count': len(waiting_flows),
                'stuck_flows': [],
                'waiting_flows': []
            }
            
            for flow in stuck_flows:
                summary['stuck_flows'].append({
                    'station_code': flow.station_code,
                    'storage_code': flow.storage_code,
                    'tc_job_id': flow.tc_job_id,
                    'action': flow.action,
                    'current_stage': flow.current_stage.value,
                    'stuck_reason': flow.stuck_reason,
                    'elapsed_minutes': flow.get_elapsed_time(),
                    'stage_elapsed_minutes': flow.get_stage_elapsed_time()
                })
            
            for flow in waiting_flows:
                summary['waiting_flows'].append({
                    'station_code': flow.station_code,
                    'storage_code': flow.storage_code,
                    'tc_job_id': flow.tc_job_id,
                    'action': flow.action,
                    'current_stage': flow.current_stage.value,
                    'waiting_reason': flow.waiting_reason,
                    'elapsed_minutes': flow.get_elapsed_time()
                })
            
            return summary
            
        except Exception as e:
            flow_monitor_log.error(f"Failed to get stuck flows summary: {e}")
            return {'stuck_count': 0, 'waiting_count': 0, 'stuck_flows': [], 'waiting_flows': []}
    
    @staticmethod
    def cleanup_old_flows():
        """Cleanup old flow records"""
        try:
            # Cleanup runtime flows older than 24 hours
            runtime.flow_tracker.cleanup_old_requests(24)
            
            # Cleanup database flows older than 7 days
            deleted_count = db.flow_tracking_db_func.cleanup_old_flows(24 * 7)
            
            flow_monitor_log.info(f"Cleaned up {deleted_count} old flow records")
            
        except Exception as e:
            flow_monitor_log.error(f"Failed to cleanup old flows: {e}")

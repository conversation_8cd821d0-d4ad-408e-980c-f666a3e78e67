import time 
import logging

from flask import Flask
from typing import Callable
from flask_cors import CORS
from flask_migrate import Migrate
from sqlalchemy import create_engine
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy.exc import SQLAlchemyError,OperationalError

from config import Config

EXCLUDE_METHODS = ['OPTIONS']

class DBMonitor:

    is_down = True

    @classmethod
    def retry_session(cls, func: Callable):
        def _decorate(*args, **kwargs):
            while True:
                try:
                    res = func(*args, **kwargs)
                    cls.is_down = False
                    return res
                except OperationalError as e:
                    cls.is_down = True
                    print(f'Retrying {func.__qualname__} due to {e.args}.')
                    time.sleep(3)
                    continue
                except Exception or SQLAlchemyError as e:
                    raise Exception(f"{func.__qualname__} raise exception : {e}")
        return _decorate


app = Flask(__name__, instance_relative_config=False)
url = Config.get_sqlalchemy_db_uri()
app.config['SQLALCHEMY_DATABASE_URI'] = url
app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = True
db = SQLAlchemy(app)
migrate = Migrate(app=app,db=db,compare_type=True)


@DBMonitor.retry_session
def create_app():

    global app
    logging.getLogger('werkzeug').disabled = True
    CORS(app, resources={r"*": {"origins": "*"}})
    app.config.from_object('config.Config')
    
    with app.app_context():
        
        from .models import Asrs_job,Asrs_order,AsrsStations
        db.create_all()


    from .blueprints.route.asrs_routes import asrs
    app.register_blueprint(asrs)

    @app.route('/')
    def index():
        return 'HCC Web Server Hello World'
    
    return app

import datetime

from typing import List
from sqlalchemy import or_
from sqlalchemy.orm.attributes import flag_modified


from ... import app,DBMonitor,db
from ... import common as common,models as model,enum_collections as ec

db_log = common.LogManager('cube_db_error',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_log))
class WebSocketReqSQLAlchemyQueries:
        
    @DBMonitor.retry_session
    def get_request(self,req_id:int)->model.WebsocketRequests:
        with app.app_context():
            curr_session = db.session
            request = curr_session.query(model.WebsocketRequests).filter_by(id = req_id).first()
            return request
        
    @DBMonitor.retry_session
    def get_available_requests(self)->List[model.WebsocketRequests]:
        with app.app_context():
            curr_session = db.session
            requests = curr_session.query(model.WebsocketRequests).filter(or_(model.WebsocketRequests.status == ec.OrderStatus.AVAILABLE.value, model.WebsocketRequests.status == ec.OrderStatus.PROCESSING.value)).all()
            return requests
               
    @DBMonitor.retry_session
    def create_ws_request(self,entity, data, event, namespace)->model.WebsocketRequests:
            curr_session = db.session
            request = model.WebsocketRequests(
                entity=entity,
                data=data,
                event=event,
                namespace=namespace,
                status=ec.OrderStatus.AVAILABLE.value,
                retry=0,
                created_at=datetime.datetime.now(),
                updated_at=datetime.datetime.now()
            )
            curr_session.add(request)
            curr_session.commit()
            return request 
                
    @DBMonitor.retry_session
    def find_ws_request(self, id:int)->model.WebsocketRequests:
        with app.app_context():
            curr_session = db.session
            request = curr_session.query(model.WebsocketRequests).filter_by(id=id).first()
            return request
        
    @DBMonitor.retry_session
    def update_ws_request(self,request_id:int,update:dict)->int:
        with app.app_context():
            curr_session = db.session
            request = curr_session.query(model.WebsocketRequests).filter_by(id=request_id).first()
            if request:
                for k,v in update.items():
                    request.__setattr__(k,v)
                request.updated_at = datetime.datetime.now()
                req_id = request.id
                curr_session.commit()
                return req_id
            else:
                db_log.error(f'No websocket request with id {request_id} found')
                return None

    @DBMonitor.retry_session
    def update_ws_request_status(self,request_id:int,status:str):
        with app.app_context():
            curr_session = db.session
            request = curr_session.query(model.WebsocketRequests).filter_by(id=request_id).first()
            request.status = status
            request.updated_at = datetime.datetime.now()
            curr_session.commit()
            
    @DBMonitor.retry_session
    def update_ws_request_status_retry(self,request_id:int,retry:int):
        with app.app_context():
            curr_session = db.session
            request = curr_session.query(model.WebsocketRequests).filter_by(id=request_id).first()
            request.retry = retry
            request.updated_at = datetime.datetime.now()
            curr_session.commit()
        
    @DBMonitor.retry_session
    def update_ws_request_payload(self,request_id:int,new_data:dict):
        with app.app_context():
            curr_session = db.session
            request = curr_session.query(model.WebsocketRequests).filter(model.WebsocketRequests.id == request_id).first()
            if request:
                request.data = new_data
                flag_modified(request, 'data')
                request.updated_at = datetime.datetime.now()
                curr_session.commit()

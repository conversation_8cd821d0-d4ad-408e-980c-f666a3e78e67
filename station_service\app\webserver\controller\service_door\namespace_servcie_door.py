from flask_restx import Namespace,Resource, reqparse

from .... import api
from ... import api_routing as api_routing
from .... import common as common,enum_collections as ec,database as db

nsservice_door = Namespace(api_routing.UrlFolder.service_door,"Service Door API")

get_sd_parser = reqparse.RequestParser()
get_sd_parser.add_argument("id",required=False,type=int,location='args')

new_sd_parser = reqparse.RequestParser()
new_sd_parser.add_argument("name",required = True,type = str,location = 'args')
new_sd_parser.add_argument("host",required = False,type = str,location = 'args')
new_sd_parser.add_argument("port",required = False,type = int,location = 'args')
new_sd_parser.add_argument("type",required = True,type = str,choices=list(ec.ServiceDoorType._value2member_map_), location = 'args')
new_sd_parser.add_argument("station_list",required = False,type = str, location = 'args', help= "Must be in format 1,2,3")
new_sd_parser.add_argument("status",required = True,type = str,choices=list(ec.ServiceDoorStatus._value2member_map_), location = 'args')

del_sd_parser = reqparse.RequestParser()
del_sd_parser.add_argument("id",required = True, type = int, location = 'args')


@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsservice_door.route(api_routing.UrlPath.service_door)
class ServiceDoor(Resource):

    @nsservice_door.doc(description='API to get Service Door table')
    @api.expect(get_sd_parser)
    def get(self):
        '''
        Get service door 
        '''

        args = get_sd_parser.parse_args(strict=True)
        id = args.get("id",None)

        if id is None:
            sds = db.sd_db_function.get_sds()
        else:
            sd = db.sd_db_function.get_sd_by_id(id)
            sds = [sd]

        sd_list = [sd.as_dict() for sd in sds]
        return_list = sorted(sd_list, key=lambda item: item["id"])

        return common.StandardResponse.response(data=return_list)

    @nsservice_door.doc(description='API to modify Service Door table')
    @api.expect(new_sd_parser)
    def post(self):
        '''
        Add new Service Door
        '''

        import json
        
        args = new_sd_parser.parse_args(strict=True)
        name = args.get("name")
        host = args.get("host")
        port = args.get("port")
        type = args.get("type")
        station_list = args.get("station_list")
        status = args.get("status")

        try:
            if station_list:
                list_of_st = station_list.split(",")
            else:
                list_of_st = []
            str_list_of_st = json.dumps(list_of_st)
        except Exception as e:
            return common.StandardResponse.response(False,"Invalid station list")

        new_sd_id = db.sd_db_function.add_new_sd(name,type,status,str_list_of_st,host,port)

        return common.StandardResponse.response(message=f'New Service Door added {new_sd_id} ')
    
    @nsservice_door.doc(description='API to reinitializd redis Service Door table')
    def put(self):
        '''
        Reinitilize redis sd from db
        '''
        from ....initialization.service_door_setup import service_door_initilization

        service_door_initilization()
        
        return common.StandardResponse.response()
    
    @nsservice_door.doc(description='API to modify Service Door table')
    @api.expect(del_sd_parser)
    def delete(self):
        '''
        Delete existing Service door
        '''

        args = del_sd_parser.parse_args(strict=True)
        id = args.get("id")

        db.sd_db_function.del_sd(id)

        return common.StandardResponse.response()


sd_id_req_parse = reqparse.RequestParser()
sd_id_req_parse.add_argument("sd_id",required=True,type = int, location = 'json')

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsservice_door.route(api_routing.UrlPath.sd_open)
class OpenServiceDoor(Resource):

    @api.expect(sd_id_req_parse)
    def post(self):
        from ....runtime import runtime
        from ....service_handler.cube_handler import send_tcp_msg
        from ....common import BackgroundTask

        data = sd_id_req_parse.parse_args(strict=True)

        sd_id = data['sd_id']

        sd = db.sd_db_function.get_sd_by_id(sd_id)

        if not sd or not sd.is_connected:
            return common.StandardResponse.response(False,f'SD{sd_id} is not connected or not exist,')

        if sd.status == ec.ServiceDoorStatus.OPEN.value:
            return common.StandardResponse.response(False,f'SD{sd_id} already opened.')
        
        if sd.type == ec.ServiceDoorType.NORMAL.value:
            send_tcp_msg(sd_id,common.SDMsgFormatter.open_door_msg(sd_id),target_module=ec.ModuleCode.SERVCIE_DOOR.value)
        else:
            # check if there is any station not connected 
            station_list = sd.station_list
            
            if all(db.st_db_func.get_station_by_code(int(st_code)).is_active for st_code in station_list):
                return common.StandardResponse.response(False,'No station is the list in in active at this moment.')
            
            if sd_id in runtime.opening_sd:
                task = runtime.opening_sd[sd_id].result()
                if task.done():
                    send_tcp_msg(sd_id,common.SDMsgFormatter.open_door_msg(sd_id),target_module=ec.ModuleCode.SERVCIE_DOOR.value)
                    return common.StandardResponse.response(True,f'Sent request to SD{sd_id} ')
                else:
                    return common.StandardResponse.response(False,f'SD{sd_id} is currently on going opening process.')
            else:
                if not BackgroundTask.open_station_service_door(sd_id):
                    return common.StandardResponse.response(True,f'Sent request to SD{sd_id} ')
                else:
                    common.notifications.notify_service_door(sd.id,"PROCESSING")
                    return common.StandardResponse.response(True,f'Will start open service door process, setting other related station to maintenance, please wait for around 1-8 seconds to finish all the related station movement')

        return common.StandardResponse.response(True,f'Sent request to SD{sd_id} ')

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsservice_door.route(api_routing.UrlPath.sd_close)
class CloseServiceDoor(Resource):

    @api.expect(sd_id_req_parse)
    def post(self):
        from ....service_handler.cube_handler import send_tcp_msg

        data = sd_id_req_parse.parse_args(strict=True)

        sd_id = data['sd_id']

        sd = db.sd_db_function.get_sd_by_id(sd_id)

        if not sd or not sd.is_connected:
            return common.StandardResponse.response(False,f'SD{sd_id} is not connected or not exist,')

        if sd.status == ec.ServiceDoorStatus.CLOSE.value:
            return common.StandardResponse.response(False,f'SD{sd_id} already closed.')
        
        send_tcp_msg(sd_id,common.SDMsgFormatter.close_door_msg(sd_id),target_module=ec.ModuleCode.SERVCIE_DOOR.value)

        return common.StandardResponse.response()    

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsservice_door.route(api_routing.UrlPath.reset)
class ResetSD(Resource):

    @api.expect(sd_id_req_parse)
    def post(self):

        from ....runtime import runtime
        
        data = sd_id_req_parse.parse_args(strict=True)

        sd_id = data.sd_id

        sd = db.sd_db_function.get_sd_by_id(sd_id)
        
        runtime.opening_sd.pop(sd_id,None) 
        db.sd_db_function.change_related_st_status(sd.id,False)

        return common.StandardResponse.response()
        



import asyncio
import datetime
import schedule
import threading

from typing import Dict
from itertools import cycle
from contextlib import suppress
from termcolor import cprint,colored
from threading import current_thread
from typing import Optional, Coroutine, Callable
from asyncio import AbstractEventLoop, Future, Task, CancelledError
from pingspace_python_packages.rabbitmq import RabbitMQ as _RMQ, TeamsNotification, WebsocketNotification

from .del_log import <PERSON><PERSON>og
from config import Config
from .. import enum_collections as ec
from .logger_manager import LogManager
from .decorator import decorateAllFunctionInClass,log_and_suppress_error

background_service_log = LogManager('cube_background_service',display_console=True)
expected_running_thread = [
    'socketio_server','tcp_server','message_handler_loop','background_task','message_broadcast_loop','event_queue_handler','flask_app','http_request'
]

class NotificationMsg():
    def __init__(self) -> None:
        self.msg = cycle([
            'If you dont have a good system, make sure you get good users.',
            'Treat your password like your toothbrush. Don’t let anybody else use it, and get a new one every six months.',
            'The best thing about a boolean is even if you are wrong, you are only off by a bit.',
            'It’s not a bug – it’s an undocumented feature.',
            'Always code as if the guy who ends up maintaining your code will be a violent psychopath who knows where you live.',
            'Don’t worry if it doesn’t work right. If everything did, you’d be out of a job.',
            'If debugging is the process of removing software bugs, then programming must be the process of putting them in.',
            'Programmer: A machine that turns coffee into code.',
            'When I wrote this code, only God and I understood what I did. Now only God knows.',
            'How many programmers does it take to change a light bulb? None, that’s a hardware problem.',
            'Copy-and-Paste was programmed by programmers for programmers actually.',
            'Software and cathedrals are much the same — first we build them, then we pray.',
            'Q: If 1 is true and 0 is false? A: 1.',
            'I have not failed. I’ve just found 10,000 ways that won’t work.',
            'Crash programs fail because they are based on theory that, with nine women pregnant, you can get a baby in a month.'
        ])
    def next(self):
        return next(self.msg)
    

@decorateAllFunctionInClass(log_and_suppress_error(background_service_log))
class BackgroundTask:
    """
    A class to run the backgroud task
        - enrollment loop
        - weight polling loop
        - station disconnect threshold loop 
        - rmq

    """
    event_loop : Optional[AbstractEventLoop]= None
    wms_rmq = None
    notification_msg = NotificationMsg()
    retry_loop: Dict[str,Future] = dict()


    @classmethod
    def run(cls):
        """
        Start the event loop 
        
        Connect rmq if needed 
        """
        threading.current_thread().setName('background_task') 

        cls.event_loop = cls.get_or_create_eventloop()

        cls.set_up_rmq()
        cls.rerun_station_mode()
        cls.run_schedule()
        cls.event_loop.create_task(cls.notify_system(),name = 'notify loop')
        cls.event_loop.run_forever()

    @classmethod
    def create_task(cls, coro: Coroutine, name: str = None):
        """Create a task which will be executed in event loop.
        """

        def _create_task():
            return cls.event_loop.create_task(coro,
                name = name or coro.__qualname__
            )

        def _async_create_task():
            task = _create_task()
            future.set_result(task)

        if cls.is_current_loop():
            return _create_task()            

        future = cls.create_future()
        cls.event_loop.call_soon_threadsafe(
            _async_create_task
        )
        return future
     
    @classmethod
    def is_current_loop(cls):
        return cls.event_loop._thread_id == current_thread().ident

    @classmethod
    def get_or_create_eventloop(cls)->Optional[AbstractEventLoop]:
        try:
            return asyncio.get_event_loop()
        except RuntimeError as ex:
            if "There is no current event loop in thread" in str(ex):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                return asyncio.get_event_loop() 
    
    @classmethod
    def create_future(cls):
        return EventLoopFuture(loop=cls.event_loop)

    @classmethod
    def execute_event(cls, interval: float, func: Callable, *args, **kwargs):
        try:
            func(*args, **kwargs)
        except Exception as e:
            background_service_log.error(f'{e} while executing {func.__qualname__} with {args} and {kwargs}')
        finally:
            cls.event_loop.call_soon_threadsafe(
                cls.event_loop.create_task,
                cls.append_event(interval, func, *args, **kwargs)
            )

    @classmethod
    async def append_event(cls, interval: float, func: Callable, *args, **kwargs):
        await asyncio.sleep(interval)
        cls.execute_event(interval, func, *args, **kwargs)

    @classmethod
    def set_up_rmq(cls):

        def _init_queue(name: str, queue_size: int = 500, **kwargs):
            if _RMQ.queues.get(name):
                return

            _RMQ.init_background_queue(name, queue_size)
            _RMQ.declare_queue_robust(name, **kwargs)

        if Config.CUBE_RMQ:
            rmq_host, rmq_port, rmq_user, rmq_password = Config.get_cube_rmq_con()

            _RMQ.initialize(
                rmq_user,
                rmq_password,
                rmq_host,
                rmq_port,
                '/',
                'HCC',
                cls.event_loop)
            _RMQ.connect()

            if Config.HCC_INFO_Q: _init_queue(
                name = ec.RabbitMQQueue.HCC_MSG.value, 
                )
            if Config.TCBT_WS_Q: _init_queue(
                name = WebsocketNotification.name,
                durable = True,
                arguments = WebsocketNotification.arguments)
            if Config.CENTRALIZED_LOGGING_Q: _init_queue(
                name = Config.CENTRALIZED_LOGGING_Q_NAME,
                durable = True,
                arguments = {
                    'x-message-ttl': Config.CENTRALIZED_LOGGING_Q_TTL
                })
            
            if Config.WEBHOOK_Q: _init_queue(
                name = TeamsNotification.name,
                durable = True,
                arguments = TeamsNotification.arguments)

    @classmethod
    def reset_connection(cls):
        cls.event_loop.call_soon_threadsafe(
            cls.event_loop.create_task,
            _RMQ.reset_connection()
        )
    
    @classmethod
    def st_disconnect_threshold(cls,staiton_code:int):
        """
        To start the disconnect theshold loop 

        Add the coroutine task into event loop to run it

        Args:
            staiton_code (int): station code
        """
        
        from ..runtime import runtime
        from ..service_handler import disconnect_threshold
        
        disconnect_task = cls.create_task(disconnect_threshold(staiton_code), name='disconnect task')
        runtime.disconnect_threshold_list[staiton_code] = disconnect_task

    @classmethod
    def st_reconnect_before_threshold(cls,station_code:int):
        """
        To cancel the disconnect threshold loop when station reconnect befoer threshold

        Will call cancel_task() to stop the coroutine in the eventloop
        Args:
            station_code (int): station Code
        """
        from ..runtime import runtime

        running_task : EventLoopFuture = runtime.disconnect_threshold_list[station_code]
        runtime.disconnect_threshold_list.pop(station_code,None)
        running_task.cancel_task()

    @classmethod
    def start_station_mode(cls,station_code:int, mode:str):
        """
        To start the enrollment loop 

        Add the coroutine task into event loop to run it

        Args:
            staiton_code (int): station code
        """
        from .. import database as db
        from ..runtime import runtime
        from ..blueprints.route.client import HttpClient
        

        from ..service_handler import station_enroll_handler,enrollment_log

        db.st_db_func.update_station(station_code,dict(mode = mode))

        cprint(f"Start {mode} for ST {station_code}",'light_green',attrs=["underline"])
        enrollment_log.info(f"Start {mode} for ST {station_code}")

        if mode == ec.CubeStationMode.ENROLL.value:
            mode_task = cls.create_task(station_enroll_handler(station_code),name = 'mode_task')
            runtime.add_station_in_mode(station_code,mode_task)
       
        HttpClient.update_st_status(station_code)
        
    @classmethod
    def start_retry(cls,station_code:int,msg:str):
        """
        To start the retry message loop untill ack by station
        """
        from ..service_handler import retry_handler

        to_complete_msg = f"ACK,{msg[:-1]}"
        if to_complete_msg in cls.retry_loop:
            return 
        retry_task = cls.create_task(retry_handler(station_code,msg),name = f'retry_{msg}_task')
        cls.retry_loop[to_complete_msg] = retry_task
       
    @classmethod
    def stop_retry(cls,msg):
        if msg in cls.retry_loop:
            cls.retry_loop[msg].cancel_task()
            cls.retry_loop.pop(msg)


    @classmethod
    def stop_station_mode(cls,station_code:int):
        """
        To stop the enrollment loop when enrollment stop 

        Will call cancel_task() to stop the coroutine in the eventloop
        Args:
            station_code (int): station Code
        """
        from .. import database as db
        from ..runtime import runtime
        from ..blueprints.route.client import HttpClient
        from ..service_handler import enrollment_log
        
        station = db.st_db_func.get_station_by_code(station_code)
        cprint(f"Stop {station.mode} for ST {station_code}",'light_green',attrs=["underline"])
        enrollment_log.info(f"Stop {station.mode} for ST {station_code}")
        if station.mode == ec.CubeStationMode.ENROLL.value:
            current_mode_task: EventLoopFuture = runtime.get_station_in_mode_future(station_code)
            current_mode_task.cancel_task()
            runtime.remove_station_in_mode(station_code)
        db.st_db_func.update_station(station_code,dict(mode = ec.CubeStationMode.NORMAL.value))
        HttpClient.update_st_status(station_code)
        return 

    # @classmethod
    # def start_weight_polling(cls,station_code:int, storage_code:str):
    #     """
    #     To start the weight polling loop when requested

    #     Args:
    #         station_code (int): station code
    #         storage_code (str): bin number
    #     """
    #     import app.runtime as rt

    #     from app.service_handler import station_weight_handler

    #     weight_polling_task = cls.create_task(station_weight_handler(station_code,storage_code),name = f'ST{station_code} weight_polling_task')
    #     rt.runtime.weight_polling_stations[station_code] = weight_polling_task
        
    #     cprint(f"Start weight polling for ST {station_code}",'light_green',attrs=["underline"])

    # @classmethod
    # def stop_weight_polling(cls,station_code:int):
    #     """
    #     To stop the weight polling loop when requested


    #     Args:
    #         station_code (int): station code
    #     """
        
    #     import app.runtime as rt

    #     current_weight_polling_task = rt.runtime.weight_polling_stations[station_code]
        
    #     cprint(f"Ending weight polling for ST {station_code}",'light_green', attrs=["underline"])

    #     rt.runtime.weight_polling_stations.pop(station_code,None)

    #     current_weight_polling_task.cancel_task()
        return 

    @classmethod
    def open_station_service_door(cls,sd_id:int)->bool:

        from .. import database as db
        from ..runtime import runtime
        from ..common import SDMsgFormatter
        from ..service_handler import service_door_handler, send_tcp_msg

        sd = db.sd_db_function.get_sd_by_id(sd_id)

        st_list = sd.station_list
        int_st_list = [int(st) for st in st_list]

        db.sd_db_function.change_related_st_status(sd.id,True)

        if all(not db.st_db_func.get_station_by_code(st_code).is_active for st_code in int_st_list):
            send_tcp_msg(sd_id,SDMsgFormatter.open_door_msg(sd_id),target_module=ec.ModuleCode.SERVCIE_DOOR.value)
            return False
        
        service_door_task = cls.create_task(service_door_handler(sd_id),name = f'SD{sd_id} service door task')
        runtime.opening_sd[sd_id] = service_door_task

        return True

    @classmethod
    def rerun_station_mode(cls):
        from ..runtime import runtime
        
        for station in runtime.get_runtime_st_list():
            if station.mode == ec.CubeStationMode.ENROLL.value:
                cls.start_station_mode(station.code,ec.CubeStationMode.ENROLL.value)
            elif station.mode == ec.CubeStationMode.TRANSFER.value:
                cls.start_station_mode(station.code,ec.CubeStationMode.TRANSFER.value)

    @classmethod
    def run_schedule(cls):
        schedule.every().saturday.at("00:00").do(DelLog.execute)
        cls.execute_event(5,schedule.run_pending)
        # Start flow monitoring
        cls.start_flow_monitoring()

    @classmethod
    async def notify_system(cls):
        """
        Works like the heartbeat of HCC
        - if there is any error, print the error found
        - else will print the notification msg 
        """
    
        from ..runtime import runtime
        from .. import database as db
        from .displayFormatter import formatString
        from ..server.station_server import tcp_server_log,close_socket_connection

        while True:
            await asyncio.sleep(10)
            st_under_maint = []
            st_I_processing = []
            st_I_paused = []

            for station in runtime.get_runtime_st_list():

                if station.type == ec.CubeStationType.I.value and db.st_mov_db_func.station_is_processing(station.code):
                    st_I_processing.append(f'ST{station.code}')

                if station.type == ec.CubeStationType.I.value and station.event_queue.is_paused:
                    st_I_paused.append(f'ST{station.code}')

                # disconnect connection when a sconnection not sending heartbeat for more than threshold seconds
                if Config.HEARTBEAT:
                    if station.is_connected and station.is_active:
                        if datetime.datetime.now() - station.last_ping_time > datetime.timedelta(seconds=Config.HEARTBEAT_LOSS_THRESHOLD):
                            tcp_server_log.error(f'ST{station.code} heartbeat loss')
                            sokcet_conn = runtime.runtime_tcp_client[station.connection].conn
                            close_socket_connection(sokcet_conn)

            # check dead thread
            running_threads = [t.getName() for t in threading.enumerate()]
            missing_threads = [t for t in expected_running_thread if t not in running_threads]


            if st_under_maint:
                print(formatString("NOTI",colored(f"{','.join(st_under_maint)} is currently under maintenance.",on_color='on_yellow',attrs=['bold'])))
            if st_I_processing:
                print(formatString("NOTI",colored(f"I station {','.join(st_I_processing)} is currently processing.",on_color='on_yellow',attrs=['bold'])))
            if st_I_paused:
                print(formatString("NOTI",colored(f"I station {','.join(st_I_processing)} event queue is currently paused.",on_color='on_yellow',attrs=['bold'])))
            if missing_threads:
                print(formatString("NOTI",colored(f"{missing_threads} thread is dead, please check the error asap",on_color='on_red',attrs=['bold'])))
            if not any([st_under_maint,st_I_processing,missing_threads,st_I_paused]):
                print(formatString("NOTI",colored(cls.notification_msg.next(),on_color='on_green',attrs=['bold'])))

    @classmethod
    def flow_monitoring_task(cls):
        """Background task to monitor flows and handle conditional logic"""
        from ..service_handler.flow_monitor import FlowMonitor
        from ..runtime import runtime
        from .. import database as db
        from ..enum_collections import CubeStationType, OrderStatus

        try:
            # Check for flows that need conditional logic handling
            for flow_request in runtime.flow_tracker.active_requests.values():

                # Handle station type I conditional logic
                if flow_request.current_stage == runtime.FlowStage.TC_REQUEST_RECEIVED:
                    station = db.st_db_func.get_station_by_code(flow_request.station_code)

                    if station.type == CubeStationType.I.value:
                        # Check if station is ready for processing
                        if not flow_request.is_waiting:
                            # Apply station type I logic
                            if FlowMonitor.handle_station_type_i_logic(
                                flow_request.station_code,
                                flow_request.tc_job_id,
                                flow_request.storage_code
                            ):
                                continue

                    # Check bin criteria for all stations
                    if not flow_request.is_waiting:
                        if not FlowMonitor.handle_bin_criteria_check(
                            flow_request.station_code,
                            flow_request.tc_job_id,
                            flow_request.storage_code
                        ):
                            continue

                    # If not waiting and criteria met, proceed to R protocol
                    if not flow_request.is_waiting:
                        FlowMonitor.update_flow_stage(
                            flow_request.tc_job_id,
                            runtime.FlowStage.HCC_R_PROTOCOL_SENT,
                            "Ready to send R protocol to PLC"
                        )

                # Check for waiting flows that can now proceed
                elif flow_request.is_waiting:
                    can_proceed = False

                    if "station ready" in flow_request.waiting_reason.lower():
                        # Check if station is now ready
                        station = db.st_db_func.get_station_by_code(flow_request.station_code)
                        if station.is_active and not station.is_maintenance:
                            can_proceed = True

                    elif "bins to be processed" in flow_request.waiting_reason.lower():
                        # Check if unprocessed bins are now processed
                        unprocessed_count = db.st_order_db_func.get_num_of_bin_in_station_pre_processed(flow_request.station_code)
                        if unprocessed_count == 0:
                            can_proceed = True

                    if can_proceed:
                        FlowMonitor.clear_flow_waiting(flow_request.tc_job_id)
                        FlowMonitor.update_flow_stage(
                            flow_request.tc_job_id,
                            runtime.FlowStage.HCC_R_PROTOCOL_SENT,
                            "Conditional logic satisfied, proceeding with R protocol"
                        )

            # Cleanup old flows periodically
            FlowMonitor.cleanup_old_flows()

        except Exception as e:
            background_service_log.error(f"Flow monitoring task error: {e}")

    @classmethod
    def start_flow_monitoring(cls):
        """Start the flow monitoring background task"""
        cls.execute_event(30, cls.flow_monitoring_task)  # Run every 30 seconds

class EventLoopFuture(Future):

    def cancel_task(self):
        """Used to cancel a task created by another thread
        """
        def _callback(future: Future):
            with suppress(CancelledError):
                task: Task = future.result()
                task.cancel()
        self.add_done_callback(_callback)
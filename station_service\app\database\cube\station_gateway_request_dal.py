import datetime

from typing import List
from sqlalchemy import desc

from ... import app,DBMonitor,db
from ... import common as common,models as model,enum_collections as ec

db_log = common.LogManager('cube_db_error',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_log))
class StationGatewayReqSQLAlchemyQueries:
        
    @DBMonitor.retry_session
    def add_new_gateway_req(self,data:dict):
        with app.app_context():
            request_event = model.StationGatewayReq(
                station_code = data.get('station_code'),
                type = data.get('type'),
                tc_job_id = data.get('tc_job_id'),
                storage_code = data.get('storage_code'),
                position= data.get('position'),
                event = data.get('event'),
                entity = data.get('entity'),
                status = data.get('status'), 
                created_at=datetime.datetime.now(),
                updated_at=datetime.datetime.now()
            )
            curr_session = db.session
            curr_session.add(request_event)
            curr_session.commit()

            req_event_id = request_event.id

            return req_event_id
    
    @DBMonitor.retry_session
    def get_gateway_req(self,filter_dict:dict)->model.StationGatewayReq:
        with app.app_context():
            curr_session = db.session
            req = curr_session.query(model.StationGatewayReq).filter_by(**filter_dict).first()
            return req
        
    @DBMonitor.retry_session
    def get_gateway_reqs(self,filter_dict:dict)->List[model.StationGatewayReq]:
        with app.app_context():
            curr_session = db.session
            gateway_reqs = curr_session.query(model.StationGatewayReq).filter_by(**filter_dict).all()
            return gateway_reqs
    
    @DBMonitor.retry_session
    def get_broadcastable_gateway_reqs(self,station_code:int = None)->List[model.StationGatewayReq]:
        with app.app_context():
            curr_session = db.session
            if station_code is not None:
                gateway_reqs = curr_session.query(model.StationGatewayReq).filter(model.StationGatewayReq.status.in_([ec.OrderStatus.READY.value,
                                                                                                                    ec.OrderStatus.CANCELED.value]))\
                                                                        .filter_by(canceled_ack = False,station_code=station_code).all()
            else:
                gateway_reqs = curr_session.query(model.StationGatewayReq).filter(model.StationGatewayReq.status.in_([ec.OrderStatus.READY.value,
                                                                                                                    ec.OrderStatus.CANCELED.value]))\
                                                                        .filter_by(canceled_ack = False).all()
            return gateway_reqs
        
    @DBMonitor.retry_session
    def get_latest_gateway_req(self,filter_dict:dict)->model.StationGatewayReq:
        with app.app_context():
            curr_session = db.session
            req = curr_session.query(model.StationGatewayReq).filter_by(**filter_dict).order_by(desc('created_at')).first()
            return req
        
    @DBMonitor.retry_session
    def update_gateway_req(self,filter_dict:int,update:dict)->int:
        with app.app_context():
            curr_session = db.session
            req:model.StationGatewayReq = curr_session.query(model.StationGatewayReq).filter_by(**filter_dict).first()
            if req:
                for k,v in update.items():
                    req.__setattr__(k,v)
                req.updated_at = datetime.datetime.now()
                req_id = req.id
                curr_session.commit()
                return req_id
            else:
                db_log.error(f'No gateway request with id {str(filter_dict)} found')
                return None
    

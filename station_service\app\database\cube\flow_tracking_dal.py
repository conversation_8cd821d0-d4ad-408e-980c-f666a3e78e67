import datetime
from typing import List, Dict, Optional
from sqlalchemy import desc, and_

from .. import db_log
from ... import app, db, models as model
from ...common import log_and_suppress_error
from ...database.db_monitor import DBMonitor
from ...enum_collections import OrderStatus


@log_and_suppress_error(db_log)
class FlowTrackingDAL:
    """Data Access Layer for flow tracking operations"""

    @DBMonitor.retry_session
    def create_flow_tracking(self, data: dict) -> int:
        """Create a new flow tracking record"""
        with app.app_context():
            flow_tracking = model.FlowTracking(
                request_id=data.get('request_id'),
                station_code=data.get('station_code'),
                storage_code=data.get('storage_code'),
                tc_job_id=data.get('tc_job_id'),
                position=data.get('position'),
                action=data.get('action'),
                current_stage=data.get('current_stage'),
                is_stuck=data.get('is_stuck', False),
                stuck_reason=data.get('stuck_reason'),
                is_waiting=data.get('is_waiting', False),
                waiting_reason=data.get('waiting_reason'),
                retry_count=data.get('retry_count', 0),
                total_elapsed_minutes=data.get('total_elapsed_minutes', 0.0),
                stage_elapsed_minutes=data.get('stage_elapsed_minutes', 0.0),
                created_at=datetime.datetime.now(),
                updated_at=datetime.datetime.now()
            )
            
            curr_session = db.session
            curr_session.add(flow_tracking)
            curr_session.commit()
            
            return flow_tracking.id

    @DBMonitor.retry_session
    def update_flow_tracking(self, request_id: str, update_data: dict) -> bool:
        """Update flow tracking record"""
        with app.app_context():
            curr_session = db.session
            flow_tracking = curr_session.query(model.FlowTracking)\
                                      .filter_by(request_id=request_id)\
                                      .first()
            
            if flow_tracking:
                for key, value in update_data.items():
                    if hasattr(flow_tracking, key):
                        setattr(flow_tracking, key, value)
                
                flow_tracking.updated_at = datetime.datetime.now()
                curr_session.commit()
                return True
            
            return False

    @DBMonitor.retry_session
    def complete_flow_tracking(self, request_id: str) -> bool:
        """Mark flow tracking as completed"""
        with app.app_context():
            curr_session = db.session
            flow_tracking = curr_session.query(model.FlowTracking)\
                                      .filter_by(request_id=request_id)\
                                      .first()
            
            if flow_tracking:
                flow_tracking.completed_at = datetime.datetime.now()
                flow_tracking.updated_at = datetime.datetime.now()
                curr_session.commit()
                return True
            
            return False

    @DBMonitor.retry_session
    def get_flow_tracking_by_request_id(self, request_id: str) -> Optional[model.FlowTracking]:
        """Get flow tracking by request ID"""
        with app.app_context():
            curr_session = db.session
            return curr_session.query(model.FlowTracking)\
                              .filter_by(request_id=request_id)\
                              .first()

    @DBMonitor.retry_session
    def get_flow_tracking_by_tc_job_id(self, tc_job_id: int) -> Optional[model.FlowTracking]:
        """Get flow tracking by TC job ID"""
        with app.app_context():
            curr_session = db.session
            return curr_session.query(model.FlowTracking)\
                              .filter_by(tc_job_id=tc_job_id)\
                              .filter(model.FlowTracking.completed_at.is_(None))\
                              .first()

    @DBMonitor.retry_session
    def get_stuck_flows(self, threshold_minutes: int = 1) -> List[model.FlowTracking]:
        """Get flows that are stuck (processing longer than threshold)"""
        with app.app_context():
            curr_session = db.session
            threshold_time = datetime.datetime.now() - datetime.timedelta(minutes=threshold_minutes)
            
            return curr_session.query(model.FlowTracking)\
                              .filter(model.FlowTracking.completed_at.is_(None))\
                              .filter(model.FlowTracking.updated_at < threshold_time)\
                              .order_by(model.FlowTracking.updated_at.asc())\
                              .all()

    @DBMonitor.retry_session
    def get_waiting_flows(self) -> List[model.FlowTracking]:
        """Get flows that are in waiting state"""
        with app.app_context():
            curr_session = db.session
            return curr_session.query(model.FlowTracking)\
                              .filter_by(is_waiting=True)\
                              .filter(model.FlowTracking.completed_at.is_(None))\
                              .order_by(model.FlowTracking.updated_at.asc())\
                              .all()

    @DBMonitor.retry_session
    def get_flows_by_station(self, station_code: int) -> List[model.FlowTracking]:
        """Get all active flows for a station"""
        with app.app_context():
            curr_session = db.session
            return curr_session.query(model.FlowTracking)\
                              .filter_by(station_code=station_code)\
                              .filter(model.FlowTracking.completed_at.is_(None))\
                              .order_by(model.FlowTracking.updated_at.desc())\
                              .all()

    @DBMonitor.retry_session
    def cleanup_old_flows(self, max_age_hours: int = 24) -> int:
        """Clean up old completed flows"""
        with app.app_context():
            curr_session = db.session
            cutoff_time = datetime.datetime.now() - datetime.timedelta(hours=max_age_hours)
            
            deleted_count = curr_session.query(model.FlowTracking)\
                                      .filter(model.FlowTracking.completed_at < cutoff_time)\
                                      .delete()
            
            curr_session.commit()
            return deleted_count

    @DBMonitor.retry_session
    def create_stage_history(self, data: dict) -> int:
        """Create a stage history record"""
        with app.app_context():
            stage_history = model.FlowStageHistory(
                request_id=data.get('request_id'),
                stage=data.get('stage'),
                entered_at=data.get('entered_at', datetime.datetime.now()),
                timeout_at=data.get('timeout_at'),
                notes=data.get('notes')
            )
            
            curr_session = db.session
            curr_session.add(stage_history)
            curr_session.commit()
            
            return stage_history.id

    @DBMonitor.retry_session
    def get_stage_history(self, request_id: str) -> List[model.FlowStageHistory]:
        """Get stage history for a request"""
        with app.app_context():
            curr_session = db.session
            return curr_session.query(model.FlowStageHistory)\
                              .filter_by(request_id=request_id)\
                              .order_by(model.FlowStageHistory.entered_at.asc())\
                              .all()

    @DBMonitor.retry_session
    def get_healthcheck_data(self, threshold_minutes: int = 1) -> List[Dict]:
        """Get comprehensive healthcheck data for flows"""
        with app.app_context():
            curr_session = db.session
            threshold_time = datetime.datetime.now() - datetime.timedelta(minutes=threshold_minutes)
            
            # Get stuck flows
            stuck_flows = curr_session.query(model.FlowTracking)\
                                    .filter(model.FlowTracking.completed_at.is_(None))\
                                    .filter(model.FlowTracking.updated_at < threshold_time)\
                                    .order_by(model.FlowTracking.updated_at.asc())\
                                    .all()
            
            healthcheck_data = []
            for flow in stuck_flows:
                flow_data = {
                    'request_id': flow.request_id,
                    'station_code': flow.station_code,
                    'storage_code': flow.storage_code,
                    'tc_job_id': flow.tc_job_id,
                    'action': flow.action,
                    'current_stage': flow.current_stage,
                    'is_stuck': flow.is_stuck,
                    'stuck_reason': flow.stuck_reason,
                    'is_waiting': flow.is_waiting,
                    'waiting_reason': flow.waiting_reason,
                    'total_elapsed_minutes': flow.total_elapsed_minutes,
                    'stage_elapsed_minutes': flow.stage_elapsed_minutes,
                    'retry_count': flow.retry_count,
                    'created_at': flow.created_at.isoformat() if flow.created_at else None,
                    'updated_at': flow.updated_at.isoformat() if flow.updated_at else None
                }
                healthcheck_data.append(flow_data)
            
            return healthcheck_data

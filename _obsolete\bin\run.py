# import os
# import sys
# import subprocess
# import json
# import platform
# import signal
# from simple_chalk import redBright
# import cProfile


# def init():
#     killProcess()

#     data = dict()
#     data['pid'] = []

#     proc = subprocess.Popen([sys.executable, os.getcwd() + "/../wsgi.py"])
#     data['pid'].append(proc.pid)

#     updateJson(data)


# def killProcess():
#     try:
#         with open('pid.json') as f:
#             pidObj = json.load(f)
#         if len(pidObj) > 0:
#             if 'pid' in pidObj:
#                 for pid in pidObj['pid']:
#                     if platform.system() != 'Windows':
#                         print('passing in pid: ', pid)
#                         kill = subprocess.Popen(os.getcwd() + '/kill_port.sh '+str(pid), shell=True, executable='/bin/bash')
#                     else:
#                         kill = subprocess.Popen('kill_port_windows.bat', shell=True)

#     except Exception as e:
#         print(redBright(e))


# def updateJson(data):
#     with open('pid.json', 'w') as f:
#         json.dump(data, f)


# if __name__ == "__main__":
#     init()

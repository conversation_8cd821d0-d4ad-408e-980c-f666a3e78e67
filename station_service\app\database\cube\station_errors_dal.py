import datetime

from typing import List
from sqlalchemy import asc, Date, cast, desc
from sqlalchemy.orm.attributes import flag_modified

from ... import app,DBMonitor,db
from ... import common as common,models as model

db_log = common.LogManager('cube_db_error',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_log))
class StationErrorSQLAlchemyQueries:
    
    @DBMonitor.retry_session
    def create_station_error_message(self,station_code,error_name)->int:
        with app.app_context():
            station_error = model.StationErrors(
                station_code = station_code,
                error_name = error_name,
                data = {station_code:station_code},
                ack = False,
                resolved = False,
                ack_resolved = False,
                created_at = datetime.datetime.now(),
                updated_at = datetime.datetime.now()
            )
            curr_session = db.session
            curr_session.add(station_error)
            curr_session.commit()

            error_id = station_error.id
            return error_id
    
    @DBMonitor.retry_session
    def find_station_err_msg(self,filter_dict:dict)->model.StationErrors:
        with app.app_context():
            curr_session = db.session
            station_error_msg = curr_session.query(model.StationErrors).filter_by(**filter_dict).first()
            return station_error_msg
        
    @DBMonitor.retry_session
    def find_station_err_msges(self,filter_dict:dict)->List[model.StationErrors]:
        with app.app_context():
            curr_session = db.session
            station_error_msges = curr_session.query(model.StationErrors).filter_by(**filter_dict).order_by('created_at').all()
            return station_error_msges
        
    @DBMonitor.retry_session
    def update_station_err_msg(self,err_msg_id:int,update:dict)->int:
        with app.app_context():
            curr_session = db.session
            err_msg:model.StationErrors= curr_session.query(model.StationErrors).filter(model.StationErrors.id == err_msg_id).first()
            if err_msg:
                for k,v in update.items():
                    err_msg.__setattr__(k,v)
                err_msg.updated_at = datetime.datetime.now()
                updated_err_msg_id = err_msg.id
                curr_session.commit()
                return updated_err_msg_id
            else:
                db_log.error(f'No station error message id with message id {err_msg_id} found')
                return None
            
    @DBMonitor.retry_session
    def update_station_error_msg_payload(self,err_msg_id:int,new_data):
        with app.app_context():
            curr_session = db.session
            err_msg = curr_session.query(model.StationErrors).filter(model.StationErrors.id == err_msg_id).first()
            err_msg.data = new_data
            flag_modified(err_msg, 'data')
            err_msg.updated_at = datetime.datetime.now()
            curr_session.commit()
        
    @DBMonitor.retry_session
    # for tc-dashboard use
    def find_distinct_station_error(self,station_code:int)->List[model.StationErrors]:
        with app.app_context():
            cur_session = db.session
            station_error_msg= cur_session.query(model.StationErrors).filter(model.StationErrors.station_code == station_code, model.StationErrors.resolved == False).order_by(asc(model.StationErrors.error_name)).distinct(model.StationErrors.error_name).all()
            return station_error_msg

    # for analysis use
    @DBMonitor.retry_session
    def find_station_error_on_date(self,station_code:int,from_date:datetime.datetime,to_date:datetime.datetime)->List[model.StationErrors]:
        with app.app_context():
            curr_session= db.session
            st_errors = curr_session.query(model.StationErrors).filter(cast(model.StationErrors.created_at,Date)>= from_date).filter(cast(model.StationErrors.created_at,Date)<= to_date).filter_by(station_code = station_code).order_by(desc('created_at')).all()
            return st_errors

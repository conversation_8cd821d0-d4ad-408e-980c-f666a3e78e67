FROM python:3.11-alpine

RUN apk add gcc build-base linux-headers libffi-dev git openssh-client
RUN addgroup -S uwsgi && adduser -S -G uwsgi uwsgi

WORKDIR /app

COPY requirements.txt .

RUN mkdir -p -m 0700 ~/.ssh && ssh-keyscan github.com >> ~/.ssh/known_hosts
RUN python -m pip install --upgrade pip
RUN \
 apk add --no-cache postgresql-libs && \
 apk add --no-cache --virtual .build-deps gcc musl-dev postgresql-dev && \
 apk --purge del .build-deps
RUN --mount=type=ssh pip3 install -r requirements.txt --no-cache-dir


COPY . .

CMD [ "python3","-u", "wsgi.py"]


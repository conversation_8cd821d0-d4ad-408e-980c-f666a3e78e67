import os


from termcolor import colored
from datetime import datetime
from urllib.parse import quote_plus


from app.enum_collections import EnvironmentType

try:
    class Config:
        FLASK_APP = os.getenv('FLASK_APP')
        FLASK_ENV = os.getenv('FLASK_ENV')
        SECRET_KEY = os.getenv('SECRET_KEY')
        SERVICE_AREA = os.getenv('SERVICE_AREA')
        HEARTBEAT = bool(os.getenv('HEARTBEAT'))
        SERVICE_START_DATE = datetime.today().strftime('%Y-%m-%d')
        # RMQ
        ASRS_RMQ = bool(os.getenv('ASRS_RABBITMQ'))
        HCC_INFO_Q = bool(os.getenv('WMS_QUEUE'))
        TCBT_WS_Q = bool(os.getenv('TC_BT_QUEUE'))
        WEBHOOK_Q = bool(os.getenv('TEAMS_WEBHOOK_QUEUE'))
        # SQL
        SQLALCHEMY_TRACK_MODIFICATIONS = os.getenv('SQLALCHEMY_TRACK_MODIFICATIONS')
        SQLALCHEMY_ECHO = 0
        # ASRS
        MOCK_ASRS_ORDER = bool(os.getenv('DRYRUN_ASRS'))  # Makes order go in and out from input area
        SKIP_ASRS_VALIDATION = bool(os.getenv('SKIP_ASRS_VALIDATION'))

        @classmethod
        def get_env_type(cls):
            if cls.FLASK_ENV == EnvironmentType.DEVELOPMENT.value:
                return "DEV"
            elif cls.FLASK_ENV == EnvironmentType.PRODUCTION.value:
                return "PROD"

        @classmethod
        def get_flask_conn(cls):
            env_type = cls.get_env_type()
            cls.FLASK_CONN = os.getenv(f'{env_type}_ASRS_FLASK_HOST'),os.getenv(f'ASRS_FLASK_RUN_PORT')
            return cls.FLASK_CONN
        
        # --------- db -------------
        @classmethod
        def get_sqlalchemy_db_uri(cls):
            
            env_type = cls.get_env_type()
            cls.SQLALCHEMY_DATABASE_URI = 'postgresql://' + os.getenv(f'{env_type}_DB_USER') + ":" + quote_plus(os.getenv(f'{env_type}_DB_PASSWORD')) + "@" + \
                os.getenv(f'{env_type}_DB_HOST') + ":" + os.getenv(f'{env_type}_DB_PORT') + "/" + os.getenv(f'{env_type}_DB_NAME')
            return  cls.SQLALCHEMY_DATABASE_URI
        
        # --------- tcpip -------------
        @classmethod
        def get_asrs_tcpip(cls):
            from app.communications_provider.tcpip import TCPIPConnection
            
            env_type = cls.get_env_type()
            cls.ASRS_TCPIP = TCPIPConnection(
                os.getenv(f'{env_type}_ASRS_TCPIP_HOST'),
                int(os.getenv(f'{env_type}_ASRS_TCPIP_PORT'))
            )
            return cls.ASRS_TCPIP
        
        # ------------- client -------------
        @classmethod
        def get_mediator_host(cls):
            
            env_type = cls.get_env_type()
            cls.MEDIATOR_HOST = 'http://'+os.getenv(f'{env_type}_MEDIATOR')
            return cls.MEDIATOR_HOST
       
        @classmethod
        def get_asrs_rmq_con(cls):
            env_type = cls.get_env_type()
            cls.ASRS_RMQ_CON = \
                os.getenv(f'{env_type}_ASRS_RABBITMQ_HOST'),\
                os.getenv(f'{env_type}_ASRS_RABBITMQ_PORT'),\
                os.getenv(f'{env_type}_ASRS_RABBITMQ_USER'),\
                os.getenv(f'{env_type}_ASRS_RABBITMQ_PASSWORD')
            return cls.ASRS_RMQ_CON

except Exception as e:
    print(colored(f'Please check your asrs .env config', 'light_red'))

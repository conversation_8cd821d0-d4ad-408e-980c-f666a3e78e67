from app import db


class Asrs_order(db.Model):
    id: int
    order_id: int
    asrs_id: int
    x: int
    y: int
    z: int
    type: str
    method: str
    status: str
    pallet_id: str
    station_id: int
    created_at: str
    updated_at: str

    __tablename__ = 'asrs_order'
    id = db.Column(
        db.Integer,
        primary_key=True
    )

    order_id = db.Column(
        db.Integer,
        index=False,
        nullable=False,
        unique=True
    )

    asrs_id = db.Column(
        db.Integer,
        index=False,
        nullable=False,
        unique=False
    )

    x = db.Column(
        db.Integer,
        index=False,
        nullable=False
    )

    y = db.Column(
        db.Integer,
        index=False
    )

    z = db.Column(
        db.Integer,
        index=False
    )

    type = db.Column(
        db.String(128),
        index=False
    )

    method = db.Column(
        db.String(128),
        index=False
    )

    status = db.Column(
        db.String(128),
        index=False,
        nullable=False
    )

    pallet_id = db.Column(
        db.String(128),
        index=False
    )

    station_id = db.Column(
        db.Integer,
        index=False,
        nullable=False,
        unique=False
    )

    created_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

    updated_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

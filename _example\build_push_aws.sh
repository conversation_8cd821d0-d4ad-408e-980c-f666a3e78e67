#!/bin/bash

if [ -z "$1" ]; then
    echo "Usage: $0 <tag>"
    exit 1
fi

tag="$1"

# latest : subang
# vitrox : vitrox
# penta : penta

aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com

echo "============Build on platform $OSTYPE ================"
if [[ "$OSTYPE" == "darwin"* ]]; then 
    docker build --platform linux/amd64 --ssh default="$HOME/.ssh/id_rsa" -t cube-ihub-hcc:$tag .
else 
    # docker build -t cube-ihub-hcc:$tag .
    docker buildx build --ssh default="$HOME\.ssh\id_rsa" -t cube-ihub-hcc:$tag .
fi

echo "===========Tag image to $tag=========="
docker tag cube-ihub-hcc:$tag 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com/cube-ihub-hcc:$tag

echo "===========Push image to ECR============"
docker push 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com/cube-ihub-hcc:$tag

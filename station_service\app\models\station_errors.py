from dataclasses import dataclass
from .. import db

@dataclass
class StationErrors(db.Model):
    id:int
    station_code:int
    error_name:str
    data:str
    ack: bool
    resolved:bool    
    created_at:str
    updated_at:str
    
    __tablename__ = "station_errors"
    
    id = db.Column(
        db.Integer,
        primary_key=True
    )
    
    station_code = db.Column(
        db.Integer,
        index=False,
        nullable=True
    )
    
    error_name = db.Column(
        db.String(10),
        index=False,
        nullable=True
    )
    
    data = db.Column(
        db.JSON,
        index=False,
        nullable=False
    )
    
    ack = db.Column(
        db.<PERSON>,
        index=False,
        unique=False,
        nullable=True,
        default = False
    )
    
    resolved = db.Column(
        db.Bo<PERSON>,
        index=False,
        unique=False,
        nullable=True,
        default = False
    )
    
    ack_resolved = db.Column(
        db.<PERSON>,
        index=False,
        unique=False,
        nullable=True,
        default = False
    )
    
    created_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

    updated_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    ) 

    def as_dict(self):

        import datetime
        
        result = {}
        for c in self.__table__.columns:
            value = getattr(self, c.name)
            if isinstance(value,datetime.datetime):
                result[c.name] = value.isoformat()
            else:
                result[c.name] = value

        return result
    
    def as_dict_for_record(self):

        import datetime

        result = {}
        for c in self.__table__.columns:
            if c.name in ['station_code','error_name', 'data','created_at']:
                value = getattr(self, c.name)
                if isinstance(value, datetime.datetime):
                    result[c.name] = value.isoformat()
                else:
                    if c.name == 'data':
                        data = value['data']
                        for k,v in data.items():
                            result[k] = v
                        continue
                    result[c.name] = value

        return result

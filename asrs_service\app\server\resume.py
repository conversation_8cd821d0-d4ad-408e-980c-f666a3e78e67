from termcolor import colored

from .. import database as db,models as model,common as common,service_handler as handler,enum_collections as ec


resume_log = common.get_logger_console('asrs_resume')

async def resume_asrs():
        
    try:
        
        pending_orders = db.as_order_db_func.get_processing_order()
            
        if pending_orders:
            
            resume_log.info(colored(f'There are currently {len(pending_orders)} pending orders to resume. HardwareX will proceed to resume.','light_yellow'))
            
            for order in pending_orders:
                order: model.Asrs_order
                resume_log.info(colored(f'Start resuming order: {order.id}','light_green'))
                latest_job_for_order : model.Asrs_job = db.as_job_db_func.get_earliest_job_for_order(order.id)
                if latest_job_for_order: 
                    if latest_job_for_order.job_type == ec.AsrsJobType.PICK.value and  latest_job_for_order.ack == True:
                        handler.use_asrs(int(order.asrs_id))
                    if latest_job_for_order.job_type == ec.AsrsJobType.DROP.value:
                        handler.use_asrs(int(order.asrs_id))
                resume_order = handler.OrderHandler(order.order_id, order.id)
                resume_order.start_order()
                    
        pending_error_jobs = db.as_job_db_func.get_processing_error_jobs()
        
        if pending_error_jobs:
            resume_log.info(colored(f'There are currently {len(pending_error_jobs)} pending error jobs to resume. HardwareX will proceed to resume.','light_yellow'))
            
            for job in pending_error_jobs:
                job : model.Asrs_job
                msg = job.msg
                splited_msg = msg.split(',')
                asrs_id = splited_msg[1]
                resume_log.info(colored(f'Start resuming error job: {job.id}','light_green'))
                if job.job_type == ec.AsrsJobType.RECOVERY_DROP.value:
                    handler.use_asrs(int(asrs_id))
                    handler.asrs_recovery(int(asrs_id))
                if job.job_type == ec.AsrsJobType.PC_LOST_DATA.value and job.ack:
                    handler.use_asrs(int(asrs_id))

                if not job.ack:
                    resume_error_order  = handler.ErrorJobHandler(job.id,int(asrs_id))
                    resume_error_order.start_error_job()   


            
    except Exception as e:
        resume_log.error(colored(f'resume_asrs error. Exception thrown: {e}','light_red'))

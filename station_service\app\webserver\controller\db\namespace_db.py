import datetime

from sqlalchemy import text 
from flask_restx import Namespace,reqparse, Resource

from .... import common as common
from ... import api_routing as api_routing

from .... import api, app, DBMonitor,db

nsdb = Namespace(api_routing.UrlFolder.db, description="API to access db table")

@DBMonitor.retry_session
def exectue(sql_expression):
    curr_session = db.session
    result = curr_session.execute(sql_expression)
    curr_session.commit()
    return result


query_get_parser = reqparse.RequestParser()
query_get_parser.add_argument('sql_expression',required=True,type=str,location='args')
query_get_parser.add_argument('row_limit',required=False,type=int,default=50,location='args')

query_post_parser = reqparse.RequestParser()
query_post_parser.add_argument('sql_expression',required=True,type=str,location='json')

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsdb.route(api_routing.UrlPath.query)
class Query(Resource):
    @api.expect(query_get_parser)
    def get(self):
        '''
        Query to execute for READ
        '''
        with app.app_context():
            data = query_get_parser.parse_args(strict=True)

            sql_expression = data['sql_expression']
            row_limt = data['row_limit']

            limited_query = text(sql_expression + f" LIMIT {row_limt}")

            result = exectue(limited_query)

            rows = []
            for row in result:
                modified_row = {}
                for column, value in row.items():
                    if isinstance(value, datetime.datetime):
                        modified_row[column] = value.isoformat()
                    else:
                        modified_row[column] = value

                rows.append(modified_row)

            return common.StandardResponse.response(data=rows)
    
    @api.expect(query_post_parser)
    def post(self):
        '''
        Query to execute for UPDATE/DELETE
        '''
        with app.app_context():

            data = query_post_parser.parse_args(strict=True)

            sql_expression = data['sql_expression']

            result = exectue(text(sql_expression))

            if result.rowcount>0:
                return common.StandardResponse.response()
            else:
                return common.StandardResponse.response(False,message='No row affected from this query')


# Accumulate spend 8 hours on this script

name: UnitTest_TeamsNotification

# Controls when the workflow will run
on:
  # Triggers the workflow on push or pull request events but only ignore the master + main branch
  push:
    # branches-ignore:
    #   - 'master'
    #   - 'main'
    branches: 
      - master 
      - master-penta  
      - release
  pull_request:
    branches-ignore:
      # - 'feat/**'
    branches:
      - master 
      - master-penta  
      - release
  
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build_test:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v2

      # map branch to branch under id extract_branch
      - name: Extract branch name
        shell: bash
        run: | 
          echo "##[set-output name=branch;]$(echo ${GITHUB_REF#refs/heads/})"        
        id: extract_branch

      # Runs a single command using the runners shell
      - name: Run a one-line script
        run: echo Hello, world! 

      # Runs a set of commands using the runners shell
      - name: Run a multi-line script
        run: |
          echo Add other actions to build,
          echo test, and deploy your project.
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
          
      - name: Test with unittest
        id : unittest
        run: |

          DESC_STRING="CI pipeline ran on branch ${{ steps.extract_branch.outputs.branch }} at ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }} by ${{ github.actor }} was"
          echo "::set-output name=unittestdesc::$DESC_STRING"
          TEST_FLAG=true python -m unittest discover -s tests -p "test_*.py" -v
          echo "Exited with '$?'"
        env:
          ENVIRONMENT: development
          DEV_ZONE: C

        # continue-on-error: true

      - name: Notification Success    
        if: steps.unittest.outcome == 'success' && always()  
        run: |
          echo ${GITHUB_REF#refs/heads/}
          echo success
          echo "${{steps.unittest.outputs.unittestdesc}} ${{steps.unittest.outcome}}."
        # echo PR is ${{ github.event.pull_request.number }} , reviewer can ready to review PR.

      - name: Notification Failure
        if: steps.unittest.outcome != 'success' && always()  
        run: |
          echo failure
          echo "${{steps.unittest.outputs.unittestdesc}} ${{steps.unittest.outcome}}."
  
      - name : 'Send msg to teams'
        if: always()  
        uses: aliencube/microsoft-teams-actions@v0.8.0
        with :
          webhook_uri: ${{secrets.SECRET_MSTEAMS_WEBHOOK}}
          title: Github CI workflows - ${{steps.unittest.outcome}}
          summary: CI workflows unitest.
          text : "${{steps.unittest.outputs.unittestdesc}} ${{steps.unittest.outcome}}."
          theme_color: 0072C6
          sections: '[{ "CI": "Continuous Integration" }]'
          actions: '[{ "@type": "OpenUri", "name": "python-traffic-control", "targets": [{ "os": "default", "uri": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"  }] }]'









        

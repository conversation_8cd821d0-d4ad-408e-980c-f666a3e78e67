from typing import Iterator 
from flask_restx import reqparse

code_parser = reqparse.RequestParser()
code_parser.add_argument("code",required = True,type = int,location = 'args')

code_body_parser = reqparse.RequestParser()
code_body_parser.add_argument("code",required = True,type = int,location = 'json')

station_code_parser = reqparse.RequestParser()
station_code_parser.add_argument("station_code",required = True,type = int,location = 'args')

station_storage_code_parser = reqparse.RequestParser()
station_storage_code_parser.add_argument("station_code",required = True,type = int,location = 'args')
station_storage_code_parser.add_argument("storage_code",required = True,type = str,location = 'args')

station_storage_code_body_parser = reqparse.RequestParser()
station_storage_code_body_parser.add_argument("station_code",required = True,type = int,location = 'json')
station_storage_code_body_parser.add_argument("storage_code",required = True,type = str,location = 'json')

station_code_body_parser = reqparse.RequestParser()
station_code_body_parser.add_argument("station_code",required = True,type = int,location = 'json')

mock_body_parser = reqparse.RequestParser()
mock_body_parser.add_argument("station_code",required = True, type = int, location = 'json')
mock_body_parser.add_argument("storage_code",required = True, type = int, location = 'json')

mock_msg_body_parser = reqparse.RequestParser()
mock_msg_body_parser.add_argument("station_code",required = True, type = int, location = 'json')
mock_msg_body_parser.add_argument("message",required = True, type = str, location = 'json')


def get_choices_args(choices: Iterator[str]):
    """Return the dict that is used to construct keyword argument when
    calling flask_restful.reqparse.RequestParser.add_argument(...)
    method.

    Parameter
    ---------
    choices: an iterator of valid choices for an argument in str.
        Eg. When using enum, use <your enum>.__members__ for the enum's
        names and use <your enum>._value2member_map_ for the enum's
        values.

    Example
    -------
    >>> data_args = reqparse.RequestParser
    >>> data_args.add_argurment(
    ...     'zone',
    ...     **get_choices_args(Zone._value2member_map_)
    ... )

    Suppose an invalid choices is given, it will show in the response
    body under error.zone with msg:
        A|B|C only. The value '<invalid choice>' is not a valid choice
        for 'zone'.
    
    Return
    ------
    A dict with 2 key-value pairs.
    - choices: tuple of choices in str
    - help: help message in string
    """
    choices = tuple(choices) # prevent iterator exhaustion
    return {
        'choices': choices,
        # 'help': ' | '.join(str(c) for c in choices) + ' only.'
    }
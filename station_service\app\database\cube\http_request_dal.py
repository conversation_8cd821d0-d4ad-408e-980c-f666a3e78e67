import datetime

from typing import List

from ... import app,DBMonitor,db
from ... import common as common,models as model


db_log = common.LogManager('cube_db_error',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_log))
class HttpRequestSQLAlchemyQueries:
    
    @DBMonitor.retry_session
    def create_request(self,data:dict):
        with app.app_context():
            request = model.HttpRequests(**data)
            curr_session = db.session
            curr_session.add(request)
            curr_session.commit()
            return request.id
    
    @DBMonitor.retry_session
    def find_request(self,filter_dict:dict)->model.HttpRequests:
        with app.app_context():
            curr_session = db.session
            req = curr_session.query(model.HttpRequests).filter_by(**filter_dict).first()
            return req
    
    @DBMonitor.retry_session
    def find_requests(self,filter_dict:dict)->List[model.HttpRequests]:
        with app.app_context():
            curr_session = db.session
            requests = curr_session.query(model.HttpRequests).filter_by(**filter_dict).order_by(model.HttpRequests.id).all()
            return requests
        
    @DBMonitor.retry_session
    def update_request(self,req_id:int,update:dict)->int:
        with app.app_context():
            curr_session = db.session
            request:model.HttpRequests= curr_session.query(model.HttpRequests).filter(model.HttpRequests.id == req_id).first()
            if request:
                for k,v in update.items():
                    request.__setattr__(k,v)
                request.updated_at = datetime.datetime.now()
                request_id = request.id
                curr_session.commit()
                return request_id
            else:
                db_log.error(f'No request with id {req_id} found')
                return None
            






from datetime import datetime as dt


from app import app,db
from ...models import Asrs_order
from ... import common as common
from ... import enum_collections as ec

db_log = common.get_logger_console('asrs_db_error')

@common.decorateAllFunctionInClass(common.db_log_and_suppress_error(app,db_log))
class AsrsOrderSQLAlchemyQueries:
    
    def create_order(self,x, y, z, asrs_id, method, order_id, pallet_id, station_id, type = None)->int:
        curr_session = db.session
        order = Asrs_order(
            x=x,
            y=y,
            z=z,
            asrs_id=asrs_id,
            order_id=order_id,
            method=method,
            pallet_id=pallet_id,
            type = type,
            station_id=station_id,
            status=ec.OrderStatus.AVAILABLE.value,
            created_at=dt.now(),
            updated_at=dt.now(),
        )
        curr_session.add(order)
        curr_session.commit()

        asrs_order_id = order.id
        return asrs_order_id
    
    def get_order_by_id(self,id:int):
        curr_session = db.session
        order = curr_session.query(Asrs_order).filter_by(id=id).first()
        return order
    
    def get_order_by_order_id(self,order_id:int):
        # query by the wcs order id
        curr_session = db.session
        order = curr_session.query(Asrs_order).filter_by(order_id=order_id).first()
        return order
    
    def get_all_orders_by_status(self,status:str):
        curr_session = db.session
        orders = curr_session.query(Asrs_order).filter_by(status=status).all()
        return orders
    
    def update_order_status(self,id:int,status:str):
        curr_session = db.session
        order = curr_session.query(Asrs_order).filter_by(id=id).first()
        order.status = status
        order.updated_at = dt.now()
        curr_session.commit()
        return order


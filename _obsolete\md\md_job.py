from app import db

class MdJob(db.Model):
    id: int
    order_id: int
    job_type: str
    job_status: str
    station_id: int
    skycar_id: int
    requested_by: str
    action: str
    acknowledged: bool
    created_at: str
    updated_at: str

    __tablename__ = 'md_job'

    id = db.Column(
        db.Integer,
        primary_key=True
    )

    order_id = db.Column(
        db.Integer,
        nullable=True
        # db.<PERSON>ey('md_order.id')
    )

    job_type = db.Column(
        db.String(128),
        index=False,
        nullable=False
    )

    job_status = db.Column(
        db.String(128),
        index=False,
        nullable=False
    )

    station_id = db.Column(
        db.Integer,
        index=False,
    )

    skycar_id = db.Column(
        db.Integer,
        index=False,
        nullable=True
    )

    requested_by = db.Column(
        db.String(128),
        index=False,
        nullable=False
    )

    action = db.Column(
        db.String(128),
        index=False,
        nullable=False
    )

    ack = db.Column(
        db.<PERSON>,
        default=False,
        index=False
    )

    created_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

    updated_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

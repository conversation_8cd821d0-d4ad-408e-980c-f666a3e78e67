from .. import common as common,enum_collections as ec

validation_log = common.LogManager('cube_validation',display_console=True)

BIN_INFO_SPLIT_LENGTH       = 2
REQUEST_INFO_SPLIT_LENGTH   = 3
JOB_INFO_SPLIT_LENGTH       = 4


def validate_msg(msg:str)->bool:
    try:
        splited_msg = msg.split(",")
        
        # shortest protocol is len of 3
        if len(splited_msg)<2:
            validation_log.error(f'Invalid message from station : {msg}')
            return False
        
        if splited_msg[0] == 'ACK':
            if splited_msg[1] not in [ec.ModuleCode.STATION.value, ec.ModuleCode.SERVCIE_DOOR.value]:
                validation_log.error(f'Invalid message prefix : {msg}')
                return False
            protocol = splited_msg[3]
            plc_id = splited_msg[2]
            module_code = splited_msg[1]
        elif splited_msg[0] in [ec.ModuleCode.STATION.value, ec.ModuleCode.SERVCIE_DOOR.value]:
            protocol = splited_msg[2]
            plc_id = splited_msg[1]
            module_code = splited_msg[0]
        elif splited_msg[0] == ec.ModuleCode.EMO.value:
            protocol = splited_msg[2]
            plc_id = splited_msg[1]
            module_code = splited_msg[0]
            plc_id = None
        else:
            validation_log.error(f'Invalid message prefix : {splited_msg[0]}')
            return False
        
        # check station code
        if plc_id and not plc_id.isnumeric():
            validation_log.error(f'Invalid station code : {msg}')
            return False
        
        if protocol not in ec.CubesProtocol._value2member_map_:
            validation_log.error(f'Invalid protocol from station : {protocol}')
            return False
        
        if protocol == ec.CubesProtocol.ERROR.value:
            '''ST,1,ERROR,1G,2Z1,3Z3,4G......'''
            # check if non integer in error list
            error_list = splited_msg[3:]
            for error_name in error_list:
                if not ec.StationErrorModuleCode.ST_GENERAL.value in error_name and \
                   not ec.StationErrorModuleCode.ST_CONVEYOR_ZONE.value in error_name and \
                   not ec.StationErrorModuleCode.B_CONVEYOR_ZONE.value in error_name and \
                   not ec.StationErrorModuleCode.B_GENERAL.value in error_name:
                    validation_log.error(f'Invalid station error name in station error list : {msg}')
                    return False
                if ec.StationErrorModuleCode.ST_CONVEYOR_ZONE.value in error_name or  \
                      ec.StationErrorModuleCode.B_CONVEYOR_ZONE.value in error_name:
                    if ec.StationErrorModuleCode.ST_CONVEYOR_ZONE.value in error_name:
                        splited_error_name_list = error_name.split(ec.StationErrorModuleCode.ST_CONVEYOR_ZONE.value)
                        if len(splited_error_name_list) != 2 or splited_error_name_list[1] == "" or not splited_error_name_list[1].isnumeric() :
                            validation_log.error(f'Invalid station error name in station error list : {msg}')
                            return False
                    else:
                        splited_error_name_list = error_name.split(ec.StationErrorModuleCode.B_CONVEYOR_ZONE.value)
                        if len(splited_error_name_list) != 2 or splited_error_name_list[1] == "" or not splited_error_name_list[1].isnumeric() :
                            validation_log.error(f'Invalid station error name in station error list : {msg}')
                            return False
                    
                elif ec.StationErrorModuleCode.ST_GENERAL.value in error_name or \
                    ec.StationErrorModuleCode.B_GENERAL.value in error_name:
                    if ec.StationErrorModuleCode.ST_GENERAL.value in error_name:
                        splited_error_name_list = error_name.split(ec.StationErrorModuleCode.ST_GENERAL.value)
                        if len(splited_error_name_list) != 2 or splited_error_name_list[1] != "":
                            validation_log.error(f'Invalid station error name in station error list : {msg}')
                            return False
                    else:
                        splited_error_name_list = error_name.split(ec.StationErrorModuleCode.B_GENERAL.value)
                        if len(splited_error_name_list) != 2 or splited_error_name_list[1] != "":
                            validation_log.error(f'Invalid station error name in station error list : {msg}')
                            return False
                else:
                    validation_log.error(f'Invalid station error name in station error list : {msg}')
                    return False
        elif protocol == ec.CubesProtocol.W.value:
            '''ST,1,W,1234,2,5'''
            if not validate_storage_code(ec.CubesProtocol.W,msg,splited_msg[3]):
                return False
            # check if there is bin and weight given
            if not splited_msg[4].strip():
                validation_log.error(f'W message provide empty weight : {msg}')
                return False
            # check is weight can be convert to float
            try:
                float(splited_msg[4])
            except ValueError:
                validation_log.error(f'W message provide invalid weight : {msg}')
                return False
        elif protocol == ec.CubesProtocol.E.value:
            '''ST,1,E,|1234'''
            # check if there is at least two info in the enrolled bin info
            bin_info = splited_msg[3].split("|")
            if len(bin_info) != BIN_INFO_SPLIT_LENGTH:
                validation_log.error(f'Invalid E message, invalid bin info : {msg}')
                return False
            if not validate_storage_code(ec.CubesProtocol.E,msg,bin_info[1]):
                return False
        elif protocol == ec.CubesProtocol.Q.value:
            '''ST,1,Q,1234'''  
            if not validate_storage_code(ec.CubesProtocol.Q,msg,splited_msg[3]):
                return False
        elif protocol == ec.CubesProtocol.J.value:
            '''ST,1,J,1,0|3|C|1234'''
            # most of the job message can be recover as long as there is storage code
            job_info = splited_msg[4].split("|")
            # check job_info format
            if len(job_info) != JOB_INFO_SPLIT_LENGTH :
                validation_log.error(f"Job message failed protocol constraint, require contain {JOB_INFO_SPLIT_LENGTH} value. Unable to process this msg: {msg}")
                return False
            if not validate_storage_code(ec.CubesProtocol.J,msg,job_info[3]):
                return False
        elif protocol == ec.CubesProtocol.L.value:
            if splited_msg[0] == 'ACK':
                storage_code = splited_msg[4]
            else:
                storage_code = splited_msg[3]
            if not validate_storage_code(ec.CubesProtocol.L,msg,storage_code):
                return False
        elif protocol == ec.CubesProtocol.A.value or protocol == ec.CubesProtocol.D.value:
            '''
            ST,1,A,34612,0|D|1234
            ST,1,D,63467,6|P|1234
            '''
            request_info = splited_msg[4].split("|")
            request_protocol = ec.CubesProtocol.A if protocol == ec.CubesProtocol.A.value else ec.CubesProtocol.D
            # check if empty job id 
            if not splited_msg[3].strip():
                validation_log.error("Receive A/D message without job id ")
                return False 
            # check request info 
            if len(request_info) != REQUEST_INFO_SPLIT_LENGTH:
                validation_log.error(f'A/D message with invalid request info, require contain {REQUEST_INFO_SPLIT_LENGTH} value. : {msg}')
                return False
            #check request type
            if not request_info[1] in ec.StationRequestType._value2member_map_:
                validation_log.error(f'Invalid request type : {msg}')
                return False
            if not validate_storage_code(request_protocol,msg,request_info[2]):
                return False
        elif protocol == ec.CubesProtocol.U.value:
            '''ACK,ST,1,U,23545,6|P|1234'''
            update_info = splited_msg[5].split("|")
            
            # check if empty job id 
            if not splited_msg[4].strip():
                validation_log.error("Receive U message without job id ")
                return False 
            # check update info 
            if len(update_info) != REQUEST_INFO_SPLIT_LENGTH:
                validation_log.error(f'U message with invalid update info, require contain {REQUEST_INFO_SPLIT_LENGTH} value. : {msg}')
                return False
            #check update type
            if not update_info[1] in ec.StationRequestType._value2member_map_:
                validation_log.error(f'Invalid update type : {msg}')
                return False
            if not update_info[2].strip():
                validation_log.error(f'U message with empty storage code : {msg}')
                return False 
            if not update_info[2].isnumeric():
                validation_log.error(f'Storage should only contain number : {msg}')
                return False
        elif protocol == ec.CubesProtocol.M.value:
            '''ACK,ST,1,M,35235,0|3|C|1234;'''
            # check if empty job id 
            if not splited_msg[4].strip():
                validation_log.error("Receive ACK M message without job id ")
                return False    
        elif protocol == ec.CubesProtocol.DO.value:
            '''ACK,ST,11,DO,581823'''
            # check if empty job id 
            if not splited_msg[4].strip():
                validation_log.error("Receive ACK DO message without job id ")
                return False 
        elif protocol == ec.CubesProtocol.H.value:
            if not splited_msg[3].strip():
                validation_log.error("Receive heartbeat message with empty heartbeat ")
                return False
            if splited_msg[3] != ec.HeartBeat.HEARTBEAT_ASK.value:
                validation_log.error("Receive heartbeat message with wrong heartbeat ")
                return False
        elif protocol == ec.CubesProtocol.OW.value:
            condition = splited_msg[3]
            if condition not in ec.ToggleType._value2member_map_:
                validation_log.error(f'Invalid overweight protocol from station : {msg}')
                return False
        # service door checking 
        elif protocol == ec.CubesProtocol.OPEN.value:
            if splited_msg[3] != 'DONE':
                validation_log.error(f'Receive invalid service door open command')
                return False
        elif protocol == ec.CubesProtocol.S.value:
            if module_code == ec.ModuleCode.SERVCIE_DOOR.value:
                if splited_msg[3] not in [ec.ServiceDoorStatus.CLOSE.value,ec.ServiceDoorStatus.OPEN.value]:
                    validation_log.error(f'Receive invalid status message')
                    return False     
        elif protocol == ec.CubesProtocol.P.value:
            if splited_msg[3].upper() not in ec.StationPairingMsg._value2member_map_:
                validation_log.error(f'Receive invalid pairing messge')
                return False
        return True
    except Exception as e:
        validation_log.error(f"validate_msg throw error : {e}, please check the fomrat of the message : {msg}")
        return False
    
def validate_storage_code(protocol:ec.CubesProtocol,msg:str ,storage_code:str)->bool:
    # check if empty storage code is given
    if not storage_code.strip():
        validation_log.error(f'{protocol.value} message with empty storage code : {msg}')
        return False
    # check if storage code 0 is given
    if storage_code == '0':
        validation_log.error(f'{protocol.value} message with storage code 0 : {msg}')
        return False
    #check if is not integer
    if not storage_code.isnumeric():
        validation_log.error(f'Storage should only contain number : {msg}')
        return False
    return True
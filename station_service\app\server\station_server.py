import time
import socket
import threading

from threading import current_thread


from config import Config
from ..blueprints.route.client import HttpClient
from .. import DBMonitor,runtime as rt,database as db,common as common,enum_collections as ec,validation as validation

tcp_server_log = common.LogManager('cube_TCP_server',display_console=True,centralize_logging=True)


@common.log_and_suppress_error(tcp_server_log)
def start_server():        
    thread = current_thread()
    thread.setName('tcp_server')
    
    s = socket.socket()
    cube_tcpip = Config.get_cube_tcpip()
    s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    s.bind((cube_tcpip.host, cube_tcpip.port))
    s.listen(5)
    tcp_server_log.info(f"Starting up TCP/IP server at \tip: {cube_tcpip.host} port: {cube_tcpip.port}",color='light_green')
    handle_connection(s)

@common.log_and_suppress_error(tcp_server_log)
def handle_connection(s):
    while True:
        c, addr = s.accept()
        
        host:str = addr[0]
        port:int = addr[1]

        tcp_client = rt.TCPClient(host,port,c)

        if not on_tcp_connected(tcp_client):
            continue

        threading.Thread(target=handle_new_client, args=(tcp_client,), name=f'{addr}').start()


def handle_new_client(tcp_client:rt.TCPClient):
    try:
        from ..service_handler.cube_handler import MessageHandler

        while True:
            try:
                reply = tcp_client.conn.recv(1024)
                decodedMsg = reply.decode()

                if(len(decodedMsg) == 0):
                    # ping client to test connection, catch error if not connected
                    tcp_client.conn.send('ping'.encode())
                    continue             
                if DBMonitor.is_down:
                    tcp_server_log.warning('Unable to access database, will not process any incoming message')
                    continue

                for msg in decodedMsg.split(';'):
                    if msg != '' and msg != '\r\n': #Ignore \r\n so macos can test using telnet
                        msg = msg.replace(";", "")
                        splited_msg = msg.split(",")
                        
                        if not validation.validate_msg(msg):
                            continue
                        
                        msg_handler = MessageHandler.create_instance(msg,tcp_client,True)

                        if msg_handler.command in ['H','P','ERROR','START','STOP','SHELL','OPEN','CLOSE','ON','OFF']:
                            msg_handler.process()
                            del msg_handler
                        
                        elif splited_msg[0] == 'ACK' or \
                             msg_handler.command in ['S', 'J','L', 'A', 'D', 'E','Q','W','OW','AC']:
                            
                            rt.runtime.msg_received_queue.put(msg_handler)
                            
                        else:
                            tcp_server_log.error(f"{msg} should not have entered this clause in server.py. Please double check.")
                                
            except (ValueError,IndexError) as e:
                tcp_server_log.error(f"Receive wrong format of message from ST - {decodedMsg}")
    except Exception as e:  
        tcp_server_log.error(f'{e} cause disconnection')
        disconnectException(tcp_client)

def disconnectException(tcp_client:rt.TCPClient):

    close_socket_connection(tcp_client.conn)
    on_tcp_disconnected(tcp_client)

@common.log_and_suppress_error(tcp_server_log)
def on_tcp_connected(tcp_client:rt.TCPClient)->bool:
    
    if not Config.RANDOM_STATION_ADDRESS:

        is_station = check_station_connection(tcp_client)
        if not is_station:
            is_sd = check_sd_connection(tcp_client)
            if not is_sd:
                tcp_server_log.warning(f'Receive unkown connection from {tcp_client.host}:{tcp_client.port}')
                close_socket_connection(tcp_client.conn)
                return False               
       
    # For mock plc, will only add to station adpater after paired to know which station connecting and pairing 
    else:
        tcp_server_log.info(f"Accept connection from {tcp_client.host}:{tcp_client.port} ",color='light_green')
    
    rt.runtime.runtime_tcp_client[tcp_client.hash] = tcp_client

    return True

@common.log_and_suppress_error(tcp_server_log)
def check_station_connection(tcp_client:rt.TCPClient)->bool:

    # host not exist in db 
    station = db.st_db_func.get_station_by_host_and_port(tcp_client.host, tcp_client.port)

    if not station:
        tcp_server_log.warning(f'Not station connection from {tcp_client.host}:{tcp_client.port}')
        return False

    # if already connected close old connection 
    if station.is_connected:
        tcp_server_log.warning(f'ST{station.code} TCP is reconnecting.')
        old_station_conn = rt.runtime.runtime_tcp_client[station.connection].conn
        tcp_server_log.warning(f'Disconnecting the old station connection...')
        close_socket_connection(old_station_conn)
        
        time.sleep(1) # wait for disconnect

    db.st_db_func.station_connecting(station.code,tcp_client.hash,tcp_client.host,tcp_client.port)
    tcp_server_log.info(f"Accept connection from {station.name} {tcp_client.host}:{tcp_client.port} ",color='light_green')
    HttpClient.update_st_status(station.code)
    
    return True

@common.log_and_suppress_error(tcp_server_log)
def check_sd_connection(tcp_client:rt.TCPClient)->bool:
    # host not exist in db 
    sd = db.sd_db_function.get_sd_by_host_and_port(tcp_client.host, tcp_client.port)

    if not sd:
        tcp_server_log.warning(f'Not service door connection from {tcp_client.host}:{tcp_client.port}')
        return False

    # if already connected close old connection 
    if sd.is_connected:
        tcp_server_log.warning(f'SD{sd.id} TCP is reconnecting.')
        old_station_conn = rt.runtime.runtime_tcp_client[sd.connection].conn
        tcp_server_log.warning(f'Disconnecting the old service door connection...')
        close_socket_connection(old_station_conn)
        
        time.sleep(1) # wait for disconnect

    db.sd_db_function.sd_connecting(sd.id,tcp_client.hash)
    tcp_server_log.info(f"Accept connection from {sd.sdname} {tcp_client.host}:{tcp_client.port} ",color='light_green')
    
    return True
@common.log_and_suppress_error(tcp_server_log)
def on_tcp_disconnected(tcp_client:rt.TCPClient):

    from ..service_handler.cube_handler import StationMessageHandler, ServiceDoorMessageHandler

    is_found = False

    station = db.st_db_func.get_station_by_connection(tcp_client.hash)
    
    del rt.runtime.runtime_tcp_client[tcp_client.hash]

    if station:
        tcp_server_log.error(f'Close connection from {station.name} {tcp_client.host}:{tcp_client.port}')
        db.st_db_func.station_disconnecting(station.code)
        common.saveNotification(title = f"Cube Station Connection",
                                module=ec.ModuleCode.STATION.value,
                                Device = f'{ec.ModuleCode.STATION.value}{station.code}',
                                Message = f'ST{station.code} TCP Disconnected '
                                )
        # if already paired
        if station.is_active:
            StationMessageHandler.on_station_unpaired(station)
        else:
            HttpClient.update_st_status(station.code)
        
        is_found=True


    sd = db.sd_db_function.get_sd_by_conn(tcp_client.hash)

    if sd:
        tcp_server_log.error(f'Close connection from {sd.sdname} {tcp_client.host}:{tcp_client.port}')
        db.sd_db_function.sd_disconnecting(sd.id)
        # if already paired
        if sd.is_active:
            ServiceDoorMessageHandler.on_sd_unpaired(sd)
        
        is_found=True

    
    for key,value in rt.runtime.runtime_emo.items():
        if value.connection == tcp_client.hash:
            tcp_server_log.error(f'Close connection from {value.name} {tcp_client.host}:{tcp_client.port}')
            value.connection = None
            
            is_found=True


    if not is_found:
        tcp_server_log.error(f'{tcp_client.host}:{tcp_client.port} has disconnected')

def close_socket_connection(c:socket.socket):
    try:
        c.shutdown(socket.SHUT_RDWR)
    except OSError as e :
        pass
    except Exception as e:
        pass
    finally:
        c.close()
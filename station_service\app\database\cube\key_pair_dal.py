
from ... import app,DBMonitor,db
from ... import common as common,models as model

db_log = common.LogManager('cube_db_error',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_log))
class KeyPairSQLAlchemyQueries:

    @DBMonitor.retry_session
    def create_keypair(self,private_key_pem_bytes,public_key_pem_bytes):
        with app.app_context():
            curr_session = db.session
            keypair = model.KeyPair(
                private_key = private_key_pem_bytes,
                public_key = public_key_pem_bytes
                )
            curr_session.add(keypair)
            curr_session.commit()
    
    @DBMonitor.retry_session
    def get_keypair(self):
        with app.app_context():
            curr_session = db.session
            instance :model.KeyPair = curr_session.query(model.KeyPair).first()
            # already has key pair 
            return instance
            


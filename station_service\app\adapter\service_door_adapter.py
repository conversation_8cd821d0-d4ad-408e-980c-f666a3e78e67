import json

from .. import models as model,enum_collections as ec
from pingspace_python_packages.redis import Redis<PERSON><PERSON>, Set<PERSON>, Getter


class _Getter(Getter):

    def str_to_list(value:str):
        """
        For string in format "[]"
        """
        return json.loads(value)
    

class RedisServiceDoor(RedisHash):

    redis_prefix = 'hcc:service_door'
    
    setter = {
        "id":Setter.empty_or_int,
        "host":Setter.empty_or_int,
        "port":Setter.empty_or_int,
        "is_connected":int,
        "is_active":int,
        "connection":Setter.empty_or_int
    }

    getter = {
        "id":Getter.empty_or_int,
        "station_list":_Getter.str_to_list,
        "port":Getter.empty_or_int,
        "is_connected":Getter.str_to_bool,
        "is_active":Getter.str_to_bool,
        "connection":Getter.empty_or_int
    }

    def __init__(self,service_door:model.ServiceDoor) -> None:
        self.__dict__['name'] = f'{ec.RedisPrefix.SERVICE_DOOR.value}:{service_door.id}'

        self.id = service_door.id
        self.sdname = service_door.name
        self.host= service_door.host
        self.port = service_door.port
        self.type = service_door.type
        self.status = service_door.status
        self.station_list = service_door.station_list
        self.is_connected = service_door.is_connected
        self.is_active = service_door.is_active
        self.connection = None



    def as_dict(self):
        from ..runtime import runtime
        return dict(
            id = self.id,
            name = self.sdname,
            host = self.host,
            port = self.port,
            type = self.type,
            status = self.status,
            station_list = self.station_list,
            is_connected = self.is_connected,
            is_active = self.is_active,
            pending_open = (self.id in runtime.opening_sd and runtime.opening_sd[self.id].result().done())
        )
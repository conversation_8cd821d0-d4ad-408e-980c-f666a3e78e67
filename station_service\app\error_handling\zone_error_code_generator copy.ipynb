{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from pprint import pprint"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1: ('<PERSON><PERSON><PERSON> Triggered',\n", "     'Please remove any object detected by curtain sensor. Press reset and '\n", "     'start back the machine.',\n", "     1),\n", " 2: ('<PERSON> Jamming Error',\n", "     'Please push the bin back to the original zone, reset and start back the '\n", "     'machine',\n", "     1),\n", " 3: ('Power Roller Error', 'Machine down. Please call technician to check.', 2),\n", " 4: ('UBT Power Roller Error',\n", "     'Machine down. Please call technician to check.',\n", "     2),\n", " 5: ('UBT Motor Alarm', 'Machine down. Please call technician to check.', 2),\n", " 6: ('UBT Failed to Retract',\n", "     'Machine down. Please call technician to check.',\n", "     2),\n", " 7: ('UBT Failed to Extend',\n", "     'Machine down. Please call technician to check.',\n", "     2),\n", " 8: ('Stopper Failed to Retract',\n", "     'Machine down. Please call technician to check.',\n", "     2),\n", " 9: ('<PERSON><PERSON> Failed to Extend',\n", "     'Machine down. Please call technician to check.',\n", "     2),\n", " 10: ('Bin ID Read Fail', 'Machine down. Please call technician to check.', 2),\n", " 11: ('Load Cell Read Fail',\n", "      'Machine down. Please call technician to check.',\n", "      2)}\n"]}], "source": ["# Load the Excel file into a DataFrame\n", "df = pd.read_excel('errorv1.xlsx', sheet_name='Zone')  # Specify your sheet name\n", "df = df.dropna(subset=[\"Warning\", \"Action\"])\n", "\n", "\n", "# Iterate over each row in the DataFrame\n", "pprint({\n", "    row['Error Code']: (\n", "        row['Warning'],\n", "        row[\"Action\"],\n", "        int(row['Error Level'])\n", "    )\n", "    for _, row in df.iterrows()\n", "})\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
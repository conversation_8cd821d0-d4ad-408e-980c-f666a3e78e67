from termcolor import colored

from .asrs_order_dal import AsrsOrderSQLAlchemyQueries
from ... import common as common, enum_collections as ec

db_func_log = common.get_logger_console('asrs_db_func')

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_func_log))
class AsrsOrderDBFunc:
    
    asrs_order_dal = AsrsOrderSQLAlchemyQueries()
    
    def create_order(self,x, y, z, asrs_id, method, type, order_id, pallet_id, station_id)->int:
        existing_order = self.asrs_order_dal.get_order_by_order_id(order_id)
        if existing_order != None:
            # If an order already exists, return None
            db_func_log.warning(colored(f'ASRS Order {order_id} already created. Ignoring this one.','light_yellow'))
            return None
        else:
            if type == '':
                asrs_order_id = self.asrs_order_dal.create_order(x, y, z, asrs_id, method, order_id, pallet_id, station_id)
            else:
                asrs_order_id = self.asrs_order_dal.create_order(x, y, z, asrs_id, method, order_id, pallet_id, station_id,type)
            return asrs_order_id
    
    def get_order(self,id:int,asrs_order_id = False):
        # query by wcs mediator order_id
        if asrs_order_id is False:  
            order = self.asrs_order_dal.get_order_by_order_id(id)
            if order == None: db_func_log.error(f'No order found for order_id {id}')
        # query by order.id
        else:  
            order = self.asrs_order_dal.get_order_by_id(id)
            if order == None: db_func_log.error(f'No order found for id {id}')
        return order
    
    def get_processing_order(self):
        return self.asrs_order_dal.get_all_orders_by_status(ec.OrderStatus.PROCESSING.value)
        
    # TODO check can straight return the modified order or not
    def complete_order(self,id:int):
        self.asrs_order_dal.update_order_status(id, ec.OrderStatus.COMPLETED.value)
        return self.asrs_order_dal.get_order_by_id(id)
        
    def processing_order(self,id:int):
        self.asrs_order_dal.update_order_status(id,ec.OrderStatus.PROCESSING.value)

# def reset_asrs_order():
#     try:
#         with app.app_context():
#             Asrs_order.query.delete()
#             db.session.commit()
#     except Exception as e:
#         error_log.error(redBright(f'Unable to reset_asrs_order. Exception thrown: {e}'))

# def reset_asrs_job():
#     try:
#         with app.app_context():
#             Asrs_job.query.delete()
#             db.session.commit()
#     except Exception as e:
#         error_log.error(redBright(f'Unable to reset_asrs_job. Exception thrown: {e}'))


# def start_fresh_jobs():
#     '''Complete everything in the warehouse and start all jobs fresh'''
#     try:
#         db.session.query(Asrs_job).filter(Asrs_job.job_status.notin_([OrderStatus.COMPLETED.value,OrderStatus.CRITICAL_ERROR.value])).update({"job_status": OrderStatus.COMPLETED.value},synchronize_session='fetch')
#         db.session.commit()

#     except Exception as e:
#         error_log.error(redBright(f'start_fresh_jobs error. Exception thrown: {e}'))
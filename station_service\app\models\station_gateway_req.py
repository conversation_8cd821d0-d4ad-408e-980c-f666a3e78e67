from dataclasses import dataclass
from .. import db


@dataclass
class StationGatewayReq(db.Model):
    id: int
    station_code:int
    type: str
    tc_job_id: int
    status: str
    storage_code: str
    position:int
    event: str
    entity: str
    canceled_ack :bool
    created_at: str
    updated_at: str

    __tablename__ = "station_gateway_req"

    id = db.Column(
        db.Integer,
        primary_key=True
    )

    station_code = db.Column(
        db.Integer,
        index=False, 
        nullable = False
        )
    
    type = db.Column(
        db.String(128),
        index=False,
        nullable=False
    )

    tc_job_id = db.Column(
        db.Integer,
        index = False,
        nullable = True,
        unique = True,
    )

    status = db.Column(
        db.String(128),
        index=False,
        unique=False
    )

    storage_code = db.Column(
        db.String(128),
        index=False,
        unique=False
    )

    position = db.Column(
        db.Integer,
        index = False,
        nullable = False
    )

    event = db.Column(
        db.String(128),
        index=False,
        nullable=False
    )

    entity = db.Column(
        db.String(128),
        index=False,
        nullable=True
    )

    canceled_ack = db.Column(
        db.Boolean,
        index=False,
        default=False
    )

    created_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

    updated_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

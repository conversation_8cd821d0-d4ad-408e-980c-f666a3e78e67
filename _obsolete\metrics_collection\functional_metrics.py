# from prometheus_client import Summary, Counter

# func_invocation = Counter('func_invocations', 'Amount of time a function was invoked', ['function_name'])
# func_time_sum = Counter('func_time_sum', 'Amount of time spend inside a function total', ['function_name'])
# func_error_sum = Counter('func_error_sum', 'Amount of error a function has encountered', ['function_name'])
# ws_time_sum = Counter('ws_time_sum', 'Amount of time spend inside each websocket event', ['function_name'])
# ws_invocation = Counter('ws_invocation', 'Amount of time a websocket event is triggered', ['function_name'])

# class FunctionMetrics:
#     def __init__(self):
#         self.func_invocation = func_invocation
#         self.func_time_sum = func_time_sum
#         self.func_error_sum = func_error_sum
#         self.ws_time_sum = ws_time_sum
#         self.ws_invocation = ws_invocation
    
#     def register_func_average_time(self,start, end, func_name):
#         self.func_invocation.labels(func_name).inc()
#         self.func_time_sum.labels(func_name).inc(end - start)
    
#     def register_func_error(self,func_name):
#         self.func_error_sum.labels(func_name).inc()
    
#     def register_ws_average_time(self,start,end,func_name):
#         self.ws_time_sum.labels(func_name).inc(end-start)
#         self.ws_invocation.labels(func_name).inc()

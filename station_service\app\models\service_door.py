from dataclasses import dataclass


from .. import db
from .. import enum_collections as ec
from .enum import SDStatus,SDType

@dataclass
class ServiceDoor(db.Model):
    id: int
    name: str
    host: str
    port: int
    type: str
    status: str
    station_list: str
    is_connected: bool
    is_active: bool
    created_at: str
    updated_at: str


    __tablename__ = 'service_doors'
    id = db.Column(
        db.Integer,
        primary_key=True)

    name = db.Column(
        db.String(128),
        index = False,
    )

    host = db.Column(
        db.String(128))
    
    port = db.Column(
        db.Integer,
        index=False,
        unique=False,
        nullable=True)

    type = db.Column(
        SDType
    )

    status = db.Column(
        SDStatus
    )

    station_list = db.Column(
        db.Text, 
        default = "[]",
        unique = False,
        index = False,
        nullable=True
    )

    is_connected = db.Column(
        db.<PERSON>, 
        default=False)

    
    is_active = db.Column(
        db.<PERSON><PERSON><PERSON>,
        index=False,
        unique=False,
        nullable=True,
        default = False
    )


    created_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

    updated_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

    def as_dict(self):
        import datetime
        
        result = {}
        for c in self.__table__.columns:
            value = getattr(self, c.name)
            if isinstance(value,datetime.datetime):
                result[c.name] = value.isoformat()
            else:
                result[c.name] = value

        return result
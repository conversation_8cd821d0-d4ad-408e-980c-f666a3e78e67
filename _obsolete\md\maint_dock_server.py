# import queue
# import socket
# import eventlet
# import threading

# from simple_chalk import greenBright, redBright, blueBright, cyanBright, yellowBright

# import config
# import app.database as db
# import app.common as common
# import app.enum_collections as enum_collection


# md_error = common.get_logger_console('md_errors')
# md_connection = common.get_logger_console('md_TCP')

# connected_md_ips = dict()
# md_logs = dict()
# connection_list = []
# disconnectList = []
# md_msg_queue = queue.Queue()


# def start_md_server():
#     try:
#         s = socket.socket()
#         md_tcpip = config.Config.get_md_tcpip()
#         s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
#         s.bind((md_tcpip.host, md_tcpip.port))
#         s.listen(5)
#         md_connection.info(greenBright(f'Starting up Maint Dock server on ip: {md_tcpip.host} and port: {md_tcpip.port}'))

#         db.clear_md_status()

#         newConnection = threading.Thread(target=onNewConnection, args=(s,), name="NewConnection")
#         newConnection.start()

#     except Exception as e:
#         md_connection.error(redBright(f'start_md_server error. Exception thrown: {e}'))


# def onNewConnection(s):
#     try:
#         while True:
#             c, addr = s.accept()
#             # get the name from database
#             if addr[0] not in connected_md_ips.keys() or addr[0] == '127.0.0.1':

#                 connected_md_ips[addr[0]] = addr[0]
#                 md_connection.info(f'Maintenance Dock has connected with ip:{addr[0]} with port: {str(addr[1])}')

#                 newClient = threading.Thread(target=onNewClient, args=(c, addr,))
#                 newClient.start()
#             else:
#                 md_connection.warning(yellowBright(f'Duplicate ip of {addr[0]}:{addr[1]} detected. Will close this connection.'))
#                 eventlet.sleep(1)
#                 c.shutdown(socket.SHUT_RDWR)
#                 c.close()
#     except Exception as e:
#         md_connection.error(redBright(f'onNewConnection (MD) error. Exception thrown: {e}'))


# def md_reconnect(c, addr):
#     try:
#         global connection_list, disconnectList
#         flag = False
#         if len(disconnectList) != 0:
#             for target_info in disconnectList:
#                 if target_info[0] == addr[0]:
#                     target_info[2] = c
#                     target_info[1] = addr[1]
#                     connection_list.append(target_info)
#                     disconnectList.remove(target_info)
#                     flag = True
#         return flag
#     except Exception as e:
#         md_connection.error(redBright('md_reconnect Error: '), e)


# def onNewClient(c, addr):
#     try:
#         flag = md_reconnect(c, addr)
#         global connection_list
#         if flag == True:
#             print(greenBright("--------  %s Reconnected  ------" % threading.currentThread().getName()))
#         else:
#             c.send('MD,P;'.encode())
#             print(greenBright("--------  %s Maint Dock Connected  ------" % threading.currentThread().getName()))
#         print(greenBright("  IP Address: %s\n  Port: %s " % (addr)))
#         print(greenBright("--------------------------------------"))

#         while 1:
#             reply = c.recv(1024)
#             decodedMsg = reply.decode()

#             if(len(decodedMsg) == 0):
#                 # ping client to test connection, catch error if not connected
#                 c.send('ping'.encode())
#             else:
#                 msg_list = decodedMsg.split(';')
#                 for msg in msg_list:
#                     if msg != '':
#                         splited_msg = msg.split(',')
#                         if splited_msg[0] != 'ACK':
#                             md_id = int(splited_msg[1])
#                         else:
#                             md_id = int(splited_msg[2])

#                         maint_log_header = common.get_logger_header(enum_collection.EntityType.MAINTANENCE_DOCK.value, f'{splited_msg[0]}{splited_msg[1]}')
#                         maint_log_header.info(blueBright(f'Received - ')+f'{msg}')

#                         if ('P' in splited_msg[2]):
#                             append_flag = True
#                             for conn in connection_list:
#                                 if splited_msg[1] == conn[4]:
#                                     append_flag = False
#                             if append_flag:
#                                 connection_list.append([addr[0], addr[1], c, f'{splited_msg[0]}{splited_msg[1]}', f'{splited_msg[1]}'])
                                
#                             msg_to_send = f'{splited_msg[0]},{splited_msg[1]},P,SUCCESS;'
#                             send_md_msg(f'{splited_msg[0]}{splited_msg[1]}', msg_to_send)
#                             db.set_md_pairing(md_id,True)
#                             md_station = db.get_md_station(md_id)
#                             json_data = {
#                                 'station_id': md_id,
#                                 'status': True
#                             }
#                             entity = f'{enum_collection.ExternalEntities.TC.value}_{md_station.zone}'
#                             db.add_md_ws_request(entity,json_data,'md-status',namespace='station')

#                         elif (splited_msg[2] in ['J', 'D', 'A']):  # Accept deny entry + job done open close
#                             msg_to_send = f'ACK,{msg};'
#                             send_md_msg(f'{splited_msg[0]}{splited_msg[1]}', msg_to_send)
#                             md_msg_queue.put([splited_msg[1], msg])
#                         elif splited_msg[0] == 'ACK':
#                             md_msg_queue.put([splited_msg[2], msg])
#                         else:
#                             md_error.error(redBright(f'Invalid Message. the msg is: {msg}'))

#     except Exception as e:
#         global connected_md_ips
#         md_connection.info(redBright(f'Maint Dock client {addr[0]}:{addr[1]} disconnected. Reason: {e}'))
#         client = on_md_disconnected(c, addr)
#         del connected_md_ips[addr[0]]
#         if client != None:
#             disconnectList.append(client)


# def on_md_disconnected(c, addr):
#     try:
#         global disconnectList, connection_list
#         c.close()
#         disconnectedClient = get_dc_md_details(c)
#         if disconnectedClient != None:
            
#             md_id = int(disconnectedClient[4])
#             md_station = db.get_md_station(md_id)
#             json_data ={
#                 'station_id': md_id,
#                 'status': False
#             }
#             entity = f'{enum_collection.ExternalEntities.TC.value}_{md_station.zone}'
#             db.add_md_ws_request(entity,json_data,'md-status',namespace='station')
#             db.set_md_pairing(md_id,False)
#             connection_list.remove(disconnectedClient)
            
#         # receivedBroadcastQ.put(["0", "ST,0,P;"])  # Use station 0 pairing protocol as hotfix to trigger broadcastLoop to emit active stations

#         print(greenBright("-------  %s Maint Dock Disconnected  --------" % (threading.currentThread().getName())))
#         print(greenBright("  IP Address: %s\n  Port: %s " % (addr)))
#         print(greenBright("---------------------------------------"))
#         return disconnectedClient
#     except Exception as e:
#         md_connection.error(redBright(f'on_md_disconnected error: {e}'))


# def get_dc_md_details(conn):
#     try:
#         global connection_list
#         getClient = next(c for c in connection_list if c[2] == conn)
#         return getClient
#     except Exception as e:
#         md_error.error(redBright(f'get_dc_md_details error: {e}'))


# def send_md_msg(targetName, msg):
#     try:
#         targetClient = get_md_details(targetName)
#         eventlet.sleep(0.1)
#         if targetClient != None:
#             targetClient[2].send(msg.encode())
            
#             # maint_log_header = logger_manager.get_logger_header(EntityType.MAINTANENCE_DOCK.value, f'MD{targetClient[4]}')
#             # maint_log_header.info(cyanBright(f'Send Message to {targetName} - ')+f'{msg}')
             
#             print(cyanBright(f'Send Message to {targetName} - ')+f'{msg}')
#             return "Message sent"
#     except Exception as e:
#         md_error.error(f'send_md_msg error for msg: - {msg}. Exception thrown:{e}')
#         return "Maintanence Dock is not connected"


# def get_md_details(name):
#     try:
#         global connection_list
#         getClient = next(c for c in connection_list if c[3] == name)
#         return getClient
#     except StopIteration as e:
#         md_error.error(redBright(f'Maintanence Dock does not exist: {name}'))
#         return None
#     except Exception as e:
#         md_error.error(redBright(f'get_md_details error:{e}'))

import datetime

from typing import List

from ... import app,DBMonitor,db
from ... import common as common,models as model,enum_collections as ec

db_log = common.LogManager('cube_db_error',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_log))
class StationEventSQLAlchemyQueries:

    @DBMonitor.retry_session
    def get_all_active_event(self,station_code:int=None)->List[model.StationEvent]:
        with app.app_context():
            curr_session = db.session
            if station_code:
                active_event = curr_session.query(model.StationEvent).filter(model.StationEvent.station_code==station_code,model.StationEvent.status == ec.OrderStatus.AVAILABLE.value).order_by('created_at','id').all()
            else:
                active_event = curr_session.query(model.StationEvent).filter(model.StationEvent.status == ec.OrderStatus.AVAILABLE.value).order_by('created_at','id').all()
            return active_event
            
    @DBMonitor.retry_session
    def get_st_event(self,filters:dict)->model.StationEvent:
        with app.app_context():
            curr_session = db.session
            st_event = curr_session.query(model.StationEvent).filter_by(**filters).first()
            return st_event

    
    @DBMonitor.retry_session
    def create_st_event(self,data:dict):
        st_event = model.StationEvent(
            event_name = data.get('event_name'),
            station_code = data.get('station_code'),
            storage_code = data.get('storage_code'),
            is_enroll = data.get('is_enroll'),
            is_processed = data.get('is_processed'),
            status = ec.OrderStatus.AVAILABLE.value,
            request_id = data.get('request_id'),
            job_to_complete_event = data.get('job_to_complete_event'),
            created_at = datetime.datetime.now(),
            updated_at = datetime.datetime.now()
        )
        curr_session = db.session 
        curr_session.add(st_event)
        curr_session.commit()

        return st_event
    
    @DBMonitor.retry_session
    def create_bundled_events(self,is_drop:bool,event_data: dict):
        from ... import adapter as adp
        
        with app.app_context():
            curr_session = db.session                
            # Create REQ_{} event
            req= model.StationEvent(
                event_name=ec.CubesIStationEvent.REQ_DROP.value if is_drop else ec.CubesIStationEvent.REQ_PICK.value,
                station_code=event_data['station_code'],
                storage_code=event_data['storage_code'],
                is_enroll=event_data.get('is_enroll'),
                is_processed=False,
                status=ec.OrderStatus.AVAILABLE.value,
                request_id=event_data['request_id'],
                created_at=datetime.datetime.now(),
                updated_at=datetime.datetime.now()
            )
            curr_session.add(req)
            
            # Create {}_DONE event
            done = model.StationEvent(
                event_name=ec.CubesIStationEvent.DROP_DONE.value if is_drop else ec.CubesIStationEvent.PICK_DONE.value,
                station_code=event_data['station_code'],
                storage_code=event_data['storage_code'],
                is_enroll=event_data.get('is_enroll'),
                is_processed=False,
                status=ec.OrderStatus.AVAILABLE.value,
                request_id=event_data['request_id'],
                created_at=datetime.datetime.now(),
                updated_at=datetime.datetime.now()
            )
            curr_session.add(done)
            
            # Commit transaction
            curr_session.commit()
            
            adp.EventAdapter(req)
            adp.EventAdapter(done)
            
            return req, done
                
    
    @DBMonitor.retry_session
    def update_st_event(self,event_id:int,update:dict)->int:
        with app.app_context():
            curr_session = db.session
            event = curr_session.query(model.StationEvent).filter_by(id = event_id).first()
            if event :
                for k,v in update.items():
                    event.__setattr__(k,v)
                event.updated_at = datetime.datetime.now()
                updated_event_id = event.id
                curr_session.commit()
                return updated_event_id
            else:
                db_log.error(f'No event with id {event_id} found')
                return None

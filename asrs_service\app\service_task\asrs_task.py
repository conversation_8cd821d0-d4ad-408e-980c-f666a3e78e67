from typing import Callable, List
from concurrent.futures import ThreadPoolExecutor

class AsrsTask:

    @classmethod
    def run(cls):
        thread_pool : List[Callable] = list()


        from ..service_handler import ASRSService
        thread_pool.append(ASRSService.run)

        if thread_pool:
            executor = ThreadPoolExecutor(len(thread_pool))

            for thread in thread_pool:
                executor.submit(thread)
from dotenv import load_dotenv
load_dotenv()

from termcolor import colored

from app import create_app
from config import Config
from app.service_task import CubeTask
from app.common.displayFormatter import formatString

import sys, os, threading
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
sys.dont_write_bytecode = True

# remove flask default cli
cli = sys.modules['flask.cli']
cli.show_server_banner = lambda *x: None


if __name__ == "__main__":        
    app = create_app()

    flask_url = Config.get_flask_conn()
    flask_host = flask_url[0]
    flask_port = flask_url[1]

    
    # Start Up different service
    CubeTask.run()

    print(formatString('INFO',colored(f"Starting up Flask server at \tip: {flask_host} port: {flask_port}","light_green")))
    threading.Thread(target=app.run,kwargs=dict(host=flask_host,port = flask_port,debug=False),name='flask_app').start()

    from app.communications_provider.socketio import SocketIOServer
    SocketIOServer.run()


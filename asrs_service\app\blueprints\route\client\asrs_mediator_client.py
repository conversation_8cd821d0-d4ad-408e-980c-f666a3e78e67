import aiohttp

from termcolor import colored

from config import Config
from .... import common as common
from ....import enum_collections as ec

mediator_http_send_log = common.get_logger_console('asrs_mediator_http_send')

@common.decorateAllFunctionInClass(common.async_log_and_suppress_error(mediator_http_send_log))
class MedHttpClient:    
    """
    class to gather all the http call to mediator client 
    """
    
    mediator_host = Config.get_mediator_host()
    
    @classmethod  
    async def request_location(cls,pallet_id:str):
                
        data = {
            'pallet_id': pallet_id
        }
        mediator_http_send_log.info(colored(f'Sent - request_location: ','light_magenta')+f'{data}')
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f'{cls.mediator_host}/v1/app/request-location', json= data, timeout = 3) as resp:
                data = await resp.json()
                mediator_http_send_log.info(colored(f'request_location : {data},{resp.status}','light_magenta'))
        
        return data['data']

    @classmethod
    async def cancel_job(cls,pallet_id:str):
        """Inform mediator to free pallet id / pallet no

        Args:
            pallet_id (_type_): pallet id that the job need to be cancel
        """
        data = {
            'pallet_id': pallet_id
        }
        mediator_http_send_log.info(colored(f'Sent - cancel_job: ','light_magenta')+f'{data}')
        
        async with aiohttp.ClientSession() as session:
            async with session.post(f'{cls.mediator_host}/v1/app/cancel-job', json= data, timeout = 3) as resp:
                mediator_http_send_log.info(colored(f'cancel_job : {resp.status}','light_magenta'))
    
    @classmethod
    async def update_error(cls,data):
        mediator_http_send_log.info(colored(f'Sent - update_error: ','light_magenta')+f'{data}')

        async with aiohttp.ClientSession() as session:
            async with session.patch(f'{cls.mediator_host}/v1/app/order-error', json= data, timeout = 3) as resp:
                mediator_http_send_log.info(colored(f'update_error : {resp.status}','light_magenta'))
        
    @classmethod
    async def reset_error(cls,station_id:int, domain:str):
        
        data = {
            'station_id':station_id,
            'domain': domain
        }
        mediator_http_send_log.info(colored(f'Sent - reset_error: ','light_magenta')+f'{data}')
            
        async with aiohttp.ClientSession() as session:
            async with session.patch(f'{cls.mediator_host}/v1/app/err3-recovery', json= data, timeout = 3) as resp:
                mediator_http_send_log.info(colored(f'reset_error : {resp.status}','light_magenta'))

    @classmethod
    async def emit_asrs_order_complete(cls,pallet_no:str, station_id:int, order_id:int, order_type:str):
        
        if order_type == ec.AsrsMethod.GET.value:
            
            data = {
                "pallet_no" : pallet_no,
                "station_id" : station_id
            }
            mediator_http_send_log.info(colored(f'Sent - pallet-arrived : ','light_magenta')+f'{data}')

            async with aiohttp.ClientSession() as session:
                async with session.patch(f'{cls.mediator_host}/v1/app/pallet-arrived', json= data, timeout = 3) as resp:
                    mediator_http_send_log.info(colored(f'pallet_arrived : {resp.status}','light_magenta'))
            
                    
        order_completed_data = {
            "order_id":order_id
        }
        mediator_http_send_log.info(colored(f'Sent - order_completed : ','light_magenta')+f'{order_completed_data}')
        
        async with aiohttp.ClientSession() as session:
            async with session.patch(f'{cls.mediator_host}/v1/app/order-completed', json= order_completed_data, timeout = 3) as resp:
                mediator_http_send_log.info(colored(f'order_completed : {resp.status}','light_magenta'))
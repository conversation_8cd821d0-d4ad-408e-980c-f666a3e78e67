import socket
import asyncio

from typing import Set, Dict
from termcolor import colored,cprint

from config import Config
from .. import common as common,enum_collections as ec


asrs_log = common.get_logger_console('asrs_server')
connections_log = common.get_logger_console('asrs_TCP')

class PLCClient():
    def __init__(self,ip:str,port:int,conn:socket.socket,plc_name:str,plc_id:int) -> None:
        self.ip         : str= ip
        self.port       : int= port
        self.conn       : socket.socket= conn
        self.plc_name   : str = plc_name
        self.plc_id     : int = plc_id
    
    def __eq__(self, other):
        if not isinstance(other, PLCClient):
            return False
        return self.ip == other.ip and self.port == other.port and self.conn == other.conn and self.plc_name == other.plc_name

    def __hash__(self):
        return hash((self.ip,self.port,self.conn,self.plc_name))
        
connected_ips : Dict[str,socket.socket]= {}
connection_list : Dict[str,Set[PLCClient]] = {}

class ASRSServer:
    
    @classmethod
    def connect(cls):
        cls.server = socket.socket()
        cls.server.setblocking(False)
        asrs_tcpip = Config.get_asrs_tcpip()
        cls.server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        cls.server.bind((asrs_tcpip.host, asrs_tcpip.port))
        cls.server.listen(5)
        asrs_log.info(colored(f'Starting up ASRS server on ip: {asrs_tcpip.host} and port: {asrs_tcpip.port}','light_green'))
    
    @classmethod
    async def start_server(cls):
        from ..service_handler.asrs_handler import ASRSService
        
        cls.connect()
        global connected_ips
        while True:
            try:
                cleint_socket, (ip, port) = await ASRSService.event_loop.sock_accept(cls.server)
                
                if ip in connected_ips and ip != '127.0.0.1' :
                # if addr[0] in connected_ips:
                    connections_log.warning(colored(f'Duplicate ip of {ip}:{port} detected. Will close old connection.','light_yellow'))
                    old_client = get_client_by_ip(ip)
                    # client paired already
                    if old_client: 
                        connections_log.warning(colored(f'Disconnecting the old plc connection...','light_yellow'))
                        old_tcp_header = old_client.conn
                    else:
                        connections_log.warning(colored(f'Disconnecting the old ip connection...','light_yellow'))
                        old_tcp_header = connected_ips[ip]
                    close_socket_connection(old_tcp_header)
                    
                    # wait for disconnection
                    await asyncio.sleep(1)  

                ASRSService.event_loop.call_soon(
                    ASRSService.event_loop.create_task,
                    cls.handle_client_message(ip ,port, cleint_socket)
                )
            except Exception as e:
                asrs_log.error(f'{cls.start_server.__qualname__} has error : {e}')

    @classmethod
    async def handle_client_message(cls,ip:str, port:int, c:socket.socket):
        try:        
            global connection_list, connected_ips
            from ..service_handler.asrs_handler import ASRSService, MessageHandler

            cprint("--------  New Client Connected  ------",'light_green')
            cprint("  IP Address: %s\n  Port: %s " % (ip,port),'light_green')
            cprint("--------------------------------------",'light_green')

            connected_ips[ip] = c

            while True:
                try:

                    reply = await ASRSService.event_loop.sock_recv(c,1024)
                    decodedMsg = reply.decode()

                    if(len(decodedMsg) == 0):
                        
                        # ping client to test connection, catch error if not connected
                        c.send('ping'.encode())
                    else:
                        msg_list = decodedMsg.split(';')
                        for msg in msg_list:
                            if msg != '' and msg != '\r\n':
                                splited_msg = msg.split(',')
                                
                                # get module and plc id
                                if splited_msg[0] == 'ACK':
                                    index = 1
                                    plc_id = int(splited_msg[2])
                                else:
                                    index = 0
                                    plc_id = int(splited_msg[1])

                                if 'PC' in splited_msg[index]:
                                    module = ec.ModuleCode.PalletConveyor.value
                                else:
                                    module = ec.ModuleCode.ASRS.value

                                asrs_log_header = common.get_logger_header(ec.EntityType.STATION.value, f'{module}{plc_id}')
                                asrs_log_header.info(colored(f"Received Message from {module}{plc_id}: ",'light_blue') + f'{common.messageColor(msg)}')  
                                
                                if (splited_msg[2] == 'Pairing'):
                                    # AS,1,Pairing     
                                    
                                    current_client = PLCClient(
                                            ip = ip,
                                            port = port,
                                            conn=c,
                                            plc_name=f'{module}{plc_id}',
                                            plc_id=plc_id
                                    )
                                    

                                    # check if client is already connected
                                    if f'{module}{plc_id}' in connection_list :
                                        if connection_list[f'{module}{plc_id}'] != current_client:
                                            asrs_log.warning(colored(f'Connection for {module}{plc_id} already exist. Replacing the old connection......','light_yellow'))
                                           
                                            close_socket_connection(connection_list[f'{module}{plc_id}'].conn)
                                    
                                            await asyncio.sleep(1) # wait for disconnection

                                           

                                    connection_list[f'{module}{plc_id}'] = current_client
                                    msg_to_reply = common.StringResponse.PairingSuccess(module,plc_id)
                                    send_asrs_msg(f'{module}{plc_id}', msg_to_reply)

                                elif (splited_msg[2] == "S"):
                                    # plc will respond status code 1-8 , 1 and 2 is non error , other than that must infrom wcs machine error
                                    if len(splited_msg) <5:
                                        asrs_log.warning(colored(f'{module}{plc_id} reply invalid S protocol. Message given: {msg}','light_yellow'))
                                        continue
                                    
                                    if splited_msg[4] == '':
                                        asrs_log.warning(colored(f'{module} reply invalid S protocol. Message given: {msg}','light_yellow'))
                                        continue
                                    
                                    status_code = int(splited_msg[4])

                                    machine_status_list = list(ec.MachineStatus)
                                    machine_status_warning_list = list(ec.MachineStatusWarning)

                                    if status_code <= 0  or status_code > len(machine_status_list)-1:
                                        asrs_log.warning(colored(f'{module} reply invalid status code. Message given: {msg}','light_yellow'))
                                        continue
                                        
                                    common.saveNotification(title = f"ASRS Station Status",
                                                            module=ec.ModuleCode.ASRS.value,
                                                            Device = f'{module}{plc_id}',
                                                            Status = machine_status_warning_list[status_code].value
                                                            )
                                    
                                await MessageHandler.process_msg(msg)
                                
                except (ValueError,IndexError) as e:
                    asrs_log.error(colored(f"Receive wrong format of message from PLC : {e} - {decodedMsg}",'light_red'))
        except Exception as e:
            connections_log.info(colored(f'Client with ip: {(ip,port)} has disconnected. Exception: {e}','light_red'))
            client = onClientDisconnected(c, (ip,port))
            if ip in connected_ips:
                del connected_ips[ip] 
            try:
                c.shutdown(socket.SHUT_RDWR)
            except OSError as e:
                pass
            c.close()

def onClientDisconnected(c, addr):
    try:
        global connection_list
        disconnectedClient = getDisconnectDetails(c)
        if disconnectedClient:
            del connection_list[disconnectedClient.plc_name]

            cprint("-------  %s Disconnected  --------" %disconnectedClient.plc_name,'light_green')
            cprint("  IP Address: %s\n  Port: %s " % (addr),'light_green')
            cprint("---------------------------------------",'light_green')
            if ec.ModuleCode.PalletConveyor.value in disconnectedClient.plc_name:
                common.saveNotification(title = f"ASRS Station Connection",
                                        module=ec.ModuleCode.ASRS.value,
                                        Device = f'{ec.ModuleCode.PalletConveyor.value}{disconnectedClient.plc_id}',
                                        Message = ec.Notify.DISCONNECT_PC.value
                                        )
            else:
                common.saveNotification(title = f"ASRS Station Connection",
                                        module=ec.ModuleCode.ASRS.value,
                                        Device = f'{ec.ModuleCode.ASRS.value}{disconnectedClient.plc_id}',
                                        Message = ec.Notify.DISCONNECT_ASRS.value
                                        )
        else:
            asrs_log.warning(colored("Not able to find disconnected client, perhaps the client has not paired yet",'light_yellow'))
        return disconnectedClient
    
    except Exception as e:
        connections_log.error(colored(f'onClientDisconnected error: {e}','light_red'))

def close_socket_connection(c:socket.socket):
    try:
        c.shutdown(socket.SHUT_RDWR)
    except OSError as e :
        connections_log.error(colored(f'Not able to close connection due to error: OSERROR: {e}','light_red'))
    except Exception as e:
        connections_log.error(colored(f'Not able to close connection due to error: {e}','light_red'))
    finally:
        c.close()

def getDisconnectDetails(conn)->PLCClient:
    try:
        global connection_list
        client = next((c for c in connection_list.values() if c.conn == conn),None)
        return client
    except Exception as e:
        connections_log.error(colored(f'getDisconnectDetails error: {e}','light_red'))

def get_client_by_ip(ip_addr)->PLCClient:
    try:
        global connection_list
        client = next(c for c in connection_list.values() if c.ip == ip_addr)
        return client
    except StopIteration as e:
        return
    except Exception as e:
        asrs_log.error(colored(f'get_client_by_ip error: {e}','light_red'))


def get_asrs_client(target_client)->PLCClient:
    try:
        global connection_list
        client = next(c for c in connection_list.values() if c.plc_name == target_client)
        return client
    except StopIteration as e:
        connections_log.error(colored(f'Client {target_client} does not exist','light_red'))
    except Exception as e:
        asrs_log.error(colored(f'get_asrs_client error. Exception thrown: {e}','light_red'))


def send_asrs_msg(target_client, msg):
    try:
        client = get_asrs_client(target_client)
        station_log_header = common.get_logger_header(ec.EntityType.STATION.value, f'{target_client}')
        client :PLCClient
        if client :
            client.conn.send(msg.encode())
            station_log_header.info(colored(f'Sent message to {target_client}: ','light_cyan') + common.messageColor(msg))
    except Exception as e:
        asrs_log.error(colored(f'send_asrs_msg error: Exception thrown: {e}, failed to send {msg}','light_red'))





from typing import List, Dict, Optional
from .. import db_func_log
from ...common import log_and_suppress_error, decorateAllFunctionInClass
from .flow_tracking_dal import FlowTrackingDAL


@decorateAllFunctionInClass(log_and_suppress_error(db_func_log))
class FlowTrackingDBFunc:
    """Database function layer for flow tracking operations"""
    
    flow_tracking_dal = FlowTrackingDAL()
    
    def create_flow_record(self, request_id: str, station_code: int, storage_code: str,
                          tc_job_id: int, position: int, action: str, current_stage: str) -> int:
        """Create a new flow tracking record"""
        data = {
            'request_id': request_id,
            'station_code': station_code,
            'storage_code': storage_code,
            'tc_job_id': tc_job_id,
            'position': position,
            'action': action,
            'current_stage': current_stage
        }
        return self.flow_tracking_dal.create_flow_tracking(data)
    
    def update_flow_stage(self, request_id: str, stage: str, elapsed_minutes: float = 0.0,
                         total_elapsed_minutes: float = 0.0) -> bool:
        """Update flow stage and timing"""
        update_data = {
            'current_stage': stage,
            'stage_elapsed_minutes': elapsed_minutes,
            'total_elapsed_minutes': total_elapsed_minutes
        }
        return self.flow_tracking_dal.update_flow_tracking(request_id, update_data)
    
    def mark_flow_stuck(self, request_id: str, reason: str) -> bool:
        """Mark flow as stuck"""
        update_data = {
            'is_stuck': True,
            'stuck_reason': reason
        }
        return self.flow_tracking_dal.update_flow_tracking(request_id, update_data)
    
    def mark_flow_waiting(self, request_id: str, reason: str) -> bool:
        """Mark flow as waiting"""
        update_data = {
            'is_waiting': True,
            'waiting_reason': reason
        }
        return self.flow_tracking_dal.update_flow_tracking(request_id, update_data)
    
    def clear_flow_waiting(self, request_id: str) -> bool:
        """Clear flow waiting state"""
        update_data = {
            'is_waiting': False,
            'waiting_reason': None
        }
        return self.flow_tracking_dal.update_flow_tracking(request_id, update_data)
    
    def increment_retry_count(self, request_id: str) -> bool:
        """Increment retry count for a flow"""
        flow = self.flow_tracking_dal.get_flow_tracking_by_request_id(request_id)
        if flow:
            update_data = {
                'retry_count': flow.retry_count + 1
            }
            return self.flow_tracking_dal.update_flow_tracking(request_id, update_data)
        return False
    
    def complete_flow(self, request_id: str) -> bool:
        """Mark flow as completed"""
        return self.flow_tracking_dal.complete_flow_tracking(request_id)
    
    def get_flow_by_request_id(self, request_id: str):
        """Get flow by request ID"""
        return self.flow_tracking_dal.get_flow_tracking_by_request_id(request_id)
    
    def get_flow_by_tc_job_id(self, tc_job_id: int):
        """Get flow by TC job ID"""
        return self.flow_tracking_dal.get_flow_tracking_by_tc_job_id(tc_job_id)
    
    def get_stuck_flows(self, threshold_minutes: int = 1) -> List:
        """Get flows that are stuck"""
        return self.flow_tracking_dal.get_stuck_flows(threshold_minutes)
    
    def get_waiting_flows(self) -> List:
        """Get flows that are waiting"""
        return self.flow_tracking_dal.get_waiting_flows()
    
    def get_flows_by_station(self, station_code: int) -> List:
        """Get flows for a specific station"""
        return self.flow_tracking_dal.get_flows_by_station(station_code)
    
    def get_healthcheck_data(self, threshold_minutes: int = 1) -> List[Dict]:
        """Get comprehensive healthcheck data"""
        return self.flow_tracking_dal.get_healthcheck_data(threshold_minutes)
    
    def create_stage_history(self, request_id: str, stage: str, timeout_at=None, notes=None) -> int:
        """Create stage history record"""
        data = {
            'request_id': request_id,
            'stage': stage,
            'timeout_at': timeout_at,
            'notes': notes
        }
        return self.flow_tracking_dal.create_stage_history(data)
    
    def get_stage_history(self, request_id: str) -> List:
        """Get stage history for a request"""
        return self.flow_tracking_dal.get_stage_history(request_id)
    
    def cleanup_old_flows(self, max_age_hours: int = 24) -> int:
        """Clean up old completed flows"""
        return self.flow_tracking_dal.cleanup_old_flows(max_age_hours)
    
    def sync_runtime_to_db(self, flow_request):
        """Sync runtime flow request to database"""
        from ...runtime import runtime
        
        # Check if record exists
        existing = self.get_flow_by_request_id(flow_request.request_id)
        
        if existing:
            # Update existing record
            update_data = {
                'current_stage': flow_request.current_stage.value,
                'is_stuck': flow_request.is_stuck,
                'stuck_reason': flow_request.stuck_reason,
                'is_waiting': flow_request.is_waiting,
                'waiting_reason': flow_request.waiting_reason,
                'retry_count': flow_request.retry_count,
                'total_elapsed_minutes': flow_request.get_elapsed_time(),
                'stage_elapsed_minutes': flow_request.get_stage_elapsed_time()
            }
            return self.flow_tracking_dal.update_flow_tracking(flow_request.request_id, update_data)
        else:
            # Create new record
            return self.create_flow_record(
                flow_request.request_id,
                flow_request.station_code,
                flow_request.storage_code,
                flow_request.tc_job_id,
                flow_request.position,
                flow_request.action,
                flow_request.current_stage.value
            )
    
    def sync_all_runtime_flows(self):
        """Sync all runtime flows to database"""
        from ...runtime import runtime
        
        synced_count = 0
        for flow_request in runtime.flow_tracker.active_requests.values():
            if self.sync_runtime_to_db(flow_request):
                synced_count += 1
        
        return synced_count

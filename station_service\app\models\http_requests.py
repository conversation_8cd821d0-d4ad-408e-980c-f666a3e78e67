from typing import Dict, Any
from dataclasses import dataclass
from sqlalchemy.types import ARRAY
from sqlalchemy.dialects.postgresql import JSONB

from .. import db
from .. import enum_collections as ec


@dataclass
class HttpRequests(db.Model):
    id: int 
    method : str
    url : str
    json : dict
    status : str
    headers : dict
    pred_ids : list
    response : dict
    reference : dict
    attempts  : int
    created_at : str
    updated_at : str


    __tablename__ = "http_requests"

    id = db.Column(
        db.Integer,
        primary_key=True
        )

    json = db.Column(
        JSONB,
        )

    status = db.Column(
        db.String(128),
        default=ec.OrderStatus.PROCESSING.value
        )

    method = db.Column(
        db.String(128)
        )

    url = db.Column(
        db.String(128)
        )

    headers = db.Column(
        JSONB
        )
    
    pred_ids = db.Column(
        ARRAY(db.Integer), 
        default=[]
        )
    
    response = db.Column(
        db.JSO<PERSON>()
        )
    
    reference = db.Column(
        JSONB
        )
    
    attempts = db.Column(
        db.Integer(), 
        default=0
        )
    
    created_at = db.Column(
        db.DateTime
    )

    updated_at = db.Column(
        db.DateTime
    )

    def as_dict(self) -> Dict[str, Any]:
        return dict(
            id = self.id,
            method = self.method,
            url = self.url,
            json = self.json,
            headers = self.headers,
            pred_ids = self.pred_ids,
            attempts = self.attempts
        )
from app import db


class Asrs_job(db.Model):
    id: int
    order_id: int
    job_type: str
    job_status: str
    pallet_id: str
    msg: str
    pred_id: int
    succ_id: int
    triggered_by: str
    ack: bool
    created_at: str
    updated_at: str

    __tablename__ = 'asrs_job'
    id = db.Column(
        db.Integer,
        primary_key=True
    )

    order_id = db.Column(
        db.Integer,
        db.ForeignKey('asrs_order.id')
    )

    job_type = db.Column(
        db.String(128),
        index=False,
        nullable=False
    )

    job_status = db.Column(
        db.String(128),
        index=False,
        nullable=False
    )

    pallet_id = db.Column(
        db.String(128),
        index=False
    )
    
    msg = db.Column(
        db.String(128),
        index=False
    )

    pred_id = db.Column(
        db.Integer,
        index=False,
    )

    succ_id = db.Column(
        db.Integer,
        index=False,
    )
    
    triggered_by = db.Column(
        db.String(128),
        nullable = True,
        index = False
    )

    ack = db.Column(
        db.<PERSON>,
        default=False,
        index=False
    )

    created_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

    updated_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

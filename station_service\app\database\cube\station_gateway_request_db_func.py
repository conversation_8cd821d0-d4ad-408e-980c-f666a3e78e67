from typing import List

from ... import models as model,common as common,enum_collections as ec
from .station_gateway_request_dal import StationGatewayReqSQLAlchemyQueries

db_func_log = common.LogManager('cube_db_func',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_func_log))
class StationGatewayReq:
    
    st_gw_req_dal = StationGatewayReqSQLAlchemyQueries()

    def add_gw_request_event(self,station_code:int, type:ec.StationRequestType, tc_job_id:int,storage_code:str,position:int,status =ec.OrderStatus.AVAILABLE.value, event:str = 'request',entity:str = 'TC')->int:
        all_args = locals()  
        if 'self' in all_args:
            del all_args['self']
        req_event_id = self.st_gw_req_dal.add_new_gateway_req(locals())
        return req_event_id
    
    def update_gateway_req_status(self,req_id:int,status:ec.OrderStatus)->int:
        req_id = self.st_gw_req_dal.update_gateway_req(dict(id = req_id),dict(status = status))
        if req_id:
            return req_id
    
    def update_gateway_req_by_id(self,req_id:int,update:dict)->int:
        req = self.st_gw_req_dal.update_gateway_req(dict(id = req_id),update)
        if req:
            return req
        
    def update_gateway_req_status_by_job_id(self,tc_job_id:int,status:ec.OrderStatus)->int:
        req = self.st_gw_req_dal.update_gateway_req(dict(tc_job_id = tc_job_id),dict(status = status))
        if req:
            return req
    
    def update_gateway_req_by_job_id(self,tc_job_id:int,update:dict)->int:
        req = self.st_gw_req_dal.update_gateway_req(dict(tc_job_id = tc_job_id),update)
        if req:
            return req
    
    def get_gateway_req(self,filter_dict:dict):
        req = self.st_gw_req_dal.get_gateway_req(filter_dict)
        return req
    
    def get_latest_gateway_req(self,filter_dict:dict):
        req = self.st_gw_req_dal.get_latest_gateway_req(filter_dict)
        return req
        
    def get_gateway_req_by_id(self,req_id:int)->model.StationGatewayReq:
        req = self.st_gw_req_dal.get_gateway_req(dict(id = req_id))
        return req       

    def get_latest_gateway_req_by_job_id(self,tc_job_id:int)->model.StationGatewayReq:
        req = self.st_gw_req_dal.get_latest_gateway_req(dict(tc_job_id = tc_job_id))
        return req

    def get_broadcastable_gateway_req(self,station_code:int = None)->List[model.StationGatewayReq]:
        ready_reqs = self.st_gw_req_dal.get_broadcastable_gateway_reqs(station_code)
        return ready_reqs

    def start_pending_gw_request(self,station_code:int):
        from . import st_event_db_func
        pending_req = self.get_gateway_req(dict(status=ec.OrderStatus.PENDING.value,station_code=station_code,type=ec.StationRequestType.DROP.value))
        if pending_req:
            self.update_gateway_req_by_id(pending_req.id,dict(status=ec.OrderStatus.AVAILABLE.value))
            # st_event_db_func.create_st_event(ec.CubesIStationEvent.REQ_DROP.value,station_code,pending_req.storage_code,request_id=pending_req.id)
            # st_event_db_func.create_st_event(ec.CubesIStationEvent.DROP_DONE.value,station_code,pending_req.storage_code,request_id=pending_req.id)
            st_event_db_func.create_bundled_events(True,dict(
                station_code = station_code,
                storage_code = pending_req.storage_code,
                request_id = pending_req.id
            ))


    

import json

from datetime import datetime as dt

from app import models as model,app,db
from app.enum_collections import CubeStationType

def clear_db_for_dryrun():
    """
    clear db data to ready for tc_dryrun mode
    """
    from config import Config

    if Config.TC_DRY_RUN_MODE:
        with app.app_context():
            curr_session = db.session
            curr_session.query(model.StationMovement).delete()
            curr_session.query(model.StationGatewayReq).delete()
            curr_session.query(model.StationEvent).delete()
            curr_session.query(model.Stations).delete()
            curr_session.query(model.WebsocketRequests).delete()

            curr_session.commit()
            
def clearDB():
    """
    clear all db data
    """
    with app.app_context():
        curr_session = db.session
        # Clear previous seeded data
        curr_session.query(model.HttpRequests).delete()
        curr_session.query(model.StationErrors).delete()
        curr_session.query(model.StationEvent).delete()
        curr_session.query(model.StationGatewayReq).delete()
        curr_session.query(model.StationMovement).delete()
        curr_session.query(model.StationOrder).delete()
        curr_session.query(model.StationRecord).delete()
        curr_session.query(model.Stations).delete()
        curr_session.query(model.WebsocketRequests).delete()
        
        curr_session.commit()

def seedPentaCubeStations():
    """
    Seed cube staiton  
    """
    from config import Config

    with app.app_context():
        curr_session = db.session

        add_station = model.Stations(
            code=1,
            zone="A" if not Config.TC_DRY_RUN_MODE else "C",
            type=CubeStationType.REGULAR.value,
            rotation="C",
            inner_drop = 0,
            inner_pick = 5,
            worker=  3,
            cell=6,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        add_station = model.Stations(
            code=2,
            zone="A",
            type=CubeStationType.REGULAR.value,
            rotation="C",
            inner_drop = 0,
            inner_pick = 5,
            worker = 3 ,
            cell=6,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        add_station = model.Stations(
            code=3,
            zone="A",
            type=CubeStationType.REGULAR.value,
            rotation="C",
            inner_drop = 0,
            inner_pick = 5,
            worker= 3,
            cell=6,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        add_station = model.Stations(
            code=4,
            zone="A",
            type=CubeStationType.REGULAR.value,
            rotation="C",
            inner_drop = 0,
            inner_pick = 5,
            worker = 3,
            cell=6,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        add_station = model.Stations(
            code=5,
            zone="A",
            type=CubeStationType.REGULAR.value,
            rotation="C",
            inner_drop = 0,
            inner_pick = 5,
            worker = 3,
            cell=6,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        add_station = model.Stations(
            code=6,
            zone="A",
            type=CubeStationType.REGULAR.value,
            rotation="C",
            inner_drop = 0,
            inner_pick = 5,
            worker=  3,
            cell=6,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        add_station = model.Stations(
            code=7,
            zone="A",
            type=CubeStationType.BRIDGE.value,
            rotation="C",
            adjacent = 11,
            inner_drop = 0,
            inner_pick = 5,
            worker= 3,
            cell=6,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )  # might change to cater special case A -> B zone bin movement
        curr_session.add(add_station)

        add_station = model.Stations(
            code=8,
            zone="A",
            type=CubeStationType.REGULAR.value,
            rotation="C",
            inner_drop = 0,
            inner_pick = 5,
            worker = 3,
            cell=6,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        add_station = model.Stations(
            code=9,
            zone="C",
            type=CubeStationType.REGULAR.value,
            rotation="C",
            inner_drop = 0,
            inner_pick = 11,
            worker = 6,
            cell=12,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )  # change rotation to nullable in future if needed (low priority)
        curr_session.add(add_station)

        add_station = model.Stations(
            code=10,
            zone="C",
            type=CubeStationType.REGULAR.value,
            rotation="C",
            inner_drop = 0,
            inner_pick = 11,
            worker= 6,
            cell=12,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )  # change rotation to nullable in future if needed (low priority)
        curr_session.add(add_station)

        add_station = model.Stations(
            code=11,
            zone="C",
            type=CubeStationType.REGULAR.value,
            rotation="C",
            inner_drop = 0,
            inner_pick = 11,
            worker = 6,
            cell=12,
            adjacent = 7,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        add_station = model.Stations(
            code=12,
            zone="C",
            type=CubeStationType.REGULAR.value,
            rotation="C",
            inner_drop = 0,
            inner_pick = 11,
            worker = 6,
            cell=12,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        add_station = model.Stations(
            code=13,
            zone="C",
            type=CubeStationType.REGULAR.value,
            rotation="C",
            inner_drop = 0,
            inner_pick = 5,
            worker= 3,
            cell=6,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        add_station = model.Stations(
            code=14,
            zone="C",
            type=CubeStationType.REGULAR.value,
            rotation="C",
            inner_drop = 0,
            inner_pick = 5,
            worker = 3,
            cell=6,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        add_station = model.Stations(
            code=15,
            zone="C",
            type=CubeStationType.REGULAR.value,
            rotation="C",
            inner_drop = 0,
            inner_pick = 5,
            worker = 3,
            cell=6,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        add_station = model.Stations(
            code=16,
            zone="C",
            type=CubeStationType.REGULAR.value,
            rotation="C",
            inner_drop = 0,
            inner_pick = 5,
            worker = 3,
            cell=6,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        add_station = model.Stations(
            code=17,
            zone="C",
            type=CubeStationType.QC.value,
            rotation="C",
            inner_drop = 13,
            inner_pick = 26,
            outer_drop = 10,
            outer_pick = 37,
            worker = 42,
            cell=42,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)
        curr_session.commit()
    
    return "All station has been seeded"

def seedVitroxStations():
    """
    Seed cube station for Vitorx service
    """
    with app.app_context():
        
        curr_session = db.session

        i = 1
        matrix_code = 1

        add_station = model.Stations(
                    code=i,
                    matrix_code=matrix_code,
                    zone="C",
                    type=CubeStationType.LARGE.value,
                    rotation="C",
                    plc_status="|||||",
                    inner_drop = 1,
                    inner_pick = 4,
                    outer_drop = 0,
                    outer_pick = 5,
                    worker=  3,
                    cell = 6,
                    queue = "[]",
                    is_active=False,
                    created_at=dt.now(),
                    updated_at=dt.now()
                )
        
        curr_session.add(add_station)
        
        i+=1 
        matrix_code+=1

        add_station = model.Stations(
                code=i,
                matrix_code=matrix_code,
                zone="C",
                type=CubeStationType.LARGE.value,
                rotation="C",
                plc_status="|||||",
                inner_drop = 1,
                inner_pick = 4,
                outer_drop = 0,
                outer_pick = 5,
                worker=  3,
                cell=6,
                queue = "[]",
                is_active=False,
                created_at=dt.now(),
                updated_at=dt.now()
            )
        curr_session.add(add_station)

        i+= 1
        matrix_code+=1

        empty_list = ["","",""]
        str_queue = json.dumps(empty_list)

        while i <=16:

            add_station = model.Stations(
            code=i,
            matrix_code = matrix_code,
            zone="C",
            type=CubeStationType.I.value,
            rotation="",
            plc_status="||",
            inner_drop = 1,
            inner_pick = 1,
            worker=  0,
            cell=3,
            queue = str_queue,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
            )

            curr_session.add(add_station)

            i += 1

            add_station = model.Stations(
            code=i,
            matrix_code = matrix_code,
            port=1001,
            zone="C",
            type=CubeStationType.I.value,
            rotation="",
            plc_status="||",
            inner_drop = 1,
            inner_pick = 1,
            worker=  0,
            cell=3,
            queue = str_queue,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
            )
            curr_session.add(add_station)

            i+=1
            matrix_code+=1
        
        curr_session.commit()

        return f"Seeded {i-1} stations"

def seedVitrox2Stations():
    """
    Seed cube station for Vitorx service
    """
    with app.app_context():
        
        curr_session = db.session
        counter = 1
        i = 1
        matrix_code=10

        empty_list = ["","",""]
        str_queue = json.dumps(empty_list)

        while i <=13:

            add_station = model.Stations(
            code=i,
            matrix_code = matrix_code,
            zone="C",
            type=CubeStationType.I.value,
            rotation="",
            plc_status="||",
            inner_drop = 1,
            inner_pick = 1,
            worker=  0,
            cell=3,
            queue = str_queue,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
            )

            curr_session.add(add_station)

            if i>3:

                i += 1
                counter+=1

                add_station = model.Stations(
                code=i,
                matrix_code = matrix_code,
                port=1001,
                zone="C",
                type=CubeStationType.I.value,
                rotation="",
                plc_status="||",
                inner_drop = 1,
                inner_pick = 1,
                worker=  0,
                cell=3,
                queue = str_queue,
                is_active=False,
                created_at=dt.now(),
                updated_at=dt.now()
                )
                curr_session.add(add_station)
                
            i+=1
            matrix_code+=1
            counter+=1

        curr_session.commit()

        return f"Seeded {counter-1} stations"

def seedSubangStations():
    with app.app_context():

        curr_session = db.session
        
        add_station = model.Stations(
            code=1,
            zone="C",
            type=CubeStationType.REGULAR.value,
            inner_drop = 0,
            inner_pick = 6,
            worker=  3,
            cell=7,
            rotation = "C",
            plc_status = "||||||",
            queue = "[]",
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        add_station = model.Stations(
            code=2,
            zone="C",
            type=CubeStationType.REGULAR.value,
            inner_drop = 0,
            inner_pick = 6,
            worker=  3,
            cell=7,
            rotation="C",
            plc_status="||||||",
            queue = "[]",
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        curr_session.commit()

        return f"Seeded {2} stations"

def seedZoneDStations():
    with app.app_context():

        curr_session = db.session

        empty_list = ["","",""]
        str_queue = json.dumps(empty_list)
        
        add_station = model.Stations(
            code=1,
            matrix_code=1,
            zone="C",
            type=CubeStationType.I.value,
            rotation="",
            inner_drop = 1,
            inner_pick = 1,
            worker=  0,
            cell=3,
            plc_status="||",
            queue = str_queue,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        add_station = model.Stations(
            code=2,
            matrix_code=1,
            port = 1001,
            zone="C",
            type=CubeStationType.I.value,
            rotation="",
            inner_drop = 1,
            inner_pick = 1,
            worker=  0,
            cell=3,
            plc_status="||",
            queue = str_queue,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        add_station = model.Stations(
            code=3,
            matrix_code=2,
            zone="C",
            type=CubeStationType.LARGE.value,
            rotation="C",
            inner_drop = 1,
            inner_pick = 4,
            outer_drop = 0,
            outer_pick = 5,
            worker = 3,
            cell=6,
            plc_status="|||||",
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        curr_session.commit()

        return f"Seeded {2} stations"

def seedWiragoStations():
    """
    Seed cube station for Wirago service
    """
    with app.app_context():
        
        curr_session = db.session
        empty_list = ["","",""]
        str_queue = json.dumps(empty_list)
        add_station = model.Stations(
            code=1,
            matrix_code=1,
            zone="C",
            type=CubeStationType.I.value,
            inner_drop = 1,
            inner_pick = 1,
            worker=  0,
            cell=3,
            rotation="",
            plc_status = "||",
            queue = str_queue,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        add_station = model.Stations(
            code=2,
            matrix_code=2,
            zone="C",
            type=CubeStationType.I.value,
            inner_drop = 1,
            inner_pick = 1,
            worker=  0,
            cell=3,
            rotation="",
            plc_status = "||",
            queue = str_queue,
            is_active=False,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_station)

        curr_session.commit()

        return f"Seeded 2 stations"
        
        


def seed_station(type :CubeStationType,code:int):
    """
    Seed a specific type of station with a specific code

    Args:
        type (CubeStationType): the station type to seed
        code (int): station code 
    """

    with app.app_context():

        curr_session = db.session

        if type == CubeStationType.REGULAR.value:

            cell = int(input(f"Please enter the number of cell for REGULAR station you want to seed: "))

            if cell == 6:
                add_station = model.Stations(
                        code=code,
                        zone="C",
                        type=CubeStationType.REGULAR.value,
                        rotation="C",
                        inner_drop = 0,
                        inner_pick = 5,
                        worker=  3,
                        cell=6,
                        plc_status="|||||",
                        is_active=False,
                        created_at=dt.now(),
                        updated_at=dt.now()
                    )
            elif cell == 7:
                add_station = model.Stations(
                        code=code,
                        zone="C",
                        type=CubeStationType.REGULAR.value,
                        rotation="C",
                        inner_drop = 0,
                        inner_pick = 6,
                        worker=  3,
                        cell=7,
                        plc_status="||||||",
                        is_active=False,
                        created_at=dt.now(),
                        updated_at=dt.now()
                    )
            elif cell == 12:
                add_station = model.Stations(
                        code=code,
                        zone="C",
                        type=CubeStationType.REGULAR.value,
                        rotation="C",
                        inner_drop = 0,
                        inner_pick = 11,
                        worker=  6,
                        cell=12,
                        plc_status="|||||||||||",
                        is_active=False,
                        created_at=dt.now(),
                        updated_at=dt.now()
                    )

        if type == CubeStationType.I.value:

            empty_list = ["","",""]
            str_queue = json.dumps(empty_list)
            
            add_station = model.Stations(
                    code=code,
                    zone="C",
                    type=CubeStationType.I.value,
                    rotation="",
                    inner_drop = 1,
                    inner_pick = 1,
                    worker=  0,
                    cell=3,
                    plc_status="||",
                    queue = str_queue,
                    is_active=False,
                    created_at=dt.now(),
                    updated_at=dt.now()
                )    
            
        if type == CubeStationType.LARGE.value:
            
            cell = int(input(f"Please enter the number of cell for LARGE station you want to seed: "))

            if cell == 12:
                add_station = model.Stations(
                    code=code,
                    zone="C",
                    type=CubeStationType.LARGE.value,
                    rotation="C",
                    inner_drop = 1,
                    inner_pick = 10,
                    outer_drop = 0,
                    outer_pick = 11,
                    worker = 6,
                    cell=12,
                    plc_status="|||||||||||",
                    is_active=False,
                    created_at=dt.now(),
                    updated_at=dt.now()
                )  # change rotation to nullable in future if needed (low priority)   

            elif cell==6:
                add_station = model.Stations(
                    code=code,
                    zone="C",
                    type=CubeStationType.LARGE.value,
                    rotation="C",
                    inner_drop = 1,
                    inner_pick = 4,
                    outer_drop = 0,
                    outer_pick = 5,
                    worker = 3,
                    cell=6,
                    plc_status="|||||",
                    is_active=False,
                    created_at=dt.now(),
                    updated_at=dt.now()
                )

        if type == CubeStationType.BRIDGE.value:
            add_station = model.Stations(
                code=code,
                zone="C",
                type=CubeStationType.BRIDGE.value,
                rotation="C",
                inner_drop = 0,
                inner_pick = 5,
                worker= 3,
                cell=6,
                is_active=False,
                created_at=dt.now(),
                updated_at=dt.now()
            )  # might change to cater special case A -> B zone bin movement 

        if type == CubeStationType.QC.value:
            add_station = model.Stations(
                code=code,
                zone="C",
                type=CubeStationType.QC.value,
                rotation="C",
                inner_drop = 13,
                inner_pick = 26,
                outer_drop = 10,
                outer_pick = 37,
                worker = 42,
                cell=42,
                is_active=False,
                created_at=dt.now(),
                updated_at=dt.now()
            )

        curr_session.add(add_station)
        curr_session.commit()    


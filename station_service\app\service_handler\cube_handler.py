import datetime
import time
import pytz

from termcolor import colored
from threading import current_thread

from config import Config
from ..error_handling import CubeEH
from ..blueprints.route import client as http_client
from ..communications_provider import socketio as sio
from .. import app,runtime as rt,adapter as adp,database as db,models as model,common as common,enum_collections as ec, validation as validation

enrollment_log = common.LogManager('cube_enrollment',centralize_logging=True)
handler_log = common.LogManager('cube_handler',display_console=True)
cube_inner_error_log = common.LogManager('cube_inner_error')
tcp_server_log = common.LogManager('cube_TCP_server',display_console=True,centralize_logging=True)
service_door_log = common.LogManager('cube_service_door',centralize_logging=True)
emo_log = common.LogManager('cube_emo')
heartbeat_log = common.LogManager('cube_heartbeat',centralize_logging=True)

msg_dict = dict()

utc = pytz.UTC

class MessageHandler:
    
    @classmethod
    def create_instance(cls,message:str,tcp_client:rt.TCPClient,is_validate:bool)->None:
        splited_msg = message.split(',')
        if splited_msg[0] == 'ACK':
            module_code = splited_msg[1]
        else:
            module_code= splited_msg[0]
        
        if module_code == ec.ModuleCode.STATION.value:
            return StationMessageHandler(message,tcp_client,is_validate)
        elif module_code == ec.ModuleCode.EMO.value:
            return EMOMessageHandler(message,tcp_client,is_validate)
        else:
            return ServiceDoorMessageHandler(message,tcp_client,is_validate)

class StationMessageHandler:

    def __init__(self,message:str,tcp_client:rt.TCPClient,is_validate:bool)->None:
        self.message = message
        self.splited_msg = message.split(',')
        self.tcp_client = tcp_client
        self.is_validate = is_validate

        header = self.splited_msg[0]

        if header == ec.CubesProtocol.ACK.value:
            self.station_code = int(self.splited_msg[2])
            self.command = self.splited_msg[3]
            self.is_ack = True
        else:
            self.station_code = int(self.splited_msg[1])
            self.command = self.splited_msg[2]
            self.is_ack = False
        
        self.station = db.st_db_func.get_station_by_code(self.station_code)
        
        if not self.station:
            print(common.formatString('ERROR',colored(f"Received Message from unregistered ST{self.station_code}: "))+f'{self.message}')
            raise Exception(f"No ST{self.station_code} registered")

        # log msg 
        if not self.command in [ec.CubesProtocol.H.value]:
            print(common.formatString('INFO',colored(f"Received Message from ST{self.station_code}: ",'light_cyan'))+f'{self.message}')
            self.station.log.msg_log(self.station_code,f"Received Message from ST{self.station_code}: {self.message}") 

    def process(self):
        
        # start process
        if self.is_ack:
            self.handle_ack_msg()
            
        else:
            # can be handle before pairing 
            match self.command:
                case ec.CubesProtocol.H.value:
                    self.handle_heartbeat_msg()
                case ec.CubesProtocol.P.value:
                    self.handle_pairing_msg()
                case ec.CubesProtocol.ERROR.value:
                    self.handle_error_msg()
                case ec.CubesProtocol.START.value:
                    self.handle_resolve_error_msg()
                case ec.CubesProtocol.STOP.value:
                    self.handle_estop_msg()
                case ec.CubesProtocol.SHELL.value:
                    self.handle_shell_msg()
                case ec.CubesProtocol.W.value:
                    sio.CubeWSEmit.emit_weight_changed_event(self.station_code,int(self.splited_msg[3]),self.splited_msg[4])
                case ec.CubesProtocol.OW.value:
                    self.handle_overweight_msg()  
                case ec.CubesProtocol.S.value:
                    self.handle_status_msg()
                case ec.CubesProtocol.J.value:
                    self.handle_job_done_msg()
                case ec.CubesProtocol.L.value:
                    self.handle_light_msg()
                case ec.CubesProtocol.Q.value:
                    self.handle_job_request_msg()
                case ec.CubesProtocol.E.value:
                    self.handle_enroll_msg()
                case ec.CubesProtocol.A.value | ec.CubesProtocol.D.value :
                    self.handle_gw_request_responsd_msg()
                case ec.CubesProtocol.AC.value:
                    self.handle_gw_cancel_request_respond_msg()
                case _:
                    self.handle_invalid_msg()
   
    def handle_heartbeat_msg(self):
        if self.station.is_active:
            self.station.last_ping_time = datetime.datetime.now()
            heartbeat_log.info(self.message)
            send_tcp_msg(self.station_code, common.STMsgFormatter.heartbeat_reply_msg(self.station_code))

    def handle_ack_msg(self):
        job_id = int(self.splited_msg[4])
        if self.command == ec.CubesProtocol.M.value:
            success,msg = db.st_mov_db_func.set_plc_ack(job_id,True)
            if not success:
                handler_log.error(msg)
        elif self.command == ec.CubesProtocol.DO.value:
            db.st_mov_db_func.set_complete(job_id,True)
        elif self.command == ec.CubesProtocol.B.value:
            bridge_job_info = self.splited_msg[5]
            split_bridge_job_info = bridge_job_info.split("|")
            if db.st_mov_db_func.set_plc_ack(job_id, True)[0]:
                job = db.st_mov_db_func.find_job_by_id(job_id)
                if job.type == ec.CubeStationJobType.BRIDGE.value:
                    sio.CubeWSEmit.emit_bridge_arrival_event(self.station_code,int(split_bridge_job_info[3]))
        elif self.command == ec.CubesProtocol.U.value:
            tc_job_id = int(self.splited_msg[4])
            update_info = self.splited_msg[5].split('|')
            update_index = int(update_info[0])
            update_action = update_info[1]
            update_bin = update_info[2]

            station = db.st_db_func.get_station_by_code(self.station_code)
   
            update_bin = update_info[2].lstrip("0")        
            if update_action == ec.StationRequestType.DROP.value:
                station.latest_drop_msg[update_index] = ''
                station.record_gw_status(True,update_index,f'Station ACK droppping bin {update_bin}, req id : {tc_job_id}')
            if update_action == ec.StationRequestType.PICK.value:  # Only pick jobs need to flag db. Drop update not under HWX jurisdiction
                if update_index in [self.station.inner_pick,self.station.outer_pick]:
                    db.st_mov_db_func.set_update_complete(update_bin,self.station_code)
                    station.latest_pick_msg[update_index] = ''      
                    station.record_gw_status(False,update_index,f'Station ACK picking bin {update_bin}, req id : {tc_job_id}')
                else:
                    handler_log.error(f'Invalid pick index {update_index} for ST{self.station_code}')
            
            sio.CubeWSEmit.emit_station_update_event(self.station_code,tc_job_id)
        elif self.command == ec.CubesProtocol.L.value:
            self.handle_light_msg()
        elif self.command in [ec.CubesProtocol.REMOVE.value]:
            from ..common.background_task import BackgroundTask

            BackgroundTask.stop_retry(self.message)
    def handle_pairing_msg(self):

        def handle_recovery_pairing(msg_handler:StationMessageHandler):
            if msg_handler.splited_msg[3].upper() == ec.StationPairingMsg.PAIRING.value:
                msg = common.STMsgFormatter.recovery_status_msg(self.station_code,self.station.plc_status)
                self.tcp_client.conn.send(msg.encode())
                self.station.log.msg_log(self.station_code,f"Sent Message to {ec.ModuleCode.STATION.value}{self.station_code}: {msg}")
                print(common.formatString('INFO',colored(f"Sent Message to {ec.ModuleCode.STATION.value}{self.station_code}: ",'light_blue')+msg))
                return True
            elif msg_handler.splited_msg[3].upper() == ec.StationPairingMsg.RECOVERY.value:
                # ST,1,P,RECOVERY,1235|1234|,SUCCESS/S;
                success_recovery = False
                if msg_handler.splited_msg[5][0].upper() == 'S':
                    # update status recovery status
                    db.st_db_func.update_station(msg_handler.station_code,dict(is_recovery=False))
                    # resend all the job of this station to plc 
                    station_jobs = db.st_mov_db_func.find_all_active_broadcast_jobs(self.station.id)
                    for job in station_jobs:
                        if db.st_mov_db_func.check_job_broadcastable(job,self.station):
                            db.st_mov_db_func.reset_job(job.id)
                
                    success_recovery = True
                
                else:
                    handler_log.error(f'ST{msg_handler.station_code} fail to recover, please check on PLC side.')
                    success_recovery = False

                sio.CubeWSEmit.emit_recovery_status(self.station_code,success_recovery)
                return not success_recovery

        if Config.RECOVERY_MODE and self.station.is_recovery:
            if skip_pairing := handle_recovery_pairing(self):
                return
        
        if self.station.is_active:
            if self.station.connection == self.tcp_client.hash:
                send_tcp_msg(self.station_code,common.STMsgFormatter.pairing_reply_msg(self.station_code))
            else:
                handler_log.error(f'ST{self.station_code} connection in HCC is registered with the other sender.')
            return 

        if Config.RANDOM_STATION_ADDRESS:
            db.st_db_func.station_connecting(self.station_code,self.tcp_client.hash,self.tcp_client.host,self.tcp_client.port)
            # If dry run mode enable, reply bin position to mock PLC client.
            # location_str = db.st_mov_db_func.get_bin_queue_indexing(self.station_code)
            # send_tcp_msg(self.station_code,common.STMsgFormatter.initialize_msg(self.station_code,location_str))

        if not self.station.is_connected:
            handler_log.error(f'ST{self.station_code} is not connected but pairing')
            return 
        
        self.on_station_paired(self.station)
        self.station.last_ping_time = datetime.datetime.now()
        send_tcp_msg(self.station_code,common.STMsgFormatter.pairing_reply_msg(self.station_code))
        sio.CubeWSEmit.emit_active_station()
        http_client.HttpClient.update_st_status(self.station_code)        
        time.sleep(0.1)
        send_tcp_msg(self.station_code,common.STMsgFormatter.status_msg(self.station_code))

    def handle_error_msg(self):
        
        error_list = self.splited_msg[3:]
        send_tcp_msg(self.station_code,"ACK,"+self.message+";")
        CubeEH.create_station_error_msg(self.station_code,error_list)
        
    def handle_resolve_error_msg(self):
        send_tcp_msg(self.station_code,"ACK,"+self.message+";")
        CubeEH.reset_station_error_msg(self.station_code)
                            
    def handle_estop_msg(self):
        msg_to_send = f'ACK,{self.message};'
        self.tcp_client.conn.send(msg_to_send.encode())
        self.station.log.msg_log(self.station_code,f"Sent Message to {ec.ModuleCode.STATION.value}{self.station_code}: {msg_to_send}")
        print(common.formatString('INFO',colored(f"Sent Message to {ec.ModuleCode.STATION.value}{self.station_code}: ",'light_blue')+msg_to_send))                                
        # check if client is already connected , only handle stop command after client connecetd
        if Config.RANDOM_STATION_ADDRESS:
            from ..server.station_server import close_socket_connection

            close_socket_connection(self.tcp_client.conn)
        else:  
            if self.station.is_active:
                self.on_station_unpaired(self.station,True)

    def handle_shell_msg(self):
        common.notify_shell(self.message,True,True)

    def handle_status_msg(self):
        bin_status = self.splited_msg[3]
        splited_bin_status = bin_status.split('|')
        plc_status_len= len(splited_bin_status)
        
        if self.station.type in [ec.CubeStationType.BRIDGE.value,ec.CubeStationType.QC.value]:
            return 

        if plc_status_len != self.station.cell:
            error_log_and_print(cube_inner_error_log,self.station_code,self.message,f"Invalid plc status, given size {plc_status_len} for station with cell,{self.station.cell}." )
            return    
            
        db.st_db_func.update_station(self.station_code,dict(plc_status=bin_status))

    def handle_job_done_msg(self):
        msg_job_id = self.splited_msg[3]
        job_info = self.splited_msg[4].split('|')
        
        msg_from_index = job_info[0]
        msg_to_index = job_info[1] 
        msg_bin_no = job_info[3].lstrip("0")

        if not Config.MOCK_STATION_MSG:
            send_tcp_msg(self.station_code,"ACK,"+self.message+";")

        # region job message without job id            
        if msg_job_id in ['',None,"-1","NULL"]:
            
            handler_log.warning(f'Received a command with invalid job ID from PLC, Will recover for TC. msg: {self.message}')

            job = db.st_mov_db_func.recover_regular_job(self.station.id,msg_bin_no,msg_from_index,msg_to_index)
                        
            #recover bridge job 
            if job is None and self.station.adjacent:
                adj_station : model.Stations = db.st_db_func.get_station_by_code(self.station.adjacent)
                if adj_station.type == ec.CubeStationType.BRIDGE.value:
                    job = db.st_mov_db_func.recover_bridge_job(msg_bin_no,msg_to_index,adj_station.id)
            
            if job is None:
                error_log_and_print(cube_inner_error_log,self.station_code,self.message,f"Job still None after tried all attempt to recover")
                return
            else:  
                msg_from_index = job.from_index
                msg_to_index = job.to_index
                msg_job_id = job.id
                mock_job_done = f'{ec.ModuleCode.STATION.value},{self.station_code},J,{job.id},{job.from_index}|{job.to_index}|{self.station.rotation}|{job.bin_no}'
                handler_log.warning(f'Replaced {self.message} with recovered job done: {mock_job_done}')                                        
        # endregion

        # job message with job id
        else:
            job = db.st_mov_db_func.find_job_by_id(int(msg_job_id))

        if job:
            valid = self.validate_job_done_msg(job,msg_bin_no,msg_from_index,msg_to_index)
            if not valid:
                return 

            if Config.MOCK_STATION_MSG:
                send_tcp_msg(self.station_code,"ACK,"+self.message+";")

            db.st_db_func.update_station_after_job_done(job,self.station_code,str(job.bin_no))
            db.st_db_func.update_station_queue_after_job_done(job,self.station_code,str(job.bin_no))
            db.st_mov_db_func.set_complete(job.id)
            if self.station.type == ec.CubeStationType.I.value:
                if job.type == ec.CubeStationJobType.MOVE_TO_WORK.value:
                    light_up_job = db.st_mov_db_func.find_succ_job(job.id)
                    if light_up_job:
                        db.st_mov_db_func.set_pending(light_up_job.id)
            if job.type == ec.CubeStationJobType.BRIDGE.value:
                bridge_order_id = db.st_order_db_func.update_order(dict(id=job.order_id),dict(status=ec.OrderStatus.COMPLETED.value))
                bridge_order = db.st_order_db_func.find_order(dict(id=bridge_order_id))
                adj_st_order_id = db.st_order_db_func.add_order(self.station_code,job.bin_no,bridge_order.tc_order_id,ec.OrderType.NORMAL.value)
                db.st_mov_db_func.create_station_jobs(self.station.code,job.bin_no,adj_st_order_id,self.station.worker,bridge=True,bridge_job_id=job.id)
            sio.CubeWSEmit.emit_job_done_event(job,self.station_code,int(job.bin_no),self.station)
            # if config.Config.SIMULATION_PLC or config.Config.MOCK_STATION_MSG:
            #     if job.type == ec.CubeStationJobType.MOVE_TO_WORK.value:
            #         rt.runtime.msg_received_queue.put(
            #             MessageHandler.create_instance(f'ST,{self.station_code},{ec.CubesProtocol.L.value},{msg_bin_no},{ec.ToggleType.ON.value}',None,True))
        
        # no job found
        else:
            error_log_and_print(cube_inner_error_log,self.station_code,self.message,f'No job found for msg')
            return 
        
    def handle_light_msg(self):
        """
        Only for I station will have this protocol to handle when bin arrive PLC decide which bin to handle first
        """
        storage_code = self.splited_msg[4]
        # check if order in station 
        order = db.st_order_db_func.find_active_order(self.station_code,storage_code)

        if not order:
            error_log_and_print(cube_inner_error_log,self.station_code,self.message,f"No order found for ST{self.station_code} with bin {storage_code}")
            return 
        
        if self.splited_msg[5] == ec.ToggleType.ON.value: # UP 
            light_up_job = db.st_mov_db_func.find_job(dict(order_id = order.id,
                                                            type = ec.CubeStationJobType.LIGHT_UP.value))
        
            if not light_up_job:
                error_log_and_print(cube_inner_error_log,self.station_code,self.message,f'No job found for msg')
                return False
            
            # check if pred job not done yet
            if light_up_job.status == ec.OrderStatus.COMPLETED.value:
                error_log_and_print(cube_inner_error_log,self.station_code,self.message,f"Receive light up message for a completed job, job id {light_up_job.id}")
                return False
            
            if not db.st_mov_db_func.check_pred_job_completed(light_up_job):
                error_log_and_print(cube_inner_error_log,self.station_code,self.message,f"Will cause skipping error, pred job is not complete. Unable to process this msg")
                return False
            
            invalid, invalid_msg = db.st_mov_db_func.invalid_condition(light_up_job, self.station_code)
            if invalid:
                error_log_and_print(cube_inner_error_log,self.station_code,self.message,f"{invalid_msg}")
                return False 
            
            db.st_mov_db_func.set_complete(light_up_job.id,True)
            sio.CubeWSEmit.emit_job_done_event(light_up_job,self.station_code,int(light_up_job.bin_no),self.station)
        else:   # DOWN
            light_down_job = db.st_mov_db_func.find_job(dict(order_id = order.id,
                                                            type = ec.CubeStationJobType.LIGHT_DOWN.value))
            
            db.st_mov_db_func.set_complete(light_down_job.id,True)
            db.st_db_func.update_station_after_job_done(light_down_job,self.station_code,str(light_down_job.bin_no))
            if self.station.mode != ec.CubeStationMode.TRANSFER.value:
                db.st_mov_db_func.light_up_next_bin(self.station.matrix_code)

                    
                     
            # start next light up job 
            # sister_station = common.MatrixConverter.get_matrix_station_list(self.station.matrix_code,self.station_code)
            # for sis_st in sister_station:
            #     latest_lu_job = db.st_mov_db_func.find_job(dict(type=ec.CubeStationJobType.LIGHT_UP.value,
            #                                                     status=ec.OrderStatus.AVAILABLE.value,
            #                                                     station_id=sis_st.id))
            #     if latest_lu_job:
            #         pred_mtw_job = db.st_mov_db_func.find_job_by_id(latest_lu_job.pred_id)
            #         if pred_mtw_job.status == ec.OrderStatus.COMPLETED.value:
            #             rt.runtime.msg_received_queue.put(
            #                 MessageHandler.create_instance(f'ST,{sis_st.code},{ec.CubesProtocol.L.value},{latest_lu_job.bin_no},{ec.ToggleType.ON.value}',None,True))
            #             break
                        
    def handle_overweight_msg(self):
        send_tcp_msg(self.station_code,"ACK,"+self.message+";")
        if self.splited_msg[3] == ec.ToggleType.ON.value:
            if self.station.bin_at_worker:
                order = db.st_order_db_func.find_active_order(self.station_code,self.station.bin_at_worker)
                if order.type == ec.OrderType.BRIDGE.value:
                    return
            else:
                return 
        db.st_db_func.update_station(self.station_code,dict(is_overweight=self.splited_msg[3] == ec.ToggleType.ON.value))
        http_client.HttpClient.update_st_status(self.station_code)

    def validate_job_done_msg(self,job:model.StationMovement,msg_bin_no:str,msg_from_index:str,msg_to_index:str)->bool:
        # check job type                    
        if job.type in [ec.CubeStationJobType.NEXT_BIN.value,ec.CubeStationJobType.UPDATE_PICK.value]:
            error_log_and_print(cube_inner_error_log,self.station_code,self.message,f"Can't complete this type of job ({job.type}) with J command")
            return False

        # check sync storage code 
        if job.bin_no != str(msg_bin_no):
            error_log_and_print(cube_inner_error_log,self.station_code,self.message,f'Job with job id {job.id} should have storage code {job.bin_no}, but station provide {msg_bin_no}. Unable to process this msg')
            return False 
        
        # check if it is completed already
        if job.status == ec.OrderStatus.COMPLETED.value:
            current_time = datetime.datetime.now()
            interval = datetime.timedelta(days = 1)
            if job.updated_at < utc.localize(current_time - interval):
                error_log_and_print(cube_inner_error_log,self.station_code,self.message,f"Receive job done message for a job which is completed at least 1 days ago, please check this error, job id {job.id}")
                return False 
            
            else:
                error_log_and_print(cube_inner_error_log,self.station_code,self.message,f"Receive job done message for a completed job, job id {job.id}")
                return False
        
        # check for pred_job done
        if not db.st_mov_db_func.check_pred_job_completed(job):
            error_log_and_print(cube_inner_error_log,self.station_code,self.message,f'Will cause skipping error, pred job is not complete. Unable to process this msg')
            return False  
                                    
        # check on the index
        if (msg_from_index not in ['',None] and job.from_index != int(msg_from_index)) or (msg_to_index not in ['',None] and job.to_index != int(msg_to_index)):
            error_log_and_print(cube_inner_error_log,self.station_code,self.message,f'Wrong index for job id {job.id}, Should have {job.from_index} to {job.to_index} , but given {msg_from_index} to {msg_to_index}. Unable to process this msg')
            return False 
                                                    
        # check whether the job done message skip the queue in the station
        cut_queue , cut_queue_msg = db.st_mov_db_func.is_cut_queue(job, self.station_code)
        if cut_queue:
            error_log_and_print(cube_inner_error_log,self.station_code,self.message,f"Message '{self.message}' is cutting the queue of the station. {cut_queue_msg}")
            return False            
        
        # check whether there is invalid situation occur 
        # Ex: the previos bin has not next bin yet but recevied job done message from drop point to workpoint  
        invalid, invalid_msg = db.st_mov_db_func.invalid_condition(job, self.station_code)
        if invalid:
            error_log_and_print(cube_inner_error_log,self.station_code,self.message,f"Index {job.to_index} is not clear.{invalid_msg}")
            return False 
        
        return True
    
    def handle_job_request_msg(self):
        # for plc to request move command again
        storage_code = self.splited_msg[3]
        job = db.st_order_db_func.find_next_job_to_run(self.station_code,storage_code)
        if job != None:
            if job.type == ec.CubeStationJobType.BRIDGE.value:
                reply_to_return = common.STMsgFormatter.bridge_msg(self.station_code,job.id,job.from_index,job.to_index,self.station.rotation,job.bin_no)
            elif job.type == ec.CubeStationJobType.MOVE_TO_WORK.value:
                reply_to_return = common.STMsgFormatter.move_msg(self.station_code,job.id,job.from_index,job.to_index,self.station.rotation,job.bin_no)
            else:
                handler_log.warning(f'No MOVE_TO_WORK/BRIDGE ready for bin {self.splited_msg[3]} : {self.message}')
                return 
            send_tcp_msg(self.station_code,reply_to_return)
        else:
            handler_log.warning(f'Invalid question request: {self.message}')

    def handle_enroll_msg(self):
        job_info = self.splited_msg[3].split('|')
        storage_code = int(job_info[1])
        str_storage_code = str(storage_code)

        if self.station.mode != ec.CubeStationMode.ENROLL.value:
            handler_log.error("Received enroll msg but station is not in ENROLL mode, will ignore this message.")
            return
        
        if db.st_order_db_func.check_enroll_order_exist(str_storage_code):
            msg=f'Received duplicate enroll bin {str_storage_code}, will ack and ignore this message.'
            enrollment_log.warning(msg)
            print(common.formatString("WARNING",msg))
            send_tcp_msg(self.station_code,"ACK,"+self.message+";")
            return 
        
        # check if ennroll allowed 
        if (order_count:= db.st_order_db_func.get_enroll_count(self.station.code))>=abs(self.station.inner_pick-self.station.worker):
            msg=f'Enroll order is more than the station buffer, will ignore this message.'
            enrollment_log.warning(msg)
            print(common.formatString("WARNING",msg))
            return 

        response_code = None
        
        with app.test_request_context('/'):
            enrollment_log.info(self.message)
            if Config.SUPPORT_V2:
                response_code = http_client.SMHttpClient.inform_bin_enroll_v2(self.station_code,storage_code)
            else:
                response_code = http_client.SMHttpClient.inform_bin_enroll(self.station_code, storage_code)
        
        if response_code is not None:
            if response_code == 200:
                send_tcp_msg(self.station_code,"ACK,"+self.message+";")
            else:
                send_tcp_msg(self.station_code,common.STMsgFormatter.reject_enroll_msg(self.station_code,str(storage_code)))


    def handle_gw_request_responsd_msg(self):
        tc_job_id = int(self.splited_msg[3])
        req_info = self.splited_msg[4].split('|')
        position = int(req_info[0])
        action = req_info[1]
        req_bin = req_info[2]


        if action == ec.StationRequestType.DROP.value:
            self.station.latest_drop_msg[position] = ''
            self.station.record_gw_status(True,position,f'Station {"approve" if self.command == "A" else "decline"} to drop {req_bin}, waiting skycar to update HCC, req id : {tc_job_id}')
        if action == ec.StationRequestType.PICK.value:
            self.station.latest_pick_msg[position] = ''
            self.station.record_gw_status(False,position,f'Station {"approve" if self.command == "A" else "decline"} to pick {req_bin}, waiting skycar to update HCC, req id : {tc_job_id}')
        
        sio.CubeWSEmit.emit_staiton_request_event(self.station_code,tc_job_id,action,(self.command == 'A'), f"ST{self.station_code} reject {action} action.")
    
    def handle_gw_cancel_request_respond_msg(self):
        tc_job_id = int(self.splited_msg[3])
        request_info = self.splited_msg[4].split('|')
        request_index = int(request_info[0])
        request_action = request_info[1]
        request_bin = request_info[2]

        station = db.st_db_func.get_station_by_code(self.station_code)

        if request_action == ec.StationRequestType.DROP.value:
            station.latest_cancel_drop_msg[request_index] = ''
            station.record_gw_status(True,request_index,f'Station accept cancel request of dropping {request_bin}, req id : {tc_job_id}')

        if request_action == ec.StationRequestType.PICK.value:
            station.latest_cancel_pick_msg[request_index] = ''
            station.record_gw_status(False,request_index,f'Station accept cancel request of picking {request_bin}, req id : {tc_job_id}')

        if self.station.type == ec.CubeStationType.I.value:
            db.st_gw_req_db_func.update_gateway_req_by_job_id(tc_job_id,dict(canceled_ack = True))
        else:
            sio.CubeWSEmit.emit_staiton_request_event(self.station_code,tc_job_id,request_action,True,"",is_cancelled=True)

    def handle_invalid_msg(self):
        handler_log.error(f"{self.message} should not have entered this clause in message_handler_loop. Please double check.")

    @staticmethod
    def on_station_paired(station:adp.StationAdapter):
        
        tcp_server_log.info(f"-------- {station.name} Paired  ------",color='light_green')
        common.notify_wcs(f"ST{station.code} is paired.")
        db.st_db_func.update_station(station.code,dict(is_active = True))
        db.st_record_db_func.record_st_down_end(station.code,datetime.datetime.now())
        if station.code in rt.runtime.disconnect_threshold_list:
            common.BackgroundTask.st_reconnect_before_threshold(station.code)
    
    @staticmethod
    def on_station_unpaired(station:adp.StationAdapter,e_stop:bool = False):
        tcp_server_log.info(f"-------- {station.name} Unpaired  ------",color='light_green')
        common.notify_wcs(f'ST{station.code} unpaired from HCC')

        update_dict = dict(is_active = False)

        db.st_db_func.update_station(station.code,update_dict)
        db.st_record_db_func.add_record(station.code,ec.StationRecordType.DOWN.value,datetime.datetime.now())
        common.BackgroundTask.st_disconnect_threshold(station.code)
        common.saveNotification(
            title='Cube Station Connection',
            module=ec.ModuleCode.STATION.value,
            Device = f'{ec.ModuleCode.STATION.value}{station.code}',
            Message = f'Station {station.code} Unpaired'
            )

class ServiceDoorMessageHandler:

    def __init__(self,message:str,tcp_client:rt.TCPClient,is_validate:bool)->None:
        self.message = message
        self.splited_msg = message.split(',')
        self.tcp_client = tcp_client
        self.is_validate = is_validate

        header = self.splited_msg[0]

        if header == ec.CubesProtocol.ACK.value:
            self.sd_id = int(self.splited_msg[2])
            self.command = self.splited_msg[3]
            self.is_ack = True
        else:
            self.sd_id = int(self.splited_msg[1])
            self.command = self.splited_msg[2]
            self.is_ack = False
        
        self.sd = db.sd_db_function.get_sd_by_id(self.sd_id)
        
        if not self.sd:
            print(common.formatString('ERROR',colored(f"Received Message from unregistered SD{self.sd_id}: "))+f'{self.message}')
            raise Exception(f"No SD{self.sd_id} registered")
        
        print(common.formatString('INFO',colored(f"Received Message from SD{self.sd_id}: ",'light_cyan'))+f'{self.message}')
        service_door_log.info(f"Received Message from SD{self.sd_id}: {self.message}") 

    def process(self):
        
        # start process
        if self.is_ack:
            pass
            
        else:
            # can be handle before pairing 
            match self.command:
                case ec.CubesProtocol.P.value:
                    self.handle_pairing_msg()
                case ec.CubesProtocol.S.value:
                    self.handle_status_msg()
                case ec.CubesProtocol.OPEN.value:
                    self.handle_open_msg()
                case ec.CubesProtocol.CLOSE.value:
                    self.handle_close_msg()
                case _:
                    self.handle_invalid_msg()

    
    def handle_pairing_msg(self):
        if self.sd.is_active:
            if self.sd.connection == self.tcp_client.hash:
                send_tcp_msg(self.sd_id,common.SDMsgFormatter.pairing_reply_msg(self.sd_id),ec.ModuleCode.SERVCIE_DOOR.value)
            else:
                handler_log.error(f'SD{self.sd_id} connection in HCC is registered with the other sender.')
            return 

        if Config.RANDOM_STATION_ADDRESS:
            db.sd_db_function.sd_connecting(self.sd_id,self.tcp_client.hash)

        if not self.sd.is_connected:
            handler_log.error(f'SD{self.sd_id} is not connected but pairing')
            return 
        
        self.on_sd_paired(self.sd)

        send_tcp_msg(self.sd_id,common.SDMsgFormatter.pairing_reply_msg(self.sd_id),ec.ModuleCode.SERVCIE_DOOR.value)
        time.sleep(0.1)
        send_tcp_msg(self.sd_id,common.SDMsgFormatter.status_msg(self.sd_id),ec.ModuleCode.SERVCIE_DOOR.value)

    def handle_status_msg(self):
        sd_status = self.splited_msg[3]
        if sd_status == ec.ServiceDoorStatus.CLOSE.value:
            self.handle_close_msg()
        else:
            self.handle_open_msg()
            
    def handle_open_msg(self):
        handler_log.info(f'SD{self.sd_id} has opened',color='green')
        db.sd_db_function.update(self.sd_id,dict(status=ec.ServiceDoorStatus.OPEN.value))
        common.notifications.notify_service_door(self.sd_id,'OPENED')

        rt.runtime.opening_sd.pop(self.sd_id,None)

    def handle_close_msg(self):

        sd = db.sd_db_function.get_sd_by_id(self.sd_id)
        if sd.type == ec.ServiceDoorType.STATION.value:
            if self.sd_id in rt.runtime.opening_sd:
                handler_log.warning(f'Will not update SD{self.sd_id} to close since SD undergoing opening process.')
                return 
            # add flag station to maintenance off for sd station 
            db.sd_db_function.change_related_st_status(sd.id,False)

        handler_log.info(f'SD{self.sd_id} has closed',color='green')
        common.notifications.notify_service_door(self.sd_id,'CLOSED')
        db.sd_db_function.update(self.sd_id,dict(status=ec.ServiceDoorStatus.CLOSE.value))
        
        
        # id sd is undergoing opening process, ignore the close messsage

        return

    def handle_invalid_msg(self):
        handler_log.error(f"{self.message} should not have entered this clause in message_handler_loop. Please double check.")

    @staticmethod
    def on_sd_paired(sd:adp.RedisServiceDoor):
        tcp_server_log.info(f"-------- {sd.sdname} Paired  ------",color='light_green')
        db.sd_db_function.update(sd.id,dict(is_active = True))

    @staticmethod
    def on_sd_unpaired(sd:adp.RedisServiceDoor):
        tcp_server_log.info(f"-------- {sd.sdname} Unpaired  ------",color='light_green')
        db.sd_db_function.update(sd.id,dict(is_active = False))

class EMOMessageHandler:
    def __init__(self,message:str,tcp_client:rt.TCPClient,is_validate:bool)->None:
        self.message = message
        self.splited_msg = message.split(',')
        self.emo_id = int(self.splited_msg[1])
        self.tcp_client = tcp_client
        self.is_validate = is_validate

        header = self.splited_msg[0]
        self.command = self.splited_msg[2]        

        if self.command != ec.CubesProtocol.H.value:
            print(common.formatString('INFO',colored(f"Received Message from EMO{self.emo_id}: ",'light_cyan'))+f'{self.message}')
            emo_log.info(f"Received Message from EMO{self.emo_id}: {self.message}")

    def process(self):
        
        if self.tcp_client:
            if rt.runtime.runtime_emo[self.emo_id].connection and rt.runtime.runtime_emo[self.emo_id].connection != self.tcp_client.hash:
                from ..server.station_server import close_socket_connection

                tcp_server_log.warning(f'There is new incoming emo connection for {rt.runtime.runtime_emo[self.emo_id].name}, will close the old connection...')
                close_socket_connection(rt.runtime.runtime_tcp_client[rt.runtime.runtime_emo[self.emo_id].connection].conn)
                time.sleep(1)

            rt.runtime.runtime_emo[self.emo_id].connection = self.tcp_client.hash

        match self.command:
            case ec.CubesProtocol.H.value:
                heartbeat_log.info(self.message)
                send_tcp_msg(self.emo_id, common.EMOMsgFromatter.heartbeat_reply_msg(self.emo_id),target_module=ec.ModuleCode.EMO.value)
            case ec.CubesProtocol.ON.value:
                sio.CubeWSEmit.emit_emo_event(self.emo_id,True)
            case ec.CubesProtocol.OFF.value:
                sio.CubeWSEmit.emit_emo_event(self.emo_id,False)
            case _:
                self.handle_invalid_msg()

    def handle_invalid_msg(self):
        handler_log.error(f"{self.message} should not have entered this clause in message_handler_loop. Please double check.")


def message_handler_loop():
    """
    Mainly to handle the message pass from station_server
    Also the main part which handle the job done message logic
    """
    
    # handle thread
    current_thread().setName('message_handler_loop')

    while True:
        time.sleep(0.01)
        try:
            if(len(rt.runtime.msg_received_queue.queue) != 0):

                msg_handler = rt.runtime.msg_received_queue.get()
                
                if not msg_handler.is_validate:
                    if not validation.validate_msg(msg_handler.message):
                        continue

                msg_handler.process()     

                del msg_handler

                rt.runtime.msg_received_queue.task_done()
        except Exception as e:
            handler_log.error(f'message_handler_loop inner Error: {e}')

def message_broadcast_loop(): 

    try:
        '''Pull list of station movement jobs which are not yet completed.
           Check if predecessor job is complete, then check if job has been ack by station.
           If predecessor has been completed or no predescessor job, and station ack is false. Will generate movement protocol and send to station.'''
           
        # handle thread
        current_thread().setName('message_broadcast_loop')
        
        while True:
            try:
                time.sleep(1)
                with db.station_mov_lock:
                    station_jobs = db.st_mov_db_func.find_all_to_broadcast_active_jobs()
                    for job,station in station_jobs:
                        if db.st_mov_db_func.check_job_broadcastable(job,station):
                            full_msg = common.STMsgFormatter.get_full_msg(station.code,job)
                            send_tcp_msg(station.code,full_msg)
                            if job.status != ec.OrderStatus.PROCESSING.value:
                                db.st_mov_db_func.set_processing(job.id)
                            continue
                with sio.request_lock:          
                    station_gateway_reqs = db.st_gw_req_db_func.get_broadcastable_gateway_req()
                    for req in station_gateway_reqs:
                        station = db.st_db_func.get_station_by_code(req.station_code)
                        if req.status == ec.OrderStatus.READY.value:
                            msg_to_send = common.STMsgFormatter.gw_request_msg(req.station_code,req.tc_job_id,req.position,req.type,req.storage_code)
                            station.record_gw_status((req.type==ec.StationRequestType.DROP.value),req.position,f'TC request to {"drop" if req.type == ec.StationRequestType.DROP.value else "pick"} {req.storage_code}, pending station to response, req id : {req.tc_job_id}')
                        else:
                            msg_to_send = common.STMsgFormatter.gw_cancel_request_msg(req.station_code,req.tc_job_id,req.position,req.type,req.storage_code)
                            station.record_gw_status((req.type==ec.StationRequestType.DROP.value),req.position,f'TC cancel {"drop" if req.type == ec.StationRequestType.DROP.value else "pick"} request of  {req.storage_code}, pending on station ack, req id : {req.tc_job_id}')
                        send_tcp_msg(req.station_code,msg_to_send)
                
            except Exception as e:
                handler_log.error(f'message_broadcast_loop error. Exception thrown: {e}')


    except Exception as e:
        handler_log.error(f'message_broadcast_loop error. Exception thrown: {e}')

def event_queue_handler()->None:
    """
    Go through each station's event queue and check/run the event 

    If the event is critical error, will continue keep in the queue ( cause might need to recover )
    If the event is canceled, will go to the next event 
    If the station is in movement, will not run the current event
    If the event is not processed , will process the event ( this is where all the depending job done is add to the list)

    If the event is already processed, will check if the depending job is all done

    If all depending job is done
        - For REQ_DROP & REQ_PICK will only complete when request is also approved
        - For other event will completed once job pending completed

    """
    current_thread().setName('event_queue_handler')
    
    station_list = db.st_db_func.get_station_list_by_type(ec.CubeStationType.I.value)

    while True:
        try:
            for st in station_list:
                
                if st.event_queue.is_paused:
                    continue
                
                if not st.event_queue.get_size():
                    continue

                curr_event :adp.EventAdapter = st.event_queue.get_first_event()
                
                if curr_event.status == ec.OrderStatus.CRITICAL_ERROR.value:
                    continue
                
                # check if the corresponding request id deleted
                if curr_event.request_id:
                    if curr_event.event_name in [ec.CubesIStationEvent.REQ_DROP.value,ec.CubesIStationEvent.REQ_PICK.value,ec.CubesIStationEvent.DROP_DONE.value,ec.CubesIStationEvent.PICK_DONE.value]:
                        req = db.st_gw_req_db_func.get_gateway_req_by_id(curr_event.request_id)
                        # when request is canceled 
                        if req.status == ec.OrderStatus.CANCELED.value:
                            curr_event.cancel_event()

                # when event is canceled
                if curr_event.status == ec.OrderStatus.CANCELED.value:
                    st.event_queue.next_event()
                    if curr_event.event_name in [ec.CubesIStationEvent.REQ_DROP.value,ec.CubesIStationEvent.REQ_PICK.value]:
                        new_curr_event = st.event_queue.get_first_event()
                        new_curr_event.cancel_event()
                        st.event_queue.next_event()
                    if curr_event.event_name in [ec.CubesIStationEvent.DROP_DONE.value,ec.CubesIStationEvent.PICK_DONE.value]:
                        db.st_db_func.update_station(curr_event.station_code,dict(gw_operating = False))

                    continue
            
                # check if the station is moving or d-ing/p-ing bin 
                if db.st_mov_db_func.station_is_processing(st.code):
                    continue

                # run the event if not processed
                if not curr_event.is_processed:
                    curr_event.process()
                    continue
                
                # check pending job complete or not 
                job_completed = True
                for job_id in curr_event.job_to_complete_event:
                    if not job_id:
                        curr_event.trigger_error("Invalid job_id")
                    if not db.st_mov_db_func.check_job_completed(job_id):
                        job_completed = False
                        break
                
                if not job_completed:
                    continue
                
                # REQ_DROP & REQ_PICK event only completed when the request is approved 
                if curr_event.event_name in [ec.CubesIStationEvent.REQ_DROP.value,ec.CubesIStationEvent.REQ_PICK.value]:
                    req = db.st_gw_req_db_func.get_gateway_req_by_id(curr_event.request_id)
                    if req.status == ec.OrderStatus.AVAILABLE.value:
                        db.st_gw_req_db_func.update_gateway_req_status(curr_event.request_id,ec.OrderStatus.READY.value)
                    elif req.status == ec.OrderStatus.COMPLETED.value:
                        curr_event.complete_event()
                        st.event_queue.next_event()
                        db.st_db_func.update_station(curr_event.station_code,dict(gw_operating = True))
                        continue
                
                # Other event complete when the pending job is completed
                else:
                    curr_event.complete_event()
                    st.event_queue.next_event()
            time.sleep(0.1)
        except Exception as e:
            handler_log.error(f'{event_queue_handler.__qualname__}() raise Exception: {e}')

async def disconnect_threshold(station_code:int):
    """
    A coroutine to keep the disconnected station and holding the emit for the threshold time 
    """
    try:
        from asyncio import sleep 
        await(sleep(0.01))
        await sleep(Config.ST_DOWN_THRESHOLD)
        rt.runtime.disconnect_threshold_list.pop(station_code,None)
        sio.CubeWSEmit.emit_active_station()
        http_client.HttpClient.update_st_status(station_code)  
          
    except Exception as e:
        handler_log.error(f'broadcast_with_threshold_loop error. Exception thrown: {e}')

async def retry_handler(station_code:int,msg:str):
    """
    A coroutine to keep sending message to station untill ack
    """
    try: 
        from asyncio import sleep
        while True:
            send_tcp_msg(station_code,msg)
            await sleep(2)

    except Exception as e:
        handler_log.error(f"retry_handler throw exception: {e}")


async def station_weight_handler(station_code:int,storage_code:str):
    """
    A coroutine to keep sending weight command to station to get the weight of the storage 
    """
    try:  # Handle polling the station for weight every set interval
        from asyncio import sleep
        while True:
            send_tcp_msg(station_code,common.STMsgFormatter.weight_msg(station_code,storage_code))
            await sleep(1)

    except Exception as e:
        handler_log.error(f"station_weight_handler throw exception: {e}")
        
async def station_enroll_handler(station_code:int):
    """
    A coroutine to keep sending enroll command to tirgger the enrollment mode of the station
    """
    try:
        from asyncio import sleep
        while True:
            send_tcp_msg(station_code,common.STMsgFormatter.enroll_msg(station_code))
            await sleep(4)

    except Exception as e:
        handler_log.error(f"station_enroll_handler throw exception: {e}")

async def service_door_handler(sd_id:int):
    """
    A coroutine to make sure when all the station stop processing job, then will continue request door to open 
    Args:
        st_list (List[int]): list to set to maintenance
    """

    from asyncio import sleep

    handler_log.warning(f'Waiting all station to stop movement...... ')
    await sleep(8)
    handler_log.info(f'Done setting all station to maintennace, will request to open door now.',color='light_green')
    common.notifications.notify_service_door(sd_id,'DONE_PROCESSING')
    send_tcp_msg(sd_id,common.SDMsgFormatter.open_door_msg(sd_id),target_module=ec.ModuleCode.SERVCIE_DOOR.value)

    return True

def send_tcp_msg(plc_id:int,msg:str,target_module:str=ec.ModuleCode.STATION.value)->bool:
    try:      
        if target_module == ec.ModuleCode.STATION.value:
            return send_station_msg(plc_id,msg)
        elif target_module == ec.ModuleCode.SERVCIE_DOOR.value:
            return send_service_door_msg(plc_id,msg)
        elif target_module == ec.ModuleCode.EMO.value:
            return send_emo_msg(plc_id,msg)
        else:
            tcp_server_log.error(f'Invalid module code {target_module}, not able to send messgae {msg}')
            return False
        
    except Exception as e:
        tcp_server_log.error(f'send_tcp_msg error for msg: {msg}. Exception thrown:{e}, failed to send {msg}')
        return False

def send_station_msg(station_code:int,msg:str):
    if Config.MOCK_STATION_MSG:
        from ..mock import MockStation
        MockStation.add_message(msg)

        return 
    
    splited_msg = msg.split(',')
    target_station = db.st_db_func.get_station_by_code(station_code)

    if not target_station.is_connected:
        tcp_server_log.error(f"ST{station_code} is not connected and paired yet, failed to send {msg}")
        return False
    
    if target_station.is_maintenance and splited_msg[2] not in [ec.CubesProtocol.H.value,
                                                                ec.CubesProtocol.P.value, 
                                                                ec.CubesProtocol.U.value,
                                                                ec.CubesProtocol.CR.value]:
        
        tcp_server_log.error(f"ST{station_code} is under maintenance, will restrict send {msg}")
        return False
    
    target_tcp_client = rt.runtime.runtime_tcp_client.get(target_station.connection,None)
    
    if target_tcp_client == None:
        tcp_server_log.error(f'ST{station_code} is connected but could not find TCP client, pleae check HCC module, failed to send {msg}')
        return False

    target_tcp_client.conn.send(msg.encode())
    if splited_msg[2] == ec.CubesProtocol.H.value:
        heartbeat_log.info(msg)
    else:
        if not target_station.is_active:
            target_station.log.msg_log(station_code,f"Sent Message to unpaired ST{station_code}: {msg}")
            print(common.formatString('INFO',colored(f"Sent Message to unpaired ST{station_code}: ",'light_yellow')+msg))
        else:
            target_station.log.msg_log(station_code,f"Sent Message to ST{station_code}: {msg}")
            if "SUCCESS" in msg:
                print(common.formatString('INFO',colored(f"Sent Message to ST{station_code}: ",'light_blue')+common.messageColor(f'{msg}')))
            else:
                print(common.formatString('INFO',colored(f"Sent Message to ST{station_code}: ",'light_blue')+msg))
    return True

def send_service_door_msg(sd_id:int,msg:str):
    target_sd = db.sd_db_function.get_sd_by_id(sd_id)

    if not target_sd.is_connected:
        tcp_server_log.error(f"SD{sd_id} is not connected and paired yet, failed to send {msg}")
        return False
    
    target_tcp_client = rt.runtime.runtime_tcp_client.get(target_sd.connection,None)
    
    if target_tcp_client == None:
        tcp_server_log.error(f'SD{sd_id} is connected but could not find TCP client, pleae check HCC module, failed to send {msg}')
        return False

    target_tcp_client.conn.send(msg.encode())
    if target_sd.is_active:
        service_door_log.info(f"Sent Message to SD{sd_id}: {msg}")
        if "SUCCESS" in msg:
            print(common.formatString('INFO',colored(f"Sent Message to SD{sd_id}: ",'light_blue')+common.messageColor(f'{msg}')))
        else:
            print(common.formatString('INFO',colored(f"Sent Message to SD{sd_id}: ",'light_blue')+msg))
    return True

def send_emo_msg(emo_id:int,msg:str):
    splited_msg = msg.split(',')

    emo_client = rt.runtime.runtime_emo[emo_id]

    if not emo_client.connection:
        tcp_server_log.error(f'No connection for EMO{emo_id}, not able to send message {msg}')
        return False
    
    target_tcp_client = rt.runtime.runtime_tcp_client.get(emo_client.connection,None)

    if not target_tcp_client:
        tcp_server_log.error(f'EMO{emo_id} connection is connected but could not find TCP client, pleae check HCC module, failed to send {msg}')
        return False
    
    target_tcp_client.conn.send(msg.encode())
    
    if splited_msg[1] == ec.CubesProtocol.H.value:
        heartbeat_log.info(msg)
    else:
        emo_log.info(f"Sent Message to EMO{emo_id}: {msg}")
        print(common.formatString('INFO',colored(f"Sent Message to EMO{emo_id}: ",'light_blue')+msg))

    return True

# helper function 
def error_log_and_print(logfile,station_code:int,err_msg:str,description:str):
    full_msg = description + f" : {err_msg}"
    logfile.error(full_msg)
    print(common.formatString("ERROR",full_msg))

    if not station_code in msg_dict or (msg_dict[station_code] + datetime.timedelta(minutes=5) < datetime.datetime.now()):
        msg_dict[station_code] = datetime.datetime.now()
        common.saveNotification(title = f"Cube Station Response Error",
                                module=ec.ModuleCode.STATION.value,
                                Device = f'{ec.ModuleCode.STATION.value}{station_code}',
                                Error_Msg = err_msg,
                                Error_Description = description
                                )
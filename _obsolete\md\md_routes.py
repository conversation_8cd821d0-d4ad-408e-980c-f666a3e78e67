# from flask import request,Blueprint
# from simple_chalk import redBright, blue

# import app.common as common
# import app.database as db

# md = Blueprint('md', __name__, url_prefix='/md')

# md_route = common.get_logger_console('md_route')

# @md.route('sensor', methods=['DELETE'])
# def reset_sensor():
#     try:
#         data = request.get_json()
#         md_route.info(blue(f'Received - ')+f'{data}')
#         db.clear_md_station_processing(data['station_id'])
#         db.complete_md_job(data['job_id'])
#         json = {
#             "status": "SUCCESS"
#         }
#         return json
#     except Exception as e:
#         md_route.error(redBright(f'reset_sensor error. Exception thrown: {e}'))
import time
import asyncio 

from termcolor import colored
from threading import current_thread
from asyncio import Abstract<PERSON><PERSON><PERSON><PERSON>
from typing import Op<PERSON>, Coroutine, ClassVar, Dict, List

from config import Config
from .. import common as common,enum_collections as ec

class StMsgQueue(asyncio.Queue):
        
    def __init__(self):
        super().__init__()
        
    def put(self, msg:str):
        self._loop.call_soon_threadsafe(
            super().put_nowait,msg
        )
        
    def get(self)->str:
        return super().get()
    
class MockStation:
    is_active = Config.MOCK_STATION_MSG
    station_msg_queue = StMsgQueue()
    station_bin_queue:Dict[str,List[str]] = dict()
    hash: ClassVar[Dict[int, asyncio.Future]] = dict()

    TIME_FOR_1_INDEX = 1.5 #seconds

    @classmethod
    def start(cls):
        time.sleep(0.1)
        from .. import database as db
        from ..communications_provider.socketio.cube import CubeWSEmit

        cls.set_up_station_bin_queue()
        cls.reset_all_broadcasting_job()
        db.st_db_func.set_active_stations()
        CubeWSEmit.emit_active_station()
        cls.event_loop = cls.get_or_create_eventloop()
        cls.event_loop.create_task(cls.st_msg_handler_loop(),name= 'statation_msg_handler_loop')
        cls.event_loop.run_forever()
        


    @classmethod
    async def st_msg_handler_loop(cls):
        while True:
            msg = await cls.station_msg_queue.get()
            await cls.process_msg(msg)

    @classmethod
    async def process_msg(cls,msg:str):

        msg = msg.replace(';','')
        splited_msg = msg.split(',')
        header = splited_msg[0]
        if header == ec.CubesProtocol.ACK.value:
            station_code = int(splited_msg[2])
            command = splited_msg[3]
            is_ack = True
        else:
            station_code = int(splited_msg[1])
            command = splited_msg[2]
            is_ack = False

        print(common.formatString('INFO',colored(f"MOCK STATION Receive: ",'yellow'))+f'{msg}')

        if is_ack:
            match command:
                case ec.CubesProtocol.J.value:
                    job_id = int(splited_msg[4])
                    try:
                        cls.hash[job_id].set_result(True)
                    except Exception as e:
                        print(e)

        else:
            match command:
                case ec.CubesProtocol.R.value:
                    msg = msg.replace('R','A')
                    cls.reply_message(msg)
                case ec.CubesProtocol.CR.value:
                    msg = msg.replace('CR','AC')
                    cls.reply_message(msg)
                case ec.CubesProtocol.U.value:
                    msg = 'ACK,'+msg
                    update_info = splited_msg[4].split('|')
                    update_index = int(update_info[0])
                    update_bin = update_info[2]

                    cls.reply_message(msg)
                    st_from_index_format = f'{station_code}.{update_index}'
                    cls.remove_from_st_bin_queue(st_from_index_format,update_bin)
                case ec.CubesProtocol.M.value:
                    cls.reply_message('ACK,'+msg)

                    job_id = int(splited_msg[3])
                    job_info = splited_msg[4].split('|')
                    
                    from_index = int(job_info[0])
                    to_index = int(job_info[1] )
                    bin_no = job_info[3].lstrip("0")

                    st_to_index_format = f'{station_code}.{to_index}'
                    st_from_index_format = f'{station_code}.{from_index}'
                    cls.remove_from_st_bin_queue(st_from_index_format,bin_no)
                    cls.add_to_st_bin_queue(st_to_index_format,bin_no)

                    msg = msg.replace('M','J')
                    wait_time = abs(to_index-from_index)*cls.TIME_FOR_1_INDEX
                    cls.reply_message_with_threshold(msg,job_id,st_to_index_format,bin_no,wait_time)
                case ec.CubesProtocol.L.value:
                    msg = 'ACK,'+msg
                    cls.reply_message(msg)
                case ec.CubesProtocol.REMOVE.value:
                    """ST,7,REMOVE,5001"""
                    bin_no = splited_msg[3].lstrip("0")
                    msg = 'ACK,'+msg
                    cls.reply_message(msg)
                    cls.chargeout_from_st_bin_queue(bin_no)


    @classmethod
    def add_message(cls,msg:str):
        cls.station_msg_queue.put(msg)

    @classmethod
    def reply_message(cls,msg:str):
        from ..service_handler.cube_handler import MessageHandler
        from ..runtime import runtime
        # print(common.formatString('INFO',colored(f"MOCK STATION Send: ",'yellow'))+f'{msg}')
        runtime.msg_received_queue.put(MessageHandler.create_instance(msg,None,True))

    @classmethod
    def reply_message_with_threshold(cls,msg:str,job_id:int,st_index_format:str,bin_no:str,seconds:int):
        cls.create_task(cls.reply_message_with_threshold_inner(msg,job_id,st_index_format,bin_no,seconds))

    @classmethod
    async def reply_message_with_threshold_inner(cls,msg:str,job_id:int,st_index_format:str,bin_no:str,seconds:int):
            # simulate the moving time of staiton 
            await(asyncio.sleep(seconds))
            while len(cls.station_bin_queue[st_index_format]) == 0 or cls.station_bin_queue[st_index_format][0] != bin_no:
                await (asyncio.sleep(1))
            cls.add_retry(msg,job_id,seconds)
            cls.reply_message(msg)


    @classmethod
    def add_retry(cls,msg:str,id:int,wait:int=None):
        future = cls.event_loop.create_future()
        cls.hash[id] = future
        cls.create_task(cls.retry_message(future,msg,wait))

    @classmethod
    async def retry_message(cls,future,msg,wait:int=None):
        if wait:
            await(asyncio.sleep(wait))
        await (asyncio.sleep(3))
        while not future.done():
            cls.reply_message(msg)
            await asyncio.sleep(1)

    @classmethod
    def add_to_st_bin_queue(cls,st_index_str:str,bin_no:str):
        if st_index_str not in cls.station_bin_queue:
            cls.station_bin_queue[st_index_str] = []
        if not bin_no in cls.station_bin_queue[st_index_str]:
            cls.station_bin_queue[st_index_str].append(bin_no)
    
    @classmethod
    def remove_from_st_bin_queue(cls,st_index_str:str,bin_no:str):
        if st_index_str in cls.station_bin_queue:
            if bin_no in cls.station_bin_queue[st_index_str]:
                cls.station_bin_queue[st_index_str].remove(bin_no)

    @classmethod
    def chargeout_from_st_bin_queue(cls,bin_no:str):
        for queue in cls.station_bin_queue.values():
            if bin_no in queue:
                queue.remove(bin_no)
                
    @classmethod
    def get_or_create_eventloop(cls)->Optional[AbstractEventLoop]:
        try:
            return asyncio.get_event_loop()
        except RuntimeError as ex:
            if "There is no current event loop in thread" in str(ex):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                return asyncio.get_event_loop() 
    
    @classmethod
    def create_task(cls, coro: Coroutine, name: str = None):
        """Create a task which will be executed in event loop.
        """

        def _create_task():
            return cls.event_loop.create_task(coro,
                name = name or coro.__qualname__
            )

        def _async_create_task():
            task = _create_task()
            future.set_result(task)

        if cls.is_current_loop():
            return _create_task()            

        future = cls.create_future()
        cls.event_loop.call_soon_threadsafe(
            _async_create_task
        )
        return future
    
    @classmethod
    def is_current_loop(cls):
        return cls.event_loop._thread_id == current_thread().ident
    
    @classmethod
    def set_up_station_bin_queue(cls):
        from ..runtime import runtime
        from .. import database as db

        st_list = runtime.get_runtime_st_list()
        for st in st_list:
            location_str = db.st_mov_db_func.get_bin_queue_indexing(st.code)
            location_str_list = location_str.split('|')
            if st.type == ec.CubeStationType.I.value:
                for index,bin in enumerate(location_str_list):
                    if bin != '':
                        cls.add_to_st_bin_queue(f'{st.code}.{index}',bin)
            elif st.type == ec.CubeStationType.REGULAR.value:
                for index,bin in enumerate(location_str_list):
                    if bin != '' and index in [st.inner_pick,st.worker]:
                        cls.add_to_st_bin_queue(f'{st.code}.{index}',bin)
            elif st.type == ec.CubeStationType.LARGE.value:
                for index,bin in enumerate(location_str_list):
                    if bin != '' and index in [st.outer_pick,st.worker]:
                        cls.add_to_st_bin_queue(f'{st.code}.{index}',bin)
            else:
                pass
        
    @classmethod
    def reset_all_broadcasting_job(cls):
        from .. import database as db

        # resend all the job of this station to plc 
        station_jobs = db.st_mov_db_func.find_all_active_broadcast_jobs_join_st() 
        for job,station in station_jobs:
            if db.st_mov_db_func.check_job_broadcastable(job,station):
                db.st_mov_db_func.reset_job(job.id)

from __future__ import annotations


import json

from datetime import datetime
from typing import List, TYPE_CHECKING,Tuple

from ...runtime import runtime
from ... import common as common,models as model,enum_collections as ec

from .station_dal import StationSQLAlchemyQueries

if TYPE_CHECKING:
    from ...adapter import StationAdapter, EventQueue

db_func_log = common.LogManager('cube_db_error',display_console=True)
@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_func_log))
class StationDBFunc:
    
    station_dal = StationSQLAlchemyQueries()
    
    def bulk_seed_station(self,station_list:List[Tuple[int,int]])->List[model.Stations]:
        return self.station_dal.bulk_seed_station(station_list)

    def get_all_station_list_db(self)->List[model.Stations]:
        return self.station_dal.get_all_station_list()
    
    def get_all_station_list(self)->List[StationAdapter]:
        return runtime.get_runtime_st_list()
    
    def get_station_list_by_type(self,type: ec.CubeStationType)->List[StationAdapter]:
        st_list = self.get_all_station_list()

        st_list = [st for st in st_list if st.type == type] 

        return st_list

    def get_active_station_list(self,zone="")->List[StationAdapter]:
        station_list = self.get_all_station_list()
        station_list = [st for st in station_list if st.is_active and not st.is_maintenance]
        if zone != "":
            station_list = [st for st in station_list if st.zone == zone]
        return station_list
            
    def get_active_station_code_list(self,zone = "")->List[int]:
        station_list = self.get_active_station_list(zone)
        station_code_list = [st.code for st in station_list]
        return station_code_list
    
    def get_station_by_code(self,code:int)->StationAdapter:

        return runtime.runtime_st.get(code,None)
    
    def get_station_list_by_matrix_code(self,matrix_code:int)->List[StationAdapter]:

        return  [station for _,station in runtime.runtime_st.items() if station.matrix_code == matrix_code]

    def get_station_by_host_and_port(self,host:str,port:int)->StationAdapter:

        for _,station in runtime.runtime_st.items():
            if station.host == host and station.port == port:
            # if station.host == host:
                return station

        # db_func_log.error(f"No station with host {host} and port {port} found in rt")
        return None
    
    def get_station_by_connection(self,connect_hash:int)->StationAdapter:
        
        for _,station in runtime.runtime_st.items():
            if station.connection == connect_hash:
                return station
        return None

    def get_station_queue(self,station_code:int)->List[str]:
        
        station = self.get_station_by_code(station_code)

        return station.queue
    
    def get_station_event_queue(self,station_code:int)->EventQueue:

        station = self.get_station_by_code(station_code)
        
        return station.event_queue

    def get_pick_index_by_cube(self,station_code:int,cube:str)->int:

        station = self.get_station_by_code(station_code)

        to_index = None

        if station.type == ec.CubeStationType.LARGE.value:
            if cube == ec.Cubes.B.value:
                to_index = station.inner_pick
            elif cube == ec.Cubes.C.value:
                to_index = station.outer_pick
            else:
                db_func_log.error(f"Invalid cube for ST{station_code}")
        elif station.type == ec.CubeStationType.QC.value:
            if cube == ec.Cubes.A.value:
                to_index = station.outer_pick
            elif cube == ec.Cubes.C.value:
                to_index = station.inner_pick
            else:
                db_func_log.error(f"Invalid cube for ST{station_code}")
        else:
            to_index = station.inner_pick
        return to_index
    
    def check_station_light_up_valid(self,matrix_code:int)->bool:
        station_list = self.get_station_list_by_matrix_code(matrix_code)
        for st in station_list:
            if st.bin_at_worker:
                return False
        return True    
        
    def update_station(self,station_code:int,update:dict):
        station = runtime.runtime_st[station_code]
        for attr,value in update.items():
            setattr(station,attr,value)
        if 'queue' in update:
            bin_list = update.get('queue')
            str_bin_list = json.dumps(bin_list)
            update.update({"queue":str_bin_list})
        update.pop('connection',None)
        self.station_dal.update_station(station_code,update)

    def update_list_of_st_maint_status(self,st_list:List[int],maint_status:bool):

        for st_code in st_list:
            setattr(runtime.runtime_st[st_code],'is_maintenance',maint_status)
        self.station_dal.update_list_of_station(st_list,maint_status)

    def update_station_after_job_done(self,job:model.StationMovement,station_code:int,bin_no:str):

        from . import st_order_db_func

        station = self.get_station_by_code(station_code)
        order = st_order_db_func.find_order(dict(id=job.order_id))
        if job.type in [ec.CubeStationJobType.MOVE_TO_WORK.value,ec.CubeStationJobType.BRIDGE.value]:  # If it is arriving at workpoint
            if station.type != ec.CubeStationType.I.value:
                if order.type != ec.OrderType.BRIDGE.value:
                    self.update_station(station_code,dict(bin_at_worker = bin_no))
            else:
                can_light_up = self.check_station_light_up_valid(station.matrix_code)
                if can_light_up or station.mode == ec.CubeStationMode.TRANSFER.value:
                    self.update_station(station_code,dict(bin_at_worker = bin_no))
        if job.type == ec.CubeStationJobType.NEXT_BIN.value:
            if station.type != ec.CubeStationType.I.value:
                self.update_station(station_code,dict(bin_at_worker = None))
                
        if job.type == ec.CubeStationJobType.LIGHT_DOWN.value:
            self.update_station(station_code,dict(bin_at_worker = None))


    def update_station_queue(self,station_code:int,bin_no:str,to_index:int =None):
        st_queue = self.get_station_queue(station_code)
        station = self.get_station_by_code(station_code)

        if station.type == ec.CubeStationType.I.value:
            if to_index is None:
                db_func_log.error("Not able to update station queue cause invalid to index for I station type.")
                return 
            st_queue[to_index] = bin_no
            self.update_station(station_code,dict(queue = st_queue))
        else:
            if bin_no not in st_queue:
                st_queue.append(bin_no)
                self.update_station(station_code,dict(queue = st_queue))

    def update_station_queue_after_job_done(self,job:model.StationMovement,station_code:int,bin_no:str):

        st_queue = self.get_station_queue(station_code)
        station = self.get_station_by_code(station_code)

        if station.type == ec.CubeStationType.I.value:
            if st_queue[job.from_index] == bin_no:
                st_queue[job.from_index] = ""
            st_queue[job.to_index] = bin_no
            self.update_station(station_code,dict(queue = st_queue))
        if (station.type == ec.CubeStationType.LARGE.value and job.type in [ec.CubeStationJobType.MOVE_TO_WORK.value,ec.CubeStationJobType.BRIDGE.value]) or\
             (station.type == ec.CubeStationType.QC.value and job.type == ec.CubeStationJobType.MOVE_TO_JUNCTION.value):
            if bin_no not in st_queue:
                st_queue.append(bin_no)
                self.update_station(station_code,dict(queue = st_queue))
        if job.type == ec.CubeStationJobType.BRIDGE.value:
            self.remove_from_station_queue(station.adjacent,bin_no)
                
    
    def add_bin_to_st_queue(self,station_code:int,bin:str):
       
        st_queue = self.get_station_queue(station_code)
        if bin not in st_queue:
            st_queue.append(bin)
            self.update_station(station_code,dict(queue = st_queue))

    def update_st_queue_after_picking(self,station_code:int,bin:str):
        
        station = self.get_station_by_code(station_code)        
        st_queue = self.get_station_queue(station_code)

        if station.type == ec.CubeStationType.I.value:
            st_queue[station.inner_pick] = ""
            self.update_station(station.code,dict(queue = st_queue))
        else:
            if bin in st_queue:
                st_queue.remove(bin)
                self.update_station(station_code,dict(queue=st_queue))

    def patch_bin_in_st_queue(self,station_code:int,old_bin_no:str,new_bin_no:str):
        st_queue = self.get_station_queue(station_code)

        for index,bin in enumerate(st_queue):
            if bin == old_bin_no:
                st_queue[index] = new_bin_no
                self.update_station(station_code,dict(queue=st_queue))
                return
           
    def remove_from_station_queue(self,station_code:int,storage_code:str):
        st_queue = self.get_station_queue(station_code)
        station = self.get_station_by_code(station_code)

        if station.type == ec.CubeStationType.I.value:
            if storage_code in st_queue:
                index = st_queue.index(storage_code)
                st_queue[index] = ""
            self.update_station(station_code,dict(queue = st_queue))
        else:
            if storage_code in st_queue:
                st_queue.remove(storage_code)
                self.update_station(station_code,dict(queue = st_queue))

    def station_connecting(self,station_code:int,conn:int,host:str,port:int):
        from config import Config
        station = self.get_station_by_code(station_code)
        
        update_dict = dict(is_connected = True)
        setattr(station,"connection",conn)
        setattr(station,"is_connected",True)
        setattr(station,"last_ping_time",datetime.now())
        if Config.RANDOM_STATION_ADDRESS:
            setattr(station,"host",host)
            setattr(station,"port",port)
            update_dict['host'] = host
            update_dict['port'] = port
        self.station_dal.update_station(station_code,update_dict)

    def station_disconnecting(self,station_code:int):
        from config import Config
        station = self.get_station_by_code(station_code)
        
        setattr(station,"connection",None)
        setattr(station,"is_connected",False)
        setattr(station,"last_ping_time",None)

        if Config.RANDOM_STATION_ADDRESS:
            setattr(station,"host",None)
            setattr(station,"port",None)

        self.station_dal.update_station(station_code,dict(is_connected = False,host=None,port=None))
           
    def reset_active_stations(self):
        for st in runtime.get_runtime_st_list():
            setattr(st,'is_active',False)
            setattr(st,'is_connected',False)
        self.station_dal.reset_active_stations()
        
    def set_active_stations(self):
        for st in runtime.get_runtime_st_list():
            setattr(st,'is_active',True)
            setattr(st,'is_connected',True)
        self.station_dal.set_active_stations()
           
    def data_retrieve(self,zone, date)->dict:
        
        from . import st_mov_db_func
        
        def format_time(time):
            local_time = time.strftime('%Y-%m-%d %H:%M:%S')
            return local_time

        json_data = dict()
        station_list = self.get_active_station_list(zone=zone)

        for station in station_list:
            json_data[station.code] = []
            type = station.type
            if type != ec.CubeStationType.BRIDGE.value and type != ec.CubeStationType.QC.value and station.code != 11:
                bin_history = st_mov_db_func.retrieve_station_job_data(station.id,date)
                for bin_job in bin_history:
                    next_bin_job = st_mov_db_func.find_succ_job(bin_job.id)
                    if type == ec.CubeStationType.REGULAR.value:
                        move_pick_job = st_mov_db_func.find_succ_job(next_bin_job.id)
                    else:
                        pre_move_job = st_mov_db_func.find_succ_job(next_bin_job.id)
                        move_pick_job = st_mov_db_func.find_succ_job(pre_move_job.id)
                    job_stats={
                        'storage_code': bin_job.bin_no,
                        'drop_to_work': {
                            'start': format_time(bin_job.created_at),
                            'end': format_time(bin_job.updated_at)
                        },
                        'worker_processing': {
                            'start': format_time(bin_job.updated_at),
                            'end': format_time(next_bin_job.updated_at)
                        },
                        'work_to_pick':{
                            'start': format_time(next_bin_job.updated_at),
                            'end': format_time(move_pick_job.updated_at)
                        }
                    }
                    json_data[station.code].append(job_stats)

        return json_data

    def get_station_detail(self,zone: ec.Cubes == None,code :int = None) -> List[dict]:
        
        from . import st_error_db_func,st_order_db_func
        
        runtime_data = list()
        station_list = self.get_all_station_list()
        if zone:
            station_list = [st for st in station_list if st.zone == zone]
        if code:
            station_list = [st for st in station_list if st.code == code]
        for station in station_list:
            pre_post_num_of_bin = st_order_db_func.get_bin_in_station_pre_post_processed(station.code)
            runtime_data.append(dict(
                station_id = station.code,
                matrix_code = station.matrix_code,
                type = station.type,
                cell = station.cell,
                mode = station.mode,
                zone = station.zone,
                adjacent = station.adjacent,
                is_connected = station.is_connected,
                is_active = station.is_active,
                is_maintenance = station.is_maintenance,
                is_enroll = True if station.mode == ec.CubeStationMode.ENROLL.value else False,
                is_overweight = station.is_overweight,
                is_recovery = station.is_recovery,
                bin_at_worker = station.bin_at_worker,
                storage_quantity = pre_post_num_of_bin,
                error_code = st_error_db_func.get_station_error_code(station.code),
            ))
        return runtime_data
    
    def get_station_detail_old(self,zone: ec.Cubes == None,code :int = None) -> List[dict]:
        
        from . import st_error_db_func,st_order_db_func
        
        runtime_data = list()
        station_list = self.get_all_station_list()
        if zone:
            station_list = [st for st in station_list if st.zone == zone]
        if code:
            station_list = [st for st in station_list if st.code == code]
        for station in station_list:
            station: model.Stations
            count =  st_order_db_func.get_num_of_bin_in_station(station.code)
            runtime_data.append(dict(
                station_id = station.code,
                type = station.type,
                zone = station.zone,
                is_active = station.is_connected,
                is_enroll = True if station.mode == ec.CubeStationMode.ENROLL.value else False,
                storage_quantity = count,
                error_code = st_error_db_func.get_station_error_code(station.code),
                is_maint = not station.is_active
            ))
        return runtime_data
    
    
    # def get_cube_by_pick_index(self,station_code:int,pick_index:int)->str:
        
    #     station = self.get_station_by_code(station_code)

    #     cube  = ""
        
    #     if station.type == ec.CubeStationType.LARGE.value:
    #         if pick_index == station.inner_pick:
    #             cube = ec.Cubes.B.value
    #         elif pick_index == station.outer_pick:
    #             cube = ec.Cubes.C.value
    #     elif station.type == ec.CubeStationType.QC.value:
    #         if pick_index == station.inner_pick:
    #             cube = ec.Cubes.C.value
    #         elif pick_index == station.outer_pick:
    #             cube = ec.Cubes.A.value
    #     else:
    #         cube = station.zone
        
    #     return cube
    
    # def create_station(self,code,zone,type,rotation,adjacent, inner_drop,inner_pick, outer_drop,outer_pick,worker):
    #      return self.station_dal.create_station(code,zone,type,rotation,adjacent, inner_drop,inner_pick, outer_drop,outer_pick,worker)

    # def get_active_station_list(self,zone= ""):
    #     if zone == "": 
    #         station_list = self.station_dal.get_active_station_list()
    #     else:   
    #         station_list = self.station_dal.get_active_station_list_by_zone(zone)
    #     return station_list
        
    # def get_active_station_code_list(self,zone = ""):
    #     stations = []
    #     station_list = self.get_active_station_list(zone)
    #     for s in station_list:
    #         stations.append(s.code)
    #     return stations

    # def get_active_station_code_list_include_threshold(self):
    #     active_station_code = list()
    #     active_stations = self.station_dal.get_active_station_list_include_threshold()
    #     for s in active_stations:
    #         active_station_code.append(s.code)   
    #     return active_station_code
    
    # def get_station_by_code(self,code:int):
    #     return self.station_dal.get_station_by_code(code)
        
    # def get_station_by_id(self,station_id):
    #     return self.station_dal.get_station_by_id(station_id)
        
    # def wcs_update_station_details(self,code,zone,type,rotation,adjacent,inner_drop,inner_pick,outer_drop,outer_pick,worker, is_deleted):
    #     self.station_dal.wcs_update_station_details(code,zone,type,rotation,adjacent,inner_drop,inner_pick,outer_drop,outer_pick,worker, is_deleted)

    # def update_active_stations(self,code:int , isRemove:bool):
    #     self.station_dal.update_station(code,{"is_active": not isRemove})

    # def update_maintenance_station(self,code:int, is_maint:bool):
    #     self.station_dal.update_station(code,{"is_maintenance":is_maint})

    # def update_station_queue(self,code:int,queue:List[str]):
    #     str_queue = json.dumps(queue)
    #     self.station_dal.update_station(code,dict(queue= str_queue))

    # def update_gw_operating_status(self,code:int,is_operating:bool):
    #     self.station_dal.update_station(code,dict(gw_operating= is_operating))

    
    
    
   
    
    # def get_station_queue(self,station_code:int)->List[str]:
        
    #     station:model.Stations = self.station_dal.get_station_by_code(station_code)

    #     if station.queue == "" or station.queue is None:
    #         return []
    #     else:
    #         queue = json.loads(station.queue)
    #         return queue

    

    



# def delete_station(station_id):
#     try:
#         start_time = time.time()
#         curr_session = Session()
        
#         station = curr_session.query(model.Stations).filter_by(id = station_id).first()
#         station.is_deleted = True
#         curr_session.commit()
#         end_time = time.time()
#         metrics.register_func_average_time(start_time,end_time,sys._getframe().f_code.co_name)
#     except Exception as e:
#         db_log.error(redBright(f'delete_station error. Exception: {e}'))
#         metrics.register_func_error(sys._getframe().f_code.co_name)
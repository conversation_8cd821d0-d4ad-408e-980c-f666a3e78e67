from flask_restx import Namespace,Resource

from .... import common as common
from ... import api_routing as api_routing

from .... import api

nskey  = Namespace(api_routing.UrlFolder.key,description="HCC Key Related API")


@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nskey.route(api_routing.UrlPath.new_key_pair)
class NewKeyPair(Resource):  
    @api.doc(description='Call this endpoint to create or replace the public & private key pair in HCC')
    def post(self):
        from ....key import KeyGen

        KeyGen.initialize()

        return common.StandardResponse.response(message = "Key Pair Generated")
    
@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nskey.route(api_routing.UrlPath.hcc_public_key)
class HCCPublicKey(Resource):  
    @api.doc(description='Call this endpoint to get public key of HCC')
    def get(self):
        """
        For HCC Public Key

        Returns:
            _type_: _description_
        """
        from ....key import KeyGen

        public_key = KeyGen.get_public_key_pem()

        return common.StandardResponse.response(message=public_key)
    
@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nskey.route(api_routing.UrlPath.oauth_public_key)
class PublicKey(Resource):  
    @api.doc()
    def post(self):
        """
        For OAuth Public Key

        Returns:
            _type_: _description_
        """
        from ....auth import AuthServices

        AuthServices.initialize()

        return common.StandardResponse.response(message = "Public Key updated successfully")
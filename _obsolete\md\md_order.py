from app import db


class MdOrder(db.Model):
    id: int
    ex_order_id: int
    skycar_id: int
    maintanence_dock_id: int
    type: str
    status: str
    created_at: str
    updated_at: str

    __tablename__ = 'md_order'

    id = db.Column(
        db.Integer,
        primary_key=True)

    ex_order_id = db.Column(
        db.Integer,
        index=False
    )

    skycar_id = db.Column(
        db.Integer,
        index=False
    )

    maintanence_dock_id = db.Column(
        db.Integer,
        index=False
    )

    type = db.Column(
        db.String(128),
        index=False,
        nullable=False
    )

    status = db.Column(
        db.String(128),
        index=False,
        nullable=False
    )

    created_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

    updated_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

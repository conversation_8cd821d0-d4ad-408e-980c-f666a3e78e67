import types
import asyncio
import functools

from termcolor import colored
from werkzeug import exceptions

from .. import common as common,enum_collections as ec,app

def decorateAllFunctionInClass(decorator):
    """ Decorate all function with argument decorator by checking the reflection of iterable obj whether is equal to types.FunctionType.
    Example Usage

    @decorateAllFunctionInClass(log_and_suppress_error())
    class YourClass():
        def __init__()
        def functionOne() 
        def functionTwo() 
        
    """
    def decorate(_class):
        for k, v in _class.__dict__.items():
            
            if isinstance(v, types.FunctionType): 
                setattr(_class, k, decorator(v))

            elif isinstance(v, classmethod):
                func = v.__func__
                setattr(_class, k, classmethod(decorator(func)))

            elif isinstance(v, staticmethod):
                func = v.__func__
                setattr(_class, k, staticmethod(decorator(func)))

        return _class
    return decorate

def log_and_suppress_error(log_func):
    """
    Decorator that logs errors exception and stops them from raising
    to caller.

    Example
    -------
    @log_and_suppress_error(log_func=<your logger>)
    def <your function>(<arguments>):
        ...
    """
    def argument_decorator(original_function):
        @functools.wraps(original_function)
        def decorated_function(*args, **kwargs):
            try:
                value =  original_function(*args, **kwargs)
                return value
            except Exception as e:
                if original_function.__qualname__ == 'DBMonitor.retry_session.<locals>._decorate':
                    log_func.error(f'{e}')
                else:
                    log_func.error(f'{original_function.__qualname__}() raise Exception: {e}')
        return decorated_function
    return argument_decorator

def async_log_and_suppress_error(log_func):
    """
    Specially made for wsroute
    Decorator logs errors exception and stops them from raising
    to caller.

    Example
    -------
    @async log_and_suppress_error(log_func=<your logger>)
    def <your function>(<arguments>):
        ...
    """
    def argument_decorator(original_function):
        @functools.wraps(original_function)
        async def decorated_function(*args, **kwargs):
            try:
                return await original_function(*args, **kwargs)
            except asyncio.TimeoutError:
                log_func.error(f'{original_function.__qualname__}() has timeout')
            except Exception as e:
                log_func.error(f'{original_function.__qualname__}() raise Exception: {e}')
        return decorated_function
    return argument_decorator

def db_log_and_suppress_error(log_func):
    """
    Specially made for db
    Decorator that logs errors exception and stops them from raising
    to caller.

    Example
    -------
    @db_log_and_suppress_error(log_func=<your logger>)
    def <your function>(<arguments>):
        ...
    """
    def argument_decorator(original_function):
        @functools.wraps(original_function)
        def decorated_function(*args, **kwargs):
            try:
                with app.app_context():
                    value =  original_function(*args, **kwargs)
                    return value
            except Exception as e:
                log_func.error(colored(f'{original_function.__qualname__}() raise Exception: {e}','light_red'))
        return decorated_function
    return argument_decorator

def log_and_suppress_return_error():
    """
    Specially made for route (api)
    Decorator that logs errors exception and return error them if there is any
    to caller.

    Example
    -------
    @log_and_suppress_return_error(log_func=<your logger>)
    def <your function>(<arguments>):
        ...
    """
    def argument_decorator(original_function):
        @functools.wraps(original_function)
        def decorated_function(*args, **kwargs):
            try:
                value =  original_function(*args, **kwargs)
                return value 
            except exceptions.BadRequest as e:
                return common.StandardResponse.response(status = False,message=f"Please check again your request data", code = ec.HTTPStatus.BAD_REQUEST.value)
            except Exception as e:
                return common.StandardResponse.response(status = False,message=f"HWX internal exception : {e}", code = ec.HTTPStatus.INTERNAL_SERVER_ERROR.value)
        return decorated_function
    return argument_decorator


def sio_log_and_suppress_error(log_func):
    """
    Specially made for wsroute which wrap with one more layer of before_sio_event_handler
    Decorator that logs errors exception and stops them from raising
    to caller.

    Example
    -------
    @log_and_suppress_error(log_func=<your logger>)
    def <your function>(<arguments>):
        ...
    """
    def argument_decorator(original_function):
        @functools.wraps(original_function)
        def decorated_function(*args, **kwargs):
            try:
                return original_function(*args, **kwargs)
            except Exception as e:
                log_func.error(colored(f'{e}','light_red'))
        return decorated_function
    return argument_decorator


def before_sio_event_handler(func):
    from .. import DBMonitor
    from ..common import JsonPayload

    def wrapper(*args, **kwargs):
        try:
            if not DBMonitor.is_down:
                return func(*args, **kwargs)
            else:
                return JsonPayload.jsonFormat(None,'Unable to access database.')
        except Exception as e:
            raise Exception(f'{func.__qualname__} raise exception : {e}')

    return wrapper
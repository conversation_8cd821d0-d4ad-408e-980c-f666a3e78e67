import json
import datetime

from termcolor import cprint,colored
from flask_restx.inputs import boolean
from flask_restx import Namespace,Resource,reqparse, fields

from .... import api
from ... import api_routing as api_routing,request_parsers as req_parser
from .... import adapter as adp,common as common,enum_collections as ec,database as db

nsoperation = Namespace(api_routing.UrlFolder.operation,description="Operation API")


next_bin_parser = req_parser.mock_body_parser.copy()
next_bin_parser.add_argument("to_index",required = False,type = int,location ='json')

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsoperation.route(api_routing.UrlPath.next_bin)
class NextBin(Resource):
    @nsoperation.doc(description = "To perform next bin operation for bin at worker point.")
    @api.expect(next_bin_parser)
    def post(self):

        data = next_bin_parser.parse_args(strict=True)
        
        station_code = int(data['station_code'])
        bin_no = str(data['storage_code'])  
        to_index = int(data['to_index']) if data['to_index'] else None

        valid , msg = db.st_mov_db_func.complete_next_bin_job(station_code,bin_no,to_index)
       
        if not valid:
            reply =  common.StandardResponse.response(False,message=msg)
        else:
            reply =  common.StandardResponse.response()
        return reply

check_queue_parser = reqparse.RequestParser()
check_queue_parser.add_argument("code",required = True, type = int, location = "args")
check_queue_parser.add_argument("cube",required = False, type = str, location = "args")
@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsoperation.route(api_routing.UrlPath.check_queue)
class CheckQueue(Resource):
    @nsoperation.doc(description = "To check the queue of the station for the bin picked order.")
    @api.expect(check_queue_parser)
    def get(self):
    
        data = check_queue_parser.parse_args(strict=True)

        station_code = int(data['code'])
        cube = data['cube']
        
        bin_list = db.st_mov_db_func.check_st_queue(station_code,cube)
            
        reply = {
            "station_code": station_code,
            "storage_codes": [int(bin) for bin in bin_list],
        }        
        
        return common.StandardResponse.response(model=reply)

update_bin_no_parser = reqparse.RequestParser()
update_bin_no_parser.add_argument("old_storage_code",required = True,type = int, location = "json")
update_bin_no_parser.add_argument("new_storage_code",required = True,type = int, location = "json")

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsoperation.route(api_routing.UrlPath.update_bin_no)
class UpdateBinNo(Resource):
    @nsoperation.doc(description = "To modify the storage code that alreaady in staiton.")
    @api.expect(update_bin_no_parser)
    def patch(self):

        data = update_bin_no_parser.parse_args(strict = True)

        old_bin_no = str(data['old_storage_code'])
        new_bin_no = str(data['new_storage_code'])
        
        order = db.st_order_db_func.find_active_order_by_bin_no(old_bin_no)
        
        if order is None:
            return common.StandardResponse.response(False,message=f'Find no order for bin number {old_bin_no}.')

        station = db.st_db_func.get_station_by_code(order.station_code)
        db.st_db_func.patch_bin_in_st_queue(station.code,old_bin_no,new_bin_no)
        db.st_order_db_func.update_order(dict(id=order.id),dict(storage_code=new_bin_no))
        db.st_mov_db_func.patch_bin_no_by_order_id(order.id,new_bin_no)
        if station.type == ec.CubeStationType.I.value:
            db.st_event_db_func.patch_bin_no(station,old_bin_no,new_bin_no)
        if station.bin_at_worker == old_bin_no:
            self.update_station(station.code,dict(bin_at_worker=new_bin_no))
        
        return common.StandardResponse.response(True,message="Update Successfully")

undo_st_order = reqparse.RequestParser()
undo_st_order.add_argument("order_id",required = True, type = int, location = "json")

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsoperation.route(api_routing.UrlPath.undo_station_order)
class UndoStationOrder(Resource):
    @nsoperation.doc(description = "To delete a mistakenly created station order.")
    @api.expect(undo_st_order)
    def delete(self):
        data = undo_st_order.parse_args()

        tc_order_id = int(data["tc_order_id"])
        order_id = db.st_order_db_func.update_order(dict(tc_order_id=tc_order_id),dict(status=ec.OrderStatus.DELETED.value))
        db.st_mov_db_func.clear_jobs_with_order_id(order_id)
        
        return common.StandardResponse.response()


station_enroll_parser = req_parser.station_code_body_parser.copy()
station_enroll_parser.add_argument("is_enroll",required = True, type = boolean, choices =(True,False))

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsoperation.route(api_routing.UrlPath.station_enroll)
class StationEnroll(Resource):
    @nsoperation.doc(description='To get the station that is in enrollment mode.')
    def get(self):
        from ....runtime import runtime 
        return common.StandardResponse.response(True,data = list(station.code for station in runtime.get_runtime_st_list() if station.mode==ec.CubeStationMode.ENROLL.value) )

    @nsoperation.doc(description='To start station enrollment mode.')
    @api.expect(station_enroll_parser)
    def post(self):
        from ....service_handler import enrollment_log

        data = station_enroll_parser.parse_args(strict=True)

        station_code = data['station_code']
        station_is_enroll = data.is_enroll
        
        if station_code is None or station_code == "" or station_is_enroll is None or station_is_enroll == "":
            return common.StandardResponse.response(status = False, message="Invalid request data")
        
        station_code = int(station_code)

        station = db.st_db_func.get_station_by_code(station_code)
        
        if station.type in [ec.CubeStationType.QC.value,ec.CubeStationType.BRIDGE.value]:
            return common.StandardResponse.response(status = False, message="Invalid station type.")
        
        # if not station.is_active:
            # return common.StandardResponse.response(status=False,message="Station not active.")
            
        # else:
        if station_is_enroll:
            if station.mode != ec.CubeStationMode.NORMAL.value:
                respond_msg = f"Station {station_code} is already in {station.mode} mode."
                cprint(respond_msg,'light_yellow',attrs=["underline"])
                enrollment_log.warning(respond_msg)
                return common.StandardResponse.response(status=False,message = respond_msg)
            else:
                if db.st_order_db_func.check_station_ready_change_mode(station.code,ec.CubeStationMode.ENROLL.value):
                    common.BackgroundTask.start_station_mode(station_code,ec.CubeStationMode.ENROLL.value)
                else:
                    return common.StandardResponse.response(status=False,message = f"Need to clear all bin in station to start enrollment mode for station")

        else:
            if station.mode != ec.CubeStationMode.ENROLL.value:
                respond_msg = f"Station {station_code} was not in enrollment mode."
                cprint(respond_msg,'light_yellow',attrs=["underline"])
                enrollment_log.warning(respond_msg)
                return common.StandardResponse.response(status=False,message = respond_msg)
            else:
                common.BackgroundTask.stop_station_mode(station_code)
        return common.StandardResponse.response(message = f"Update enrollment status for ST{station_code} {station_is_enroll}")


@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsoperation.route(api_routing.UrlPath.charge_out_bin)
class ChargeOutBin(Resource):
    @nsoperation.doc(description='To be used when bin need to move out from station.')
    @api.expect(req_parser.station_storage_code_parser)
    def post(self):

        data = req_parser.station_storage_code_parser.parse_args(strict=True)

        station_code = data.station_code
        storage_code = data.storage_code
        
        station = db.st_db_func.get_station_by_code(station_code)


        processing_station_order_id = db.st_order_db_func.update_order(dict(station_code=station_code,
                                                                         storage_code=storage_code,
                                                                         status=ec.OrderStatus.PROCESSING.value),
                                                                    dict(status=ec.OrderStatus.DELETED.value))
        if not processing_station_order_id:
            return common.StandardResponse.response(False,message=f'No processing bin {storage_code} found in ST{station_code}')
        
        msg = common.STMsgFormatter.remove_bin_msg(station_code,storage_code)
        # handler.send_tcp_msg(station_code,msg)
        common.BackgroundTask.start_retry(station_code,msg)
        
        db.st_db_func.remove_from_station_queue(station_code,storage_code)
        deleted_job_id = db.st_mov_db_func.clear_jobs_with_order_id(processing_station_order_id)
        if station.type==ec.CubeStationType.I.value:
            db.st_event_db_func.reschedule_st_event_after_chargeout(station,storage_code,deleted_job_id,processing_station_order_id)
        # if station at worker 
        if station.bin_at_worker == storage_code:
            db.st_db_func.update_station(station_code,dict(bin_at_worker = None))
            if station.type==ec.CubeStationType.I.value:
                # start the pending request since bin not at worker anymore
                db.st_gw_req_db_func.start_pending_gw_request(station_code)
                # light up other station bin if there is any bin at sis station
                if station.mode != ec.CubeStationMode.TRANSFER.value:
                    db.st_mov_db_func.light_up_next_bin(station.matrix_code)

        print(common.formatString('INFO',colored(f'Bin {storage_code} has been successfully chargeout from ST{station_code}','light_green')))
        return common.StandardResponse.response()

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsoperation.route(api_routing.UrlPath.skydrop_recovery)
class SkyDropRecovery(Resource):
    @api.expect(req_parser.station_storage_code_body_parser)
    def post(self):
        '''
        API to call to recover on HCC and PLC side when skycar drop without requesting
        Need to make sure all the movement is stop and the dropped bin is at drop point
        Only called by TC when it is I station
        '''
        data = req_parser.station_storage_code_body_parser.parse_args(strict=True)

        station_code = data.station_code
        storage_code = data.storage_code

        station = db.st_db_func.get_station_by_code(station_code)
        st_queue = db.st_db_func.get_station_queue(station_code)
        # check if TC already create station order
        order = db.st_order_db_func.find_active_order(station_code,storage_code)

        if not order:
            return common.StandardResponse.response(False,f"No order created yet from TC for storage code {storage_code} in ST{station_code}")

        # non-I station
        if station.type != ec.CubeStationType.I.value:
            return common.StandardResponse.response("Update command has sent to PLC")
        
        # I station
        else:

            # stop pending cancel request
            if pending_canceled_req := db.st_gw_req_db_func.get_latest_gateway_req(dict(
                station_code = station.code,
                type=ec.StationRequestType.DROP.value,
                status=ec.OrderStatus.CANCELED.value,
                storage_code=storage_code,
                canceled_ack = False
                )):
                db.st_gw_req_db_func.update_gateway_req_by_id(pending_canceled_req.id,dict(canceled_ack=True))

            st_event_q = db.st_db_func.get_station_event_queue(station_code)
            station.latest_cancel_drop_msg[station.inner_drop] = ''

            # Check if there is depending event 
            if not st_event_q.get_size():
                db.st_event_db_func.create_st_event(ec.CubesIStationEvent.DROP_DONE.value,station_code,storage_code)
                return common.StandardResponse.response("Event is created, update command has sent to PLC")
            
            curr_event:adp.EventAdapter = st_event_q.get_first_event()
            
            if curr_event.event_name == ec.CubesIStationEvent.NEXT_BIN.value and st_queue[station.worker]:
                # check if job generated
                if curr_event.is_processed:
                    # remove generated job
                    job_generated = curr_event.job_to_complete_event
                    for job_id in job_generated:
                        db.st_mov_db_func.delete_job(job_id)

                    # recover next bin job to regenerate correct job
                    db.st_event_db_func.recover_st_event(curr_event)
                else:
                    return common.StandardResponse.response(False,message='There is NEXT_BIN event in the queue but not processed yet')
            elif curr_event.event_name == ec.CubesIStationEvent.REQ_PICK.value and st_queue[station.inner_pick]:
                if curr_event.is_processed:
                    db.st_event_db_func.create_st_event(ec.CubesIStationEvent.DROP_DONE.value,station_code,storage_code)
                    # move req pick event to the end 
                    req_pick_event = st_event_q.next_event()
                    st_event_q.add_event(req_pick_event)
                    # pick done event 
                    pick_done_event = st_event_q.next_event()
                    st_event_q.add_event(pick_done_event)
                    
                    # remove generated job
                    job_generated = curr_event.job_to_complete_event
                    for job_id in job_generated:
                        db.st_mov_db_func.delete_job(job_id)

                    # recover next bin job to regenerate correct job
                    db.st_event_db_func.recover_st_event(curr_event)
                else:
                    return common.StandardResponse.response(False,message='There is REQ_DROP&DROP_DONE event in the queue but not processed yet')
            return common.StandardResponse.response("Event is created, update command has sent to PLC")

skypick_recovery_parser = req_parser.station_storage_code_body_parser.copy()
skypick_recovery_parser.add_argument("tc_order_id",required=False,type=int,location='json')

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsoperation.route(api_routing.UrlPath.skypick_recovery)
class SkyPickReocvery(Resource):
    @api.expect(skypick_recovery_parser)
    def post(self):
        '''
        API to call to recover on HCC and PLC side when skycar pick without requesting
        Need to make sure all the movement is stop and the picked bin is at skycar
        Only called by TC when it is I station
        Assuming station status bin is already at pick point
        '''
        data = skypick_recovery_parser.parse_args(strict=True)

        station_code = data.station_code
        storage_code = data.storage_code
        tc_order_id = data.tc_order_id

        station = db.st_db_func.get_station_by_code(station_code)

        if station.type == ec.CubeStationType.I.value:
            # stop pending cancel request
            if pending_canceled_req := db.st_gw_req_db_func.get_latest_gateway_req(dict(
                station_code = station.code,
                type=ec.StationRequestType.PICK.value,
                status=ec.OrderStatus.CANCELED.value,
                storage_code=storage_code,
                canceled_ack = False
                )):
                db.st_gw_req_db_func.update_gateway_req_by_id(pending_canceled_req.id,dict(canceled_ack=True))
                station.latest_cancel_pick_msg[station.inner_pick] = ''

            # second find the latets update pick to use as dependency
            if tc_order_id:
                st_order = db.st_order_db_func.find_order(dict(tc_order_id = tc_order_id))
            else:
                st_order = db.st_order_db_func.find_latest_order(dict(station_code=station_code,
                                                                      storage_code=storage_code))

            if not st_order:
                return common.StandardResponse.response(False,message=f'ST{station_code} does not have bin : {storage_code}, tc order id : {tc_order_id} record.')
            # if enroll tc will not give order id
            if st_order.type != ec.OrderType.ENROLL.value and tc_order_id is None:
                if st_order.status == ec.OrderStatus.COMPLETED.value:
                    if datetime.datetime.now() - datetime.timedelta(minutes=5) > st_order.updated_at:
                        return common.StandardResponse.response(False,message=f'ST{station_code} does not have recent bin {storage_code} record.')
            
            if st_order.status != ec.OrderStatus.COMPLETED.value:
                if not db.st_order_db_func.check_bin_arrive_pick(station_code,storage_code):
                    if st_order.type != ec.OrderType.ENROLL.value:
                        return common.StandardResponse.response(False,message=f'Bin {storage_code} has not reached pick point yet.')

            
            update_pick_job = db.st_mov_db_func.find_job(dict(
                order_id = st_order.id,
                type = ec.CubeStationJobType.UPDATE_PICK.value
            ))
            str_job_list = json.dumps([update_pick_job.id])
            # add a dependcy event
            db.st_event_db_func.create_st_event(ec.CubesIStationEvent.RECOVERY.value,station_code,storage_code,is_processed=True,job_to_complete_event=str_job_list)
            db.st_event_db_func.create_st_event(ec.CubesIStationEvent.PICK_DONE.value,station_code,storage_code)
        
            return common.StandardResponse.response("Event is created, update command has sent to PLC")
        else:
            return common.StandardResponse.response(True)



dummy_recovery_parser = req_parser.station_code_body_parser.copy()
dummy_recovery_parser.add_argument(
    "bin_list",
    required=True,
    type=list,  # Expecting a list directly
    help="A list of items",
    location='json'
)
bin_list_model = api.model(api_routing.UrlPath.dummy_recovery, {
    "station_code": fields.Integer(required=True, description="The code of the station"),
    "bin_list": fields.List(fields.String, required=True, description="A list of items")
})
@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsoperation.route(api_routing.UrlPath.dummy_recovery)
class StationBinRecovery(Resource):
    @api.expect(bin_list_model)
    def post(self):
        '''
        Used by TC-dash
        
        API to call to start station bin recovery where user will move 
        the bin to the correct position and hcc will inform plc to 
        clear the bin status and resend all the station job to plc
        '''
        data = dummy_recovery_parser.parse_args(strict=True)

        station_code = data.station_code
        bin_list = data.bin_list
        
        station = db.st_db_func.get_station_by_code(station_code)

        if station.is_active:
            return common.StandardResponse.response(False,'Station is active cannot enter recovert mode')

        user_confirmed_plc_status = '|'.join(bin_list)
 
        db.st_db_func.update_station(station_code,dict(is_recovery=True,
                                                       plc_status=user_confirmed_plc_status))

        return common.StandardResponse.response()

bin_history_parser = reqparse.RequestParser()
bin_history_parser.add_argument("bin_no",required = True, type = int, location = "args")
bin_history_parser.add_argument("page",required = False,type = int,location = 'args', default = 1)
bin_history_parser.add_argument("per_page",required = False,type = int,location = 'args', default =10)
@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsoperation.route(api_routing.UrlPath.bin_history)
class BinHistory(Resource):
    @nsoperation.doc(description = "To get the bin today's history.")
    @api.expect(bin_history_parser)
    def get(self):
        from math import ceil

        args = bin_history_parser.parse_args(strict=True)
        
        bin_no = args.get('bin_no',None)
        page = args.get('page',1)
        per_page = args.get('per_page',10)

        if bin_no:
            bin_no = str(bin_no)
        bin_history = db.st_order_db_func.get_bin_history(bin_no)

        total_items = len(bin_history)
        total_pages = ceil(total_items / per_page)

        start_index = (page - 1) * per_page
        end_index = start_index + per_page

        paginated_data = bin_history[start_index:end_index]
        return common.StandardResponse.response(data=paginated_data,metadata = {
                "total_page": total_pages})
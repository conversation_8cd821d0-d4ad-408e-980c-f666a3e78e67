from datetime import datetime as dt


from app import app,db
from ...models import Asrs_job
from ... import common as common
from ... import enum_collections as ec

db_log = common.get_logger_console('asrs_db_error')

@common.decorateAllFunctionInClass(common.db_log_and_suppress_error(app,db_log))
class AsrsJobSQLAlchemyQueries:
    
    def create_job(self,order_id:int,job_type:ec.AsrsJobType,job_status:ec.OrderStatus,pallet_id:str,triggered_by:str=None,pred_id:int=None)->int:
        
        asrs_job = Asrs_job(
            order_id=order_id,
            job_type=job_type,
            job_status=job_status,
            pallet_id=pallet_id,
            pred_id=pred_id,
            triggered_by=triggered_by,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session = db.session
        curr_session.add(asrs_job)
        curr_session.commit()

        job_id = asrs_job.id

        return job_id
        
    def get_job_by_id(self,id:int)->Asrs_job:
        curr_session = db.session
        job = curr_session.query(Asrs_job).filter(Asrs_job.id == id).first()
        return job
    
    def get_jobs_by_order_id(self,order_id:int):
        curr_session = db.session
        jobs = curr_session.query(Asrs_job).filter(Asrs_job.order_id == order_id).all()
        return jobs
    
    def get_active_jobs_by_order_id(self,order_id:int):
        curr_session = db.session
        jobs = curr_session.query(Asrs_job).filter_by(order_id = order_id).filter(
            ((Asrs_job.job_status == ec.OrderStatus.AVAILABLE.value) | (Asrs_job.job_status == ec.OrderStatus.PROCESSING.value)) &
            ((Asrs_job.job_type == ec.AsrsJobType.PICK.value) |(Asrs_job.job_type == ec.AsrsJobType.DROP.value) |(Asrs_job.job_type == ec.AsrsJobType.MOVE.value))).all()
        return jobs
    
    def get_available_job_by_type(self,job_type:str):
        curr_session = db.session
        job = curr_session.query(Asrs_job).filter_by(job_type = job_type, job_status=ec.OrderStatus.AVAILABLE.value).first()
        return job
    
    def get_job_by_order_id_type(self,order_id:int, type:str)->Asrs_job:
        curr_session = db.session
        job = curr_session.query(Asrs_job).filter_by(order_id = order_id, type = type).first()
        return job
    
    def get_lost_data_job(self,pallet_id:str)->Asrs_job:
        curr_session = db.session
        move_job_lost_data = curr_session.query(Asrs_job).filter(Asrs_job.job_status == ec.OrderStatus.PROCESSING.value, Asrs_job.pallet_id == pallet_id, Asrs_job.job_type == ec.AsrsJobType.MOVE.value).first()
        return move_job_lost_data
    
    def get_succ_job(self,job_id:int)->Asrs_job:
        curr_session = db.session
        succ_job = curr_session.query(Asrs_job).filter_by(pred_id = job_id).first()
        return succ_job
    
    def get_earliest_job_by_order_id(self,order_id:int)->Asrs_job:
        curr_session = db.session
        job = curr_session.query(Asrs_job).filter_by(order_id = order_id).filter(Asrs_job.job_status != ec.OrderStatus.COMPLETED.value).order_by(Asrs_job.created_at).first()
        return job
    
    def get_processing_error_jobs(self):
        curr_session = db.session
        job = curr_session.query(Asrs_job).filter_by(job_status = ec.OrderStatus.PROCESSING.value).filter((Asrs_job.job_type == ec.AsrsJobType.PC_LOST_DATA.value) | (Asrs_job.job_type == ec.AsrsJobType.RECOVERY_DROP.value) ).all()
        return job
    
    
    def check_jobs_interval(self,interval,ack):
        curr_session = db.session
        now = dt.now()
        jobs = curr_session.query(Asrs_job).filter(Asrs_job.job_status == ec.OrderStatus.PROCESSING.value, now > Asrs_job.updated_at + interval, Asrs_job.ack == ack ).all()
        return jobs
    

    def update_job(self,job_id:int,update:dict):
        curr_session = db.session
        job :Asrs_job = curr_session.query(Asrs_job).filter(Asrs_job.id == job_id).first()
        if job:
            for k,v in update.items():
                job.__setattr__(k,v)
            job.updated_at = dt.now()
            curr_session.commit()
        else:
            db_log.error(f'No job with id {job_id} found.') 
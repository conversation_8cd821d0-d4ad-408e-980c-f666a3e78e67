from datetime import datetime as dt

from ..app import models as model,app,db
from ..app.enum_collections import  AsrsStationType

            
def clearDB():
    """
    clear all db data
    """
    with app.app_context():
        curr_session = db.session
        # Clear previous seeded data
        curr_session.query(model.Asrs_job).delete()
        curr_session.query(model.Asrs_order).delete()
        curr_session.query(model.AsrsStations).delete()
        curr_session.commit()


def seedAsrsStations():
    """
    Seed ASRS station
    """
    with app.app_context():
        curr_session = db.session
        add_asrs_station = model.AsrsStations(
            station_id=1,
            type=AsrsStationType.LIFTER_STATION.value,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_asrs_station)

        add_asrs_station = model.AsrsStations(
            station_id=2,
            type=AsrsStationType.BREAKBULK_STATION.value,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_asrs_station)

        add_asrs_station = model.AsrsStations(
            station_id=3,
            type=AsrsStationType.BREAKBULK_STATION.value,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_asrs_station)

        add_asrs_station = model.AsrsStations(
            station_id=4,
            type=AsrsStationType.QC_STATION.value,
            created_at=dt.now(),
            updated_at=dt.now()
        )
        curr_session.add(add_asrs_station)
        curr_session.commit()


def seedAllStations():
    seedAsrsStations()
    
    return "All station has been seeded"

  


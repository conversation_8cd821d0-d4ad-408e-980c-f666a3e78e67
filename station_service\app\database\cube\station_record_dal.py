import datetime

from typing import List
from sqlalchemy import Date, cast, or_, and_, asc


from ... import app,DBMonitor,db
from ... import common as common,models as model,enum_collections as ec

db_log = common.LogManager('cube_db_error',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_log))
class StationRecordSQLAlchemyQueries:
        
    @DBMonitor.retry_session
    def add_new_record(self,data:dict):
        with app.app_context():
            st_record = model.StationRecord(
                station_code = data.get('station_code'),
                type = data.get('type'),
                start_time = data.get('start_time'),
                created_at = datetime.datetime.now(),
                updated_at = datetime.datetime.now()
            )
            curr_session = db.session
            curr_session.add(st_record)
            curr_session.commit()
        
    @DBMonitor.retry_session
    def find_record(self,filter_dict:dict)->model.StationRecord:
        with app.app_context():
            curr_session = db.session
            rec = curr_session.query(model.StationRecord).filter_by(**filter_dict).first()
            return rec
        
    @DBMonitor.retry_session
    def find_earliest_record(self,filter_dict:dict)->model.StationRecord:
        with app.app_context():
            curr_session = db.session
            rec = curr_session.query(model.StationRecord).filter_by(**filter_dict).order_by(asc(model.StationRecord.created_at)).first()
            return rec
            
    @DBMonitor.retry_session
    def find_st_records_on_date_by_type(self,station_code:int,from_date,to_date,type:ec.StationRecordType)->List[model.StationRecord]:
        with app.app_context():
            curr_session = db.session
            st_records = curr_session.query(model.StationRecord).filter(or_(and_(cast(model.StationRecord.start_time,Date)<= to_date,from_date<=cast(model.StationRecord.start_time,Date),model.StationRecord.end_time.isnot(None)),
                                                                            and_(from_date<=cast(model.StationRecord.start_time,Date),model.StationRecord.end_time.is_(None)))).\
                                                                filter_by(station_code = station_code, type = type).all()
            return st_records
            
    @DBMonitor.retry_session
    def update_st_record(self,filter_dict:int,update:dict)->int:
        with app.app_context():
            curr_session = db.session
            req = curr_session.query(model.StationRecord).filter_by(**filter_dict).first()
            if req:
                for k,v in update.items():
                    req.__setattr__(k,v)
                req.updated_at = datetime.datetime.now()
                req_id = req.id
                curr_session.commit()
                return req_id
            else:
                db_log.error(f'No station record with  {str(filter_dict)} found')
                return None

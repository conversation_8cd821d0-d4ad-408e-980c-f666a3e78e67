from termcolor import colored
from flask import request, Blueprint

from ... import common as common, server as asrs_server, database as db, service_handler as handler

asrs = Blueprint('asrs', __name__, url_prefix='/asrs')

asrs_route = common.get_logger_console('asrs_route_recv')

# starting_fresh = False

@asrs.route("/test", methods=['GET'])
@common.old_log_and_suppress_return_error(asrs_route)
def test():
    
    asrs_route.info(colored(f'Received - test','light_magenta'))
    return {"status" : "Connected to ASRS."} 

@asrs.route("/asrs-free", methods=['GET','POST'])
@common.old_log_and_suppress_return_error(asrs_route)
def asrs_free():
    
    if request.method == 'POST':
        data = request.get_json()
        asrs_route.info(colored(f'Received - ','light_magenta')+f'{data}')
        handler.free_asrs(int(data['id']))
    return {"asrs in use":list(handler.asrs_in_use)}
 
@asrs.route("/asrs-recovery", methods=['GET','POST'])
@common.old_log_and_suppress_return_error(asrs_route)   
def asrs_recovery():
    
    if request.method == 'POST':
        data = request.get_json()
        asrs_route.info(colored(f'Received - ','light_magenta')+f'{data}')
        handler.asrs_recovery_done(int(data['id']))
    return {"asrs in recovery":list(handler.asrs_in_recovery)}
    

@asrs.route("/create_order", methods=['POST'])
@common.old_log_and_suppress_return_error(asrs_route)       
def create_order():
    
    data = request.get_json()
    asrs_route.info(colored(f'Received - create order : ','light_magenta')+f'{data}')
    type = ''

    order_id = int(data['order_id'])
    asrs_id = handler.ASRSLogic.map_asrs_id(data['x'])
    asrs_order_id = db.as_order_db_func.create_order(data['x'], data['y'], data['z'], asrs_id, data['method'], type, data['order_id'], data['pallet_no'], data['station_id'])
    
    if asrs_order_id is None:
        return "Duplicate Order","400"
    else:
        db.as_job_db_func.generate_jobs(asrs_order_id)
        
        new_order_handler = handler.OrderHandler(order_id,asrs_order_id)
        new_order_handler.start_order_ts()
        
            
        return "OK","200"
    
@asrs.route("/connection_list", methods=['GET'])
@common.old_log_and_suppress_return_error(asrs_route)     
def get_connection_list():
    asrs_route.info(colored(f'Received - connection_list','light_magenta'))
    
    addr_list = []
    client_list = []
    connection_list = []
    
    for conn in asrs_server.as_connection_list.copy().values():
        addr_list.append((conn.ip,conn.port))
        client_list.append(conn.plc_name)
        connection_list.append(str(conn.conn))
        
    reply = {
        "addr":addr_list,
        "client":client_list,
        "socket_connection":connection_list
    }
    
    return reply
    
        
@asrs.route("/mock_plc_msg", methods = ['POST'])
@common.log_and_suppress_error(asrs_route)
def mock_plc_msg():
    
    data = request.get_json()
    
    msg = data['message']
    asrs_route.info(colored('Received - mock plc msg : ','light_magenta')+f'{msg}')  
    
    handler.ASRSService.add_message(msg)

    return "OK",200
        
@asrs.route("/send_plc_msg", methods = ['POST'])
@common.log_and_suppress_error(asrs_route)
def send_plc_msg():
    data = request.get_json()
    
    target_client = data['plc_name'] #ex: AS1,PC1
    msg = data['message']

    asrs_server.send_asrs_msg(target_client, msg)

    return "OK", 200
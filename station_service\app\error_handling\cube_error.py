import pytz

from typing import List
from datetime import datetime

from ..blueprints.route.client import HttpClient
from .. import common as common,enum_collections as ec,database as db
from .error_code import get_error_code, ErrorDict

station_error_log = common.LogManager("cube_station_error",display_console=True,centralize_logging=True)

class ErrorCodeOverflowException(Exception):
    "Raise when the error code is more than the list"
    pass

class CubeEH:
        
    @classmethod
    @common.log_and_suppress_error(station_error_log)
    def create_station_error_msg(cls,station_code:int,error_list:List[str]):
        """
        Go through every error code in the list and generate an error payload for each error code
        Add each error into db
        Check if the same error code is already in db and not resolved yet, if yes ignore the error code 
        """
        new_error_name_list = []
        
       
        # Get current time in UTC
        current_time_str = datetime.now(pytz.timezone('UTC')).isoformat().replace('+00:00', 'Z')

        for error_name in error_list:
            if db.st_error_db_func.check_exisitng_station_error_msg(station_code,error_name): 
                continue
            # Ex: XXXSZYY, XXXSG, 12BZ1, 122BG
            if ec.StationErrorModuleCode.ST_CONVEYOR_ZONE.value in error_name:
                splited_error_name_list = error_name.split(ec.StationErrorModuleCode.ST_CONVEYOR_ZONE.value)
                error_code = int(splited_error_name_list[0])
                zone = int(splited_error_name_list[1])
                module = f"Conveyor Zone {zone}"
                type = ec.StationErrorModule.CONVEYOR_ZONE.value
            elif ec.StationErrorModuleCode.ST_GENERAL.value in error_name:
                splited_error_name_list = error_name.split(ec.StationErrorModuleCode.ST_GENERAL.value)
                error_code = int(splited_error_name_list[0])
                zone = None
                module = "General"
                type = ec.StationErrorModule.GENERAL.value
            elif ec.StationErrorModuleCode.B_CONVEYOR_ZONE.value in error_name:
                splited_error_name_list = error_name.split(ec.StationErrorModuleCode.B_CONVEYOR_ZONE.value)
                error_code = int(splited_error_name_list[0])
                zone = int(splited_error_name_list[1])
                module = f"Bridge Conveyor Zone {zone}"
                type = ec.StationErrorModule.CONVEYOR_ZONE.value
            elif ec.StationErrorModuleCode.B_GENERAL.value in error_name:
                splited_error_name_list = error_name.split(ec.StationErrorModuleCode.B_GENERAL.value)
                error_code = int(splited_error_name_list[0])
                zone = None
                module = "Bridge General"
                type = ec.StationErrorModule.GENERAL.value

            else:
                station_error_log.error(f"Invalid Error Name : {error_name}")
                continue

            error:ErrorDict = get_error_code(error_code,is_general=(type == ec.StationErrorModule.GENERAL.value))

            if error is None:
                station_error_log.error(f"Invalid Error Code {error_code}")
                continue
            
            
            error_id = db.st_error_db_func.add_station_error_message(station_code,error_name)
            payload = HttpClient.get_standard_payload(station_code,error_name,error['error_warning'],error_id,current_time_str)
            data = {
                "data":{
                    "error_level": error['error_level'],
                    "module":module, 
                    "error_message": error['error_warning'],
                    "action":error['error_action'],
                },
                "wms_data":payload
            }
            db.st_error_db_func.update_station_error_msg_payload(error_id,data)

            new_error_name_list.append(error_name)
            if zone:
                station_error_log.error(f'ST{station_code} Error: '+module+" "+error['error_warning'])
            else:
                station_error_log.error(f'ST{station_code} Error: '+error['error_warning'])

            HttpClient.inform_station_error_status(station_code,payload)

        common.saveNotification(title = f"Cube Station Error",
                                module=ec.ModuleCode.STATION.value,
                                Device = f'{ec.ModuleCode.STATION.value}{station_code}',
                                Error_List = ','.join(new_error_name_list)
                                )
    
    @classmethod
    @common.log_and_suppress_error(station_error_log)
    def reset_station_error_msg(cls,station_code:int):
        station_error_msg_list = db.st_error_db_func.get_existing_station_error_msg_list(station_code)
        
        # Get current time in UTC
        current_time_str = datetime.now(pytz.timezone('UTC')).isoformat().replace('+00:00', 'Z')

        for msg in station_error_msg_list:
            db.st_error_db_func.update_station_error_msg_status(msg.id)
            payload = HttpClient.get_standard_resolved_payload(msg.data["wms_data"],current_time_str)
            HttpClient.inform_station_error_status(station_code,payload)

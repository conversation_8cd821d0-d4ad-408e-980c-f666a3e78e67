{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from pprint import pprint"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1: ('ESTOP PRESSED, RELEASE ESTOP BUTTON',\n", "     'Please release all E-Stop button. Press reset and start back the machine',\n", "     1),\n", " 10: ('MV Z0 DEST ALARM, CHECK ACTUAL BIN / SENSOR',\n", "      'Check Unattend Bin Present',\n", "      1),\n", " 11: ('MV Z1 DEST ALARM, CHECK ACTUAL BIN / SENSOR',\n", "      'Check Unattend Bin Present',\n", "      1),\n", " 12: ('MV Z2 DEST ALARM, CHECK ACTUAL BIN / SENSOR',\n", "      'Check Unattend Bin Present',\n", "      1),\n", " 13: ('MV Z0 SENSOR NOT CLEAR, CHECK ACTUAL BIN / SENSOR',\n", "      'Check Unattend Bin Present',\n", "      1),\n", " 14: ('MV Z1 SENSOR NOT CLEAR, CHECK ACTUAL BIN / SENSOR',\n", "      'Check Unattend Bin Present',\n", "      1),\n", " 15: ('MV Z2 SENSOR NOT CLEAR, CHECK ACTUAL BIN / SENSOR',\n", "      'Check Unattend Bin Present',\n", "      1),\n", " 16: ('<PERSON><PERSON><PERSON><PERSON> BIN SENSOR NOT ON ALARM, CHECK ACTUAL BIN / SENSOR',\n", "      'Check Bin Position, Move until sensor on press reset and start',\n", "      1),\n", " 17: ('WS BIN SENSOR NOT ON ALARM, CHECK ACTUAL BIN / SENSOR',\n", "      'Check Bin Position, Move until sensor on press reset and start',\n", "      1),\n", " 18: ('DROP/PICK BIN SENSOR NOT ON ALARM, CHECK ACTUAL BIN / SENSOR',\n", "      'Check Bin Position, Move until sensor on press reset and start',\n", "      1),\n", " 19: ('OFFSET SENSOR ON ALARM, CHECK ACTUAL BIN / SENSOR',\n", "      'Check Bin Position, Move until sensor off press reset and start',\n", "      1),\n", " 20: ('WEIGHT NOT ZERO ALARM, CHECK WEIGHER', 'Check Weigher Connection', 1),\n", " 21: ('OVER WEIGHT ALARM, REDUCE WEIGHT ON BIN',\n", "      'Check Weight of the Bin, remove weight and press reset and start',\n", "      1),\n", " 22: ('DROP/PICK BIN SENSOR ON ALARM, CHECK ACTUAL BIN / SENSOR',\n", "      'Check Bin Position, Move until sensor on press reset and start',\n", "      1),\n", " 23: ('WS BIN SENSOR ON ALARM, CHECK ACTUAL BIN / SENSOR',\n", "      'Check Bin Position, Move until sensor on press reset and start',\n", "      1),\n", " 24: ('<PERSON><PERSON><PERSON><PERSON> BIN SENSOR ON ALARM, CHECK ACTUAL BIN / SENSOR',\n", "      'Check Bin Position, Move until sensor on press reset and start',\n", "      1),\n", " 25: ('BARCODE NOT MATCH ALARM, CHECK BIN BARCODE',\n", "      'Check Barcode Label or Barcode reader.',\n", "      1),\n", " 26: ('BARCOD<PERSON> READ TIMEOUT ALARM, CHECK BARCODE READER',\n", "      'Check Barcode Label or Barcode reader.',\n", "      1),\n", " 27: ('CURTAIN SENSOR TRIGGERED, REMOVE HAND AND START',\n", "      'Re<PERSON><PERSON> from WS, Press reset and start',\n", "      1),\n", " 28: ('ITEM PROTRUDE, TIDY BIN AND START',\n", "      'Re<PERSON><PERSON> from WS, Press reset and start',\n", "      1),\n", " 29: ('WEIGHER NO RESPONSE, RESET & START / CHECK CONNECTION',\n", "      'Check Weigher Connection',\n", "      1)}\n"]}], "source": ["# Load the Excel file into a DataFrame\n", "df = pd.read_excel('errorv2.xlsx', sheet_name='General')  # Specify your sheet name\n", "df = df.dropna(subset=[\"Warning\", \"Action\"])\n", "\n", "\n", "# Iterate over each row in the DataFrame\n", "pprint({\n", "    row['Error Code']: (\n", "        row['Warning'],\n", "        row[\"Action\"],\n", "        int(row['Error Level'])\n", "    )\n", "    for _, row in df.iterrows()\n", "})\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}
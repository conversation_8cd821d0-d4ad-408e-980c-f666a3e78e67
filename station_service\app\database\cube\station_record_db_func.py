import datetime

from typing import List

from ... import models as model,common as common,enum_collections as ec
from.station_record_dal import StationRecordSQLAlchemyQueries

db_func_log = common.LogManager('cube_db_func',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_func_log))
class StationRecordDBFunc:

    st_record_dal = StationRecordSQLAlchemyQueries()

    def add_record(self,station_code:int,type:ec.StationRecordType,start_time:datetime.datetime):
        
        not_end_record = self.st_record_dal.find_record(dict(station_code= station_code,
                                                             type = type,
                                                             end_time = None))
        
        if not not_end_record:
            all_args = locals()  
            if 'self' in all_args:
                del all_args['self']
            self.st_record_dal.add_new_record(all_args)


    def record_st_down_end(self,staiton_code:int,end_time:datetime.datetime):

        st_down_record = self.st_record_dal.find_record(dict(station_code = staiton_code,
                                                             type = ec.StationRecordType.DOWN.value,
                                                             end_time = None))
        if st_down_record:
            total_seconds = (end_time-st_down_record.start_time).total_seconds()
            self.st_record_dal.update_st_record(dict(id = st_down_record.id),dict(end_time = end_time,
                                                                                  total_seconds = total_seconds))
                                                                                  

    def get_st_record_by_date_type(self,station_code:int,from_date:datetime.datetime,to_date:datetime.datetime,type:ec.StationRecordType)->List[model.StationRecord]:
        return self.st_record_dal.find_st_records_on_date_by_type(station_code,from_date,to_date,type)
    
        # def record_st_enroll_end(self,station_code:int,end_time:datetime.datetime):

    #     st_enroll_record = self.st_record_dal.find_record(dict(station_code = station_code,
    #                                                          type = ec.StationRecordType.ENROLL.value,
    #                                                          end_time = None))
    #     if st_enroll_record:
    #         total_seconds = (end_time-st_enroll_record.start_time).total_seconds()
    #         self.st_record_dal.update_st_record(dict(id = st_enroll_record.id),dict(end_time = end_time,
    #                                                                               total_seconds = total_seconds))

    # def record_st_brdige_done(self,station_code:int,end_time:datetime.datetime):
    #     st_bridge_record = self.st_record_dal.find_earliest_record(dict(station_code = station_code,
    #                                                          type = ec.StationRecordType.BRIDGE.value,
    #                                                          end_time = None))
    #     if st_bridge_record:
    #         total_seconds = (end_time-st_bridge_record.start_time).total_seconds()
    #         self.st_record_dal.update_st_record(dict(id = st_bridge_record.id),dict(end_time = end_time,
    #                                                                               total_seconds = total_seconds))
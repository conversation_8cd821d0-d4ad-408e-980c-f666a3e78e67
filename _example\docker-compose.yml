services:
  hcc:
    restart : unless-stopped
    image : 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com/cube-ihub-hcc:penta
    command: ["python", "-u", "wsgi.py"]
    ports:
      - "3040:4000"
      - "3042:2525"
      - "3041:4050"
    volumes:
      - ihub-hcc-data:/app/logfile
    env_file:
      - .env.hcc
    container_name: ihub-hcc
    network_mode: bridge

    stdin_open: true
    tty: true

  asrs:
    restart: unless-stopped
    image: 551979302293.dkr.ecr.ap-southeast-1.amazonaws.com/cube-ihub-hcc:asrs
    command: ["python", "-u", "wsgi.py"]
    ports:
      - "3140:4100"
      - "3142:3535"
    volumes:
      - ihub-hcc-data:/app/logfile
    env_file:
      - .env.hcc
    container_name: asrs-ihub-hcc
    network_mode: bridge

volumes:
  ihub-hcc-data:
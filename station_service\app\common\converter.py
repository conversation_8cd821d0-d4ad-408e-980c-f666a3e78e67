from __future__ import annotations

from typing import List, TYPE_CHECKING

from .. import enum_collections as ec

if TYPE_CHECKING:
    from ..adapter import StationAdapter

class MatrixConverter:

    @staticmethod
    def get_matrix_station_code(station_code:int)->int:
        from .. import database as db

        station = db.st_db_func.get_station_by_code(station_code)

        return station.matrix_code

    @staticmethod
    def get_matrix_station_list(matrix_code:int, exclude_self_station_id:int=None)->List[StationAdapter]:
        from .. import database as db

        station_list = db.st_db_func.get_station_list_by_matrix_code(matrix_code)

        if exclude_self_station_id:
            return [station for station in station_list if station.code != exclude_self_station_id]

        return station_list

    @staticmethod
    def get_matrix_st_cluster_index(station_code: int) -> int:
        """
        Ex: ST 7,8,9 has matrix code 2. We trying to get the cluster index of ST 8

        We find lowest station code in this list (7)
        Deduct the lowest with 1 (7-1)
        Then we minus with the cluster index station code we trying to get  ( 8 - 6 )
        We get cluster index 2

        """
        from .. import database as db

        station = db.st_db_func.get_station_by_code(station_code)

        if station.type == ec.CubeStationType.I.value:
            station_cluster = MatrixConverter.get_matrix_station_list(
                station.matrix_code
            )
            station_cluster.sort(key=lambda x: x.code)

            cluster_index = station.code - (station_cluster[0].code - 1)

            return cluster_index
        else:
            return 1
        
    @staticmethod
    def get_station_wms_status(station_code:int):
        """
        A function sepcially made for WMS to get the latest station mode
        [Operation,Not Ready, Enroll, Overweight, Maintenance, Transfer]
        """ 
        station_matrix_code = MatrixConverter.get_matrix_station_code(station_code)
        adj_station_list = MatrixConverter.get_matrix_station_list(station_matrix_code)
        
        if not all(st.is_connected for st in adj_station_list):
            return ec.CubeStationWMSStatus.NOT_READY.value
        if not all(st.is_active for st in adj_station_list):
            return ec.CubeStationWMSStatus.MAINTENANCE.value
        if any(st.is_maintenance for st in adj_station_list):
            return ec.CubeStationWMSStatus.MAINTENANCE.value
        if any(st.is_overweight for st in adj_station_list):
            return ec.CubeStationWMSStatus.OVERWEIGHT.value
        if any(st.mode == ec.CubeStationMode.ENROLL.value for st in adj_station_list):
            return ec.CubeStationWMSStatus.ENROLL.value
        if any(st.mode == ec.CubeStationMode.TRANSFER.value for st in adj_station_list):
            return ec.CubeStationWMSStatus.TRANSFER.value
        
        return ec.CubeStationWMSStatus.OPERATION.value

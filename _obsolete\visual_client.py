# import socketio
# import eventlet
# from config import Config
# from simple_chalk import yellowBright

# sio = socketio.Client()


# def connect_visual():
#     if Config.send_visual is True:
#         connected = False
#         try:
#             url = f'http://{Config.get_dashboard()}'
#             sio.connect(url)
#         except socketio.exceptions.ConnectionError as e:
#             print('Error connecting to visualisation dashboard. Retrying in 5 seconds')
#             eventlet.sleep(5)
#             connect_visual()
#         else:
#             connected = True
#         if connected is True:
#             print('my sid is', sio.sid)
#             return connected


# def init_dashboard():
#     if Config.send_visual is True:
#         data = {}
#         sio.emit('hwx-init', data)


# def send_station(station_id, bridge=False):
#     if Config.send_visual is True:
#         data = {
#             "station_id": station_id,
#             "bridge": bridge
#         }
#         sio.emit('station-update', data)


# def bin_arrival(json):
#     if Config.send_visual is True:
#         sio.emit('bin-arrival', json)
#         print(yellowBright("Broadcasting (client) Bin Arrival: "), json)

from flask import request


from config import Config
from . import enum_collections as ec
from .communications_provider.redis import Redis
from pingspace_python_packages.auth import AuthServices as _AuthServices


class AuthServices(_AuthServices):

    @classmethod
    def initialize_(cls):
        return super().initialize(Config.get_auth_address(), list(Config.get_client_ids().values()))
    
    @classmethod
    def initialize_robust_(cls):
        return super().initialize_robust(Config.get_auth_address(), list(Config.get_client_ids().values()))
    
    @classmethod 
    def initialization(cls):
        from config import Config

        if Config.FLASK_ENV == ec.EnvironmentType.DEVELOPMENT.value:
            cls.initialize_()
        else:
            cls.initialize_robust_()

    @classmethod
    def extract_authorization(cls):
        headers = request.headers
        # referrer = headers.get('REFERER')
        # if referrer and referrer.endswith(ec.CONF.SWAGGER_ROUTE.value):
        #     if auth := headers.get('Authorization'):
        #         return super().extract_authorization(auth)
            
        #     return
        if auth := headers.get('Authorization') or headers.get('X-SWAGGER'):
            return super().extract_authorization(auth)
        return 0
        
    @classmethod
    def _get_user_info_(cls, user_id: int):
        if user_id:
            return super().get_user_info(user_id)
    
    @classmethod
    def get_username(cls, user_id: int):
        if user_info := cls.get_user_info_(user_id):
            return user_info["username"]
        return "UNKNOWN"

    
    get_user_info_ = Redis.register_function(_get_user_info_, lambda *args, **kwargs: None)

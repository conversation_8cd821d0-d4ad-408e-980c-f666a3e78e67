from typing import List, Tuple
from sqlalchemy import desc, Date, cast, asc 

from ... import app,DBMonitor,db
from ... import common as common,models as model,enum_collections as ec

db_log = common.LogManager('cube_db_error',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_log))
class StationMovementSQLAlchemyQueries:
        
    @DBMonitor.retry_session
    def get_grouped_processing_order(self,station_id:int)-> List[model.StationMovement]:
        with app.app_context():
            curr_session = db.session
            not_completed_rows = curr_session.query(model.StationMovement).filter(
                model.StationMovement.status != ec.OrderStatus.COMPLETED.value ,
                model.StationMovement.station_id == station_id).all()
            
            order_ids = set([row.order_id for row in not_completed_rows])
            grouped_rows = curr_session.query(model.StationMovement)\
                .filter(model.StationMovement.order_id.in_(order_ids))\
                .order_by(desc(model.StationMovement.created_at))\
                .all()

            return grouped_rows
            
    @DBMonitor.retry_session
    def find_job(self,filter_dict:dict = dict(),not_filter_dict:dict = dict())->model.StationMovement:
        with app.app_context():
            not_filter = [getattr(model.StationMovement, key) != value for key, value in not_filter_dict.items()]
            curr_session = db.session
            job = curr_session.query(model.StationMovement).filter_by(**filter_dict).filter(*not_filter).first()
            return job
    
    @DBMonitor.retry_session
    def find_job_join_st(self,filter_dict:dict = dict(),not_filter_dict:dict = dict())->model.StationMovement:
        with app.app_context():
            not_filter = [getattr(model.StationMovement, key) != value for key, value in not_filter_dict.items()]
            filter = [getattr(model.StationMovement, key) == value for key, value in filter_dict.items()]
            curr_session = db.session
            job = curr_session.query(model.StationMovement, model.Stations)\
                              .join(model.Stations, (model.StationMovement.station_id == model.Stations.id))\
                              .filter(*filter).filter(*not_filter).first()
            return job
            
    @DBMonitor.retry_session
    def find_job_join_order(self,station_id:int,storage_code:str,type:ec.CubeStationJobType)->Tuple[model.StationMovement,model.StationOrder]:
        with app.app_context():
            curr_session = db.session
            job = curr_session.query(model.StationMovement,model.StationOrder)\
                            .join(model.StationOrder,(model.StationMovement.order_id == model.StationOrder.id))\
                            .filter(model.StationMovement.station_id == station_id,
                                    model.StationMovement.bin_no == storage_code,
                                    model.StationMovement.status != ec.OrderStatus.COMPLETED.value,
                                    model.StationMovement.type == type)\
                            .first()
            return job
        
    @DBMonitor.retry_session
    def find_earliest_job(self,filter_dict:dict)->model.StationMovement:
        with app.app_context():
            curr_session = db.session
            job = curr_session.query(model.StationMovement).filter_by(**filter_dict).order_by(asc('created_at')).first()
            return job
        
    @DBMonitor.retry_session
    def find_latest_job(self,filter_dict:dict)->model.StationMovement:
        with app.app_context():
            curr_session = db.session
            job = curr_session.query(model.StationMovement).filter_by(**filter_dict).order_by(desc('created_at')).first()
            return job
            
    @DBMonitor.retry_session
    def find_jobs(self,filter_dict:dict = dict(),not_filter_dict:dict = dict())->List[model.StationMovement]:
        with app.app_context():
            not_filter = [getattr(model.StationMovement, key) != value for key, value in not_filter_dict.items()]
            curr_session = db.session
            job = curr_session.query(model.StationMovement).filter_by(**filter_dict).filter(*not_filter).order_by(model.StationMovement.created_at).all()
            return job
        
    @DBMonitor.retry_session
    def find_jobs_join_st(self,filter_dict:dict = dict(),not_filter_dict:dict = dict())->List[Tuple[model.StationMovement,model.Stations]]:
        with app.app_context():
            all_filters = []
            for key, value in filter_dict.items():
                all_filters.append(getattr(model.StationMovement, key) == value)
            for key, value in not_filter_dict.items():
                all_filters.append(getattr(model.StationMovement, key) != value)
            curr_session = db.session
            job = curr_session.query(model.StationMovement, model.Stations)\
                              .join(model.Stations, (model.StationMovement.station_id == model.Stations.id))\
                              .filter(*all_filters).order_by(model.StationMovement.created_at).all()
            return job
            
    @DBMonitor.retry_session
    def find_all_recoverable_active_jobs(self,station_id:int, bin_no:str)->List[model.StationMovement]:
        with app.app_context():
            curr_session = db.session
            recoverable_jobs = curr_session.query(model.StationMovement).filter_by(station_id=station_id,bin_no=bin_no).filter(model.StationMovement.status != ec.OrderStatus.COMPLETED.value,model.StationMovement.type != ec.CubeStationJobType.NEXT_BIN.value,model.StationMovement.type != ec.CubeStationJobType.UPDATE_PICK.value).order_by('created_at').all()
            return recoverable_jobs
            
    @DBMonitor.retry_session
    def find_all_recoverable_jobs(self,station_id:int, bin_no:str)->List[model.StationMovement]:
        with app.app_context():
            curr_session = db.session
            recoverable_jobs = curr_session.query(model.StationMovement).filter_by(station_id=station_id,bin_no=bin_no,).filter(model.StationMovement.type != ec.CubeStationJobType.NEXT_BIN.value,model.StationMovement.type != ec.CubeStationJobType.UPDATE_PICK.value).order_by(desc('created_at')).all()
            return recoverable_jobs
            
    @DBMonitor.retry_session
    def find_all_recoverable_active_bridge_jobs(self,station_id:int, bin_no:str):
        with app.app_context():
            curr_session = db.session
            recoverable_jobs = curr_session.query(model.StationMovement).filter_by(station_id=station_id,bin_no=bin_no).filter(model.StationMovement.status != ec.OrderStatus.COMPLETED.value,model.StationMovement.type == ec.CubeStationJobType.BRIDGE.value).order_by('created_at').all()
            return recoverable_jobs
            
    @DBMonitor.retry_session
    def find_jobs_by_date(self,station_id:int,date)->List[model.StationMovement]:
        with app.app_context():
            curr_session = db.session
            temp = curr_session.query(model.StationMovement).filter_by(station_id = station_id,type = ec.CubeStationJobType.MOVE_TO_WORK.value, status = ec.OrderStatus.COMPLETED.value).filter(cast(model.StationMovement.created_at,Date)== date).order_by('created_at').all()
            return temp

    @DBMonitor.retry_session
    def create_station_job(self,station_id:int,type:ec.CubeStationJobType,order_id:int,from_index:int,to_index:int,bin_no:str,status:ec.OrderStatus,plc_ack:bool=False,pred_id:int=None)->None:
        with app.app_context():
            station_job = model.StationMovement(
                station_id=station_id,
                type=type,
                order_id = order_id,
                from_index=from_index,
                to_index=to_index,
                bin_no=bin_no,
                status=status,
                plc_ack = plc_ack,
                pred_id=pred_id
            )
            curr_session = db.session
            curr_session.add(station_job)
            curr_session.commit()

            job_id = station_job.id

            return job_id
        
    @DBMonitor.retry_session
    def update_job(self,job_id:int,update:dict)->int:
        with app.app_context():
            curr_session = db.session
            job:model.StationMovement = curr_session.query(model.StationMovement).filter(model.StationMovement.id == job_id).first()
            if job:
                for k,v in update.items():
                    job.__setattr__(k,v)
                # job.updated_at = datetime.datetime.now()
                    job_id = job.id
                curr_session.commit()
                return job_id
            else:
                db_log.error(f'No job with id {job_id} found')
                return None
        
    @DBMonitor.retry_session
    def get_station_movement(self,station_id:int, order_id_list:List[int])->List[model.StationMovement]:
        with app.app_context():
            curr_session = db.session
            db_data = curr_session.query(model.StationMovement).filter(
                    # model.StationMovement.status != ec.OrderStatus.COMPLETED.value,
                    model.StationMovement.station_id == station_id
                ).order_by(model.StationMovement.created_at).all()
            return db_data

    @DBMonitor.retry_session
    def delete_job(self,job_id:int):
        with app.app_context():
            curr_session = db.session
            curr_session.query(model.StationMovement).filter_by(id = job_id).delete()
            curr_session.commit()
                
    @DBMonitor.retry_session    
    def clear_jobs_with_order_id(self,order_id:int)->List[int]:
        with app.app_context():
            curr_session = db.session
            deleted_job = curr_session.query(model.StationMovement).filter_by(order_id = order_id).all()
            deleted_job_id = [job.id for job in deleted_job]
            curr_session.query(model.StationMovement).filter_by(order_id = order_id).delete()
            curr_session.commit()

            return deleted_job_id

    @DBMonitor.retry_session
    def find_jobs_for_healthcheck(self, threshold_time) -> List[model.StationMovement]:
        """
        Find all processing jobs that are older than threshold_time, joined with their orders and stations
        """
        with app.app_context():
            curr_session = db.session
            jobs = curr_session.query(model.StationMovement)\
                              .filter(model.StationMovement.status == ec.OrderStatus.PROCESSING.value,
                                     model.StationMovement.updated_at < threshold_time)\
                              .order_by(model.StationMovement.updated_at.asc())\
                              .all()
            return jobs

    
# Obsolete function 
    
# def manual_override_station_jobs(station_id):
#     try:
#         start_time = time.time()
#         curr_session = db.session
#         curr_session.query(model.StationMovement).filter(model.StationMovement.status != ec.OrderStatus.COMPLETED.value, model.StationMovement.station_id == station_id).update({"status": ec.OrderStatus.COMPLETED.value})
#         curr_session.commit()
#         end_time = time.time()
#         metrics.register_func_average_time(start_time,end_time,sys._getframe().f_code.co_name)
#     except Exception as e:
#         db_log.error(redBright(f'manual_override_station_jobs error. Exception thrown: {e}'))
#         metrics.register_func_error(sys._getframe().f_code.co_name)

# def recover_job_by_time_6cell(station_id):
#     try:
#         start_time = time.time()
#         curr_session = db.session
#         with app.app_context():
#             job = curr_session.query(model.StationMovement).filter_by(station_id=station_id, from_index = 3).filter(model.StationMovement.status !=
#                                                                                                ec.OrderStatus.COMPLETED.value).order_by('created_at').first()
#             end_time = time.time()
#             metrics.register_func_average_time(start_time,end_time,sys._getframe().f_code.co_name)
#             return job
#     except Exception as e:
#         db_log.error(redBright(f'recover_job_by_time error. Exception thrown: {e}'))
#         metrics.register_func_error(sys._getframe().f_code.co_name)


# def complete_next_bin_job(bin_no:str, station_id:int):
#     try:
#         start_time = time.time()
#         curr_session = db.session
#         job = curr_session.query(model.StationMovement).filter_by(bin_no=bin_no, type = ec.CubeStationJobType.NEXT_BIN.value, station_id=station_id).filter(
#             model.StationMovement.status != ec.OrderStatus.COMPLETED.value).first()
#         if job: 
#             job.status = ec.OrderStatus.COMPLETED.value
#             job.updated_at = datetime.datetime.now()
#             curr_session.commit()
#             end_time = time.time()
#             metrics.register_func_average_time(start_time,end_time,sys._getframe().f_code.co_name)
#             return job
#         else:
#             error_msg = f"Could not find next_bin job with bin_no : {bin_no}, station_id: {station_id}"
#             db_log.error(redBright(f'get_next_bin_job error. {error_msg}'))
#             return None
#     except Exception as e:
#         db_log.error(redBright(f'get_next_bin_job error. Exception thrown: {e}'))
#         metrics.register_func_error(sys._getframe().f_code.co_name)

# def check_completed(id):
#     try:
#         start_time = time.time()
#         curr_session = db.session
#         with app.app_context():
#             job = curr_session.query(model.StationMovement.status).filter(model.StationMovement.id == id).first()
#             end_time = time.time()
#             metrics.register_func_average_time(start_time,end_time,sys._getframe().f_code.co_name)
#             if job.status == ec.OrderStatus.COMPLETED.value:
#                 return True
#             else:
#                 return False
#     except Exception as e:
#         db_log.error(redBright(f'check_completed error. Exception thrown: {e}'))
#         metrics.register_func_error(sys._getframe().f_code.co_name)

# def find_bin_processing_job(bin_id, type = None):
#     try:
#         start_time = time.time()
#         curr_session = db.session
#         if type:
#             job = curr_session.query(model.StationMovement).filter_by(bin_no= bin_id,type = type, status = ec.OrderStatus.PROCESSING.value).first()
#         else:
#             job = curr_session.query(model.StationMovement).filter_by(bin_no = bin_id, status = ec.OrderStatus.PROCESSING.value).first()
#         end_time = time.time()
#         metrics.register_func_average_time(start_time,end_time,sys._getframe().f_code.co_name)
#         return job
#     except Exception as e:
#         db_log.error(redBright(f'find_bin_processing_job error. Exception thrown: {e}'))
#         metrics.register_func_error(sys._getframe().f_code.co_name)
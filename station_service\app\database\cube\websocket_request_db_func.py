import copy

from typing import List

from ... import common as common,models as model,enum_collections as ec

from .websocket_request_dal import WebSocketReqSQLAlchemyQueries,app

db_func_log = common.LogManager('cube_db_func',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_func_log))
class WebSocketReqDBFunc:
    
    websocket_req_dal = WebSocketReqSQLAlchemyQueries()

    def get_available_requests(self)->List[model.WebsocketRequests]:
        return self.websocket_req_dal.get_available_requests()
    
    def get_request(self,req_id:int)->model.WebsocketRequests:
        return self.websocket_req_dal.get_request(req_id)

    def add_ws_request(self,entity, data, event, namespace='/station'):
        from ...communications_provider.socketio.cube import SioBroadcast<PERSON>and<PERSON>
        with app.app_context():
            instance = self.websocket_req_dal.create_ws_request(entity,copy.deepcopy(data),event,namespace)
            SioBroadcastHandler(instance)

    def complete_ws_request(self,id:int):
        self.websocket_req_dal.update_ws_request_status(id,ec.OrderStatus.COMPLETED.value)

    def update_req(self,id:int,update:dict):
        self.websocket_req_dal.update_ws_request(id,update)

    def update_ws_request_status(self,id:int,status:str):
         self.websocket_req_dal.update_ws_request_status(id,status)
    
    def update_ws_request_retry(self,id:int):
        request = self.websocket_req_dal.find_ws_request(id)
        self.websocket_req_dal.update_ws_request_status_retry(request.id,request.retry+1)
import datetime

from typing import List

from ... import models as model,common as common
from .station_errors_dal import StationErrorSQLAlchemyQueries


db_func_log = common.LogManager('cube_db_func',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_func_log))
class StationErrorDBFunction:
    
    station_error_dal = StationErrorSQLAlchemyQueries()
    
    def add_station_error_message(self,station_code:int,error_name:str)->int:
        return self.station_error_dal.create_station_error_message(station_code,error_name)
    
    def check_exisitng_station_error_msg(self,station_code:int,error_name:str)->bool:
        existed_station_error_msg = self.station_error_dal.find_station_err_msg(dict(station_code = station_code,error_name = error_name,resolved = False))
        return True if existed_station_error_msg else False
    
    def get_existing_station_error_msg_list(self,station_code:int)->List[model.StationErrors]:
        station_error_msg_list = self.station_error_dal.find_station_err_msges(dict(station_code = station_code,resolved = False))
        return station_error_msg_list

    def update_station_error_msg_status(self,msg_id:int):
        self.station_error_dal.update_station_err_msg(msg_id,dict(resolved = True))
           
    def update_station_error_msg_payload(self,id:int,new_data):
        self.station_error_dal.update_station_error_msg_payload(id,new_data)

       
    # for tc-dashboard use
    def get_station_error_code(self,station_code:int)->List[int]:
        station_err_msg_list =  self.station_error_dal.find_distinct_station_error(station_code)
        return [error_msg.error_name for error_msg in station_err_msg_list]
 
    def get_station_error_details(self,station_code:int)->list:
        """
            { text: "Error Code",value: "errorCode"},
            { text: "Module", value: "module"},
            { text: "Error Message", value: "errorMessage"},
            { text: "Action", value: "action"},
        """
        station_err_msg_list = self.station_error_dal.find_distinct_station_error(station_code)
        return_data = []
        for msg in station_err_msg_list:
            msg:model.StationErrors
            return_data.append(msg.as_dict_for_record())                
        return return_data
    
    # for analysis use
    def get_station_error_by_date(self,station_code:int,from_date:datetime.datetime,to_date:datetime.datetime):
        return self.station_error_dal.find_station_error_on_date(station_code,from_date,to_date)
from __future__ import annotations

import asyncio
import aiohttp

from datetime import datetime
from termcolor import colored
from threading import current_thread
from dataclasses import dataclass, field
from typing import ClassVar, Dict, Callable, List, Iterable, Any,TypedDict


from . import database as db,models as model,common as common,enum_collections as ec
from app import DBMonitor,app,db as app_db


http_log = common.LogManager('cube_http_post',centralize_logging=True)


class HttpResponse(TypedDict):
    data: Any
    status_code: ec.HTTPStatus

@dataclass
class InternalHttpResponse:
    json: dict
    message: str = ''
    clientResponse: aiohttp.ClientResponse = None

    @property
    def is_success(self)->bool:
        if not self.clientResponse:
            return False
        if self.clientResponse.status != 200:
            return False
        return True

    @classmethod
    def exception(cls, json: dict, e: Exception):
        return cls(json, str(e.args))


@dataclass
class HttpRequest:
    HTTP_RETRY_MAX_ATTEMPT = 50
    CLEAN_UP_INTERVAL = 1 * 24 * 60 * 60 # in seconds
    CLEAN_UP_HISTORY = 7 # in days
    TIME_OUT_OBJ = aiohttp.ClientTimeout(total=10)
    
    event_loop = asyncio.new_event_loop()
    queue: ClassVar[asyncio.Queue["HttpRequest"]] = asyncio.Queue()
    hash: ClassVar[Dict[int, HttpRequest]] = dict()


    method: str
    url: str
    json: Dict = field(default_factory=dict)
    callback_pred: Callable[[], List[int]] = lambda: []
    headers: Dict = field(default_factory=dict)
    pred_ids: List[int] = field(default_factory=list)
    succ_id: int = 0
    id: int = 0
    attempts :int = 0
    reference: Dict = field(default_factory=dict)
    is_completed: asyncio.Future = None

    def __post_init__(self):
        if not self.headers :
            self.headers = {}
        self.is_completed = self.event_loop.create_future()
        self.event_loop.call_soon_threadsafe(
            self.queue.put_nowait,
            self
        )

    async def emit(self):
        await asyncio.gather(*self.dependency)
        while not self.is_completed.done():

            # if self.attempts > self.HTTP_RETRY_MAX_ATTEMPT:
            #     self.post_time_outed()
            #     print(common.formatString("ERROR",f'Post request {self.id} has time outed, please check the error'))
            data = self.as_dict()

            res  = await self.send_request()
            if not res.is_success:
                self.update_post_attempt()
                http_log.info(f'Sent - {data} : '+f'{res}')
                print(common.formatString("ERROR",f'Sent - {data} \n Res - {res}'))

                await asyncio.sleep(3)
                continue
            
            http_log.info(f'Sent {data} , {res}')
            print(common.formatString("INFO",colored(f'Sent - {data} : {res.clientResponse.status}','blue')))
            self.complete_post(HttpResponse(
                data = res.json, 
                status_code = res.clientResponse.status
            ))

    @property
    def dependency(self):
        return (
            pred.is_completed
            for id in self.pred_ids
            if (pred := self.get_req_by_id(id))
        )
    
    @classmethod
    def loop(cls):
        current_thread().setName('http_request')
        cls.event_loop.set_exception_handler(cls.exception_handler)
        asyncio.set_event_loop(cls.event_loop)

        cls.reinitialization(False)
        for coro in (
            cls.process(),
            # cls.rotate_clean_up()
        ):
            cls.event_loop.create_task(coro)

        cls.event_loop.run_forever()

    @classmethod
    def exception_handler(cls, loop, context: Dict):
        http_log.error(str(context))
        context['created_at'] = datetime.now()
        print(common.formatString('ERROR',f'Asyncio Exception Handler {context}'))
    
    @classmethod
    def reinitialization(cls, reset=True):

        def _task():
            if reset:
                while not cls.queue.empty():
                    cls.queue.get_nowait()

            for req in cls.hash.copy().values():
                cls.remove_request(req)
         
            posts: Iterable[model.HttpRequests] = db.http_req_db_func.find_requests(dict(status=ec.OrderStatus.PROCESSING.value))
            for post in posts:
                req = cls(**post.as_dict())
                req.create_post = lambda: True

        cls.event_loop.call_soon_threadsafe(_task)
    
    @classmethod
    async def process(cls):
        while True:
            req = await cls.queue.get()
            if not req.create_post():
                continue

            cls.add_request(req)
            cls.event_loop.create_task(
                req.emit()
            )

    @common.async_log_and_suppress_error(http_log)
    async def send_request(self)->InternalHttpResponse:
        try:
            async with aiohttp.ClientSession(timeout = self.TIME_OUT_OBJ) as session:
                async with session.post(self.url, json=self.json, headers=self.headers) as response:
                    json =  await response.json()

                    return InternalHttpResponse(json,'',response)
                
        except aiohttp.ClientResponseError as e:
            # 404,409,429 and more to go here, no specific retry logic for each error code for now, retry feature shall compliant to standard retry with (n) interval delay and (m) max attempt
            return InternalHttpResponse(locals(), e.message, response)

        except aiohttp.ClientConnectorError as e :
            return InternalHttpResponse(locals(), e.strerror)

        except Exception as e:
            return InternalHttpResponse.exception(locals(), e)
            
    def create_post(self):
        duplicated = db.http_req_db_func.find_request(dict(
            method = self.method,
            url = self.url,
            json = self.json,
            headers = self.headers,
            status = ec.OrderStatus.PROCESSING.value
        ))
        if duplicated:
            return False
        self.pred_ids = self.callback_pred()
        post_id = db.http_req_db_func.create_request(self.as_dict())
        self.id = post_id
        return True
    
    def update_post(self,update:dict):
        for attr,value in update.items():
            setattr(self,attr,value)
        db.http_req_db_func.update_request(self.id,update)

    def update_post_attempt(self):
        self.attempts += 1
        db.http_req_db_func.update_request(self.id,dict(
            attempts = self.attempts)
        )
    
    def complete_post(self, res: HttpResponse):
        db.http_req_db_func.update_request(self.id,dict(
            status = ec.OrderStatus.COMPLETED.value,
            response = res)
        )
        self.remove_request(self)

    def post_time_outed(self):
        db.http_req_db_func.update_request(self.id,dict(
            status = ec.OrderStatus.TIMED_OUT.value,
        ))
        self.remove_request(self)

    def soft_delete_post(self):
        db.http_req_db_func.update_request(self.id,dict(
            status = ec.OrderStatus.DELETED.value,
        ))
        self.remove_request(self)

    @classmethod
    def add_request(cls, req: HttpRequest):
        cls.hash[req.id] = req

    @classmethod
    def remove_request(cls, req: HttpRequest):
        if not req.is_completed.done():
            req.is_completed.set_result(True)
        cls.hash.pop(req.id, None)

    @classmethod
    def update_request(cls,id:int,update:dict):
        if req := cls.get_req_by_id(id):
            req.update_post(update)
        return req.as_dict()

    @classmethod
    def soft_delete_post_by_id(cls, id: int):
        if req := cls.get_req_by_id(id):
            req.soft_delete_post()

    @classmethod
    def get_req_by_id(cls, id: int):
        return cls.hash.get(id)
    
    @classmethod
    def get_pred_st_status(cls, matrix_st_code:int ,url: str):
        @DBMonitor.retry_session
        def _callback():
            filter = {'matrix_code' : f'{matrix_st_code}'}
            with app.app_context():
                curr_session = app_db.session
                post: model.HttpRequests = curr_session.query(model.HttpRequests).filter(
                        model.HttpRequests.url == url,
                        model.HttpRequests.json['data'].contains(filter),
                        model.HttpRequests.status == ec.OrderStatus.PROCESSING.value
                    ).order_by(model.HttpRequests.created_at.desc()).first()
                    
                if post:
                    return [post.id]
                return []

        return _callback
    
    @classmethod
    def get_pred_st_error(cls, matrix_st_code:int ,url: str):
        @DBMonitor.retry_session
        def _callback():
            filter = {'matrix_code' : f'{matrix_st_code}'}
            with app.app_context():
                curr_session = app_db.session
                post: model.HttpRequests = curr_session.query(model.HttpRequests).filter(
                        model.HttpRequests.url == url,
                        model.HttpRequests.json['payload'].contains(filter),
                        model.HttpRequests.status == ec.OrderStatus.PROCESSING.value
                    ).order_by(model.HttpRequests.created_at.desc()).first()
                    
                if post:
                    return [post.id]
                return []

        return _callback
    
    def as_dict(self):
        return dict(
            method = self.method,
            url = self.url,
            json = self.json,
            pred_ids = self.pred_ids,
            headers = self.headers,
            reference = self.reference
        )
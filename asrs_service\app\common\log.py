import logging,sys, os

from pathlib import Path
from datetime import datetime
from logging.handlers import TimedRotatingFileHandler

TIME_FORMAT_DAY = "%Y-%m-%d"
TIME_FORMAT_HOUR = "%Y-%m-%d-%H"
TIME_FORMAT_MINUTE = "%Y-%m-%d-%H-%M"
TIME_FORMAT_SECOND = "%Y-%m-%d-%H-%M-%S"

ROTATE_METHOD = {
    'S' : TIME_FORMAT_SECOND,
    'M' : TIME_FORMAT_MINUTE,
    'H' : TIME_FORMAT_HOUR,
    'D' : TIME_FORMAT_DAY,
    'MIDNIGHT' : TIME_FORMAT_DAY
}

FORMATTER = logging.Formatter("%(asctime)s.%(msecs)03d - %(name)s - [%(levelname)s] - %(message)s -  %(funcName)s", datefmt='%Y-%m-%d %H:%M:%S')
FORMATTER_console = logging.Formatter(fmt="{asctime} - {levelname} - {message}", datefmt='%H:%M:%S', style='{')

rotate_method = 'MIDNIGHT'   # can be s=second, m=minute, h=hour d/midnight = daily

class PathConfiguration:
    DIR_LEVEL_UP = 2
    LOG_FILE_WITHIN_REPO_STORE_DIRECTORY_ = f'{os.getcwd()}/logfile/'

    @classmethod
    def get_directory(cls)->Path:
        directory_path = Path(cls.LOG_FILE_WITHIN_REPO_STORE_DIRECTORY_)
        return directory_path
    
    @classmethod
    def get_directory_str(cls)->str:
        return os.path.abspath(cls.get_directory())

    @classmethod
    def get_directory_with_dt_folder(cls):
        ''' Return Path object 
        e.g os_to_app_full_path/CUBE-2023-08-21/ '''
        cls.extStyle = ROTATE_METHOD[rotate_method]
        p = Path(cls.get_directory(), log_file_name(cls.extStyle))
        return p

class CustomTimedRotatingFolderHandler(TimedRotatingFileHandler):
    def __init__(self, log_title, whenTo=rotate_method, intervals=1):
        self.when = whenTo.upper()
        self.inter = intervals
        self.log_file_path = PathConfiguration.get_directory_str()
        if not os.path.isdir(self.log_file_path):
            os.mkdir(self.log_file_path)

        self.extStyle = ROTATE_METHOD[rotate_method]

        full_path_with_custome_dt_folder  = Path(self.log_file_path,log_file_name(self.extStyle))
        self.dir_with_dt_folder : str = os.path.abspath(full_path_with_custome_dt_folder)

        if not os.path.isdir(self.dir_with_dt_folder):
            os.mkdir(self.dir_with_dt_folder)

        self.title = log_title
        filename = os.path.join(self.dir_with_dt_folder, self.title)
        TimedRotatingFileHandler.__init__(self, filename, when=self.when, interval=self.inter, backupCount=0, encoding=None)
        self._header = ""
        self._log = None
        self._counter = 0

    def doRollover(self):
        """
        TimedRotatingFileHandler override - rotates log folders on daily basis
        """
        self.stream.close()
        # t = self.rolloverAt - self.interval
        full_path_with_custome_dt_folder  = Path(
            self.log_file_path, 
            log_file_name(self.extStyle))
        self.new_dir : str = os.path.abspath(full_path_with_custome_dt_folder)

        if not os.path.isdir(self.new_dir):
            os.mkdir(self.new_dir)
            
        self.baseFilename = os.path.abspath(os.path.join(self.new_dir, self.title.name))
        self.stream = open(self.baseFilename, "w", encoding=self.encoding)
        self.rolloverAt = self.rolloverAt + self.interval

def detectExists(pathDir):
    if not(os.path.exists(pathDir)):
        os.makedirs(pathDir)
        print("Log file directory does not exist, creation of folder completed.")

def get_console_handler():
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(FORMATTER_console)
    return console_handler

def get_file_handler(filename):
    LOG_FILE_PATH = PathConfiguration.get_directory_with_dt_folder()
        
    file_handler = CustomTimedRotatingFolderHandler(Path(LOG_FILE_PATH, f'{filename}.log'))
    file_handler.setFormatter(FORMATTER)
    return file_handler

def get_logger(logger_name):
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.DEBUG)
    
    if (logger.hasHandlers()):
        logger.handlers.clear()

    # logger.addHandler(get_console_handler())
    logger.addHandler(get_file_handler(logger_name))
    return logger

def get_logger_console(logger_name):
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.DEBUG)

    if (logger.hasHandlers()):
        logger.handlers.clear()

    logger.addHandler(get_console_handler())
    logger.addHandler(get_file_handler(logger_name))
    # logger.addHandler(logging.StreamHandler())
    return logger

def log_file_name(extStyle=TIME_FORMAT_DAY):
    return  f'ASRS-' + datetime.today().strftime(extStyle) + "/"

detectExists(PathConfiguration.get_directory())
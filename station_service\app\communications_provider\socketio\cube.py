from __future__ import annotations

import asyncio
import datetime

from socketio import AsyncNamespace
from termcolor import cprint,colored
from typing import TYPE_CHECKING,ClassVar, Dict, List, Iterable

from config import Config
from ... import database as db,models as model,common as common,enum_collections as ec
from ...communications_provider import socketio as socketio 

if TYPE_CHECKING:
    from ...adapter import StationAdapter

last_log = dict()

ws_heartbeat_log = common.LogManager('cube_ws_heartbeat',centralize_logging=True)
websocket_clients = {
        ec.TcSubEntities.TC_A.value: None,
        ec.TcSubEntities.TC_B.value: None,
        ec.TcSubEntities.TC_C.value: None,
        ec.ExternalEntities.SM.value: None
        }

class SioBroadcastHandler:
    SIO_EMIT_RETRY_MAX_ATTEMPT = 50
    
    event_loop = None
    queue: ClassVar[asyncio.Queue["SioBroadcastHandler"]] = None
    hash: ClassVar[Dict[int, SioBroadcast<PERSON>andler]] = None

    def __init__(self,sio_req:model.WebsocketRequests):
        self.id = sio_req.id
        self.entity = sio_req.entity
        self.data = sio_req.data
        self.event = sio_req.event
        self.namespace = sio_req.namespace
        self.retry = sio_req.retry
        self.pred_ids: List[int] = []
        self.room = websocket_clients[self.entity]
        self.retry_interval = 3 if self.entity == 'SM' else 1
        if self.event == 'work_arrival':
            self.callback = work_arrival_callback
        else:
            self.callback = ack_ws_request
        if 'data' in self.data:
            self.data['data']['request_id'] = self.id
        else:
            self.data['request_id'] = self.id
        self.is_completed = self.event_loop.create_future()
        self.event_loop.call_soon_threadsafe(
            self.queue.put_nowait,
            self
        )
    
    @property
    def dependency(self):
        return (
            pred.is_completed
            for id in self.pred_ids
            if (pred := self.get_req_by_id(id))
        )

    @classmethod
    def initialization(cls,loop):
        cls.event_loop = loop
        cls.queue = asyncio.Queue()
        cls.hash = dict()
        
    @classmethod
    async def loop(cls):

        cls.reinitialization(False)
        for coro in (
            cls.process(),
        ):
            cls.event_loop.create_task(coro)


    @classmethod
    async def process(cls):
        while True:
            try:
                req = await cls.queue.get()
                req.start_processing()
                cls.add_request(req)
                cls.event_loop.create_task(
                    req.emit()
                )
            except asyncio.TimeoutError:
                socketio.ws_server_log.error(f'socket io broadcast process loop has timeout error')
            except Exception as e:
                socketio.ws_server_log.error(f'socket io broadcast process loop raise Exception: {e}')

    @classmethod
    def reinitialization(cls, reset=True):

        def _task():
            if reset:
                while not cls.queue.empty():
                    cls.queue.get_nowait()

            for req in cls.hash.copy().values():
                cls.remove_request(req)
         
            sio_reqs: Iterable[model.WebsocketRequests] = db.ws_request_db_func.get_available_requests()
            for sio_req in sio_reqs:
                cls(sio_req)

        cls.event_loop.call_soon_threadsafe(_task)

    @classmethod
    def add_request(cls, req: SioBroadcastHandler):
        cls.hash[req.id] = req

    @classmethod
    def update_request(cls,req_id:int,update:dict):
        if req := cls.get_req_by_id(req_id):
            req.update_req(update)

    @classmethod
    def complete_request(cls,req_id:int):
        if req := cls.get_req_by_id(req_id):
            req.set_complete()

    @classmethod
    def soft_delete_req_by_id(cls, id: int):
        if req := cls.get_req_by_id(id):
            req.soft_delete_req()

    @classmethod
    def get_req_by_id(cls, id: int):
        return cls.hash.get(id)
    
    @classmethod
    def remove_request(cls, req: SioBroadcastHandler):
        if not req.is_completed.done():
            req.is_completed.set_result(True)
        cls.hash.pop(req.id, None)

    async def emit(self):
        await asyncio.gather(*self.dependency)
        while not self.is_completed.done():
            if self.retry > self.SIO_EMIT_RETRY_MAX_ATTEMPT:
                self.over_max_retry()
            elif self.entity is None or not self.entity in websocket_clients.keys():
                socketio.ws_server_log.error(f'WS request with invalid entity {self.entity}, please check db, id:{self.id}')
            elif websocket_clients[self.entity] is None:
                socketio.ws_server_log.error(f'WS request client not connected or not initialzie yet: {self.entity}')
            else:
                await self.emit_req()
            self.update_req_attempt()
            await asyncio.sleep(self.retry_interval)
            continue
    
    async def emit_req(self):
        try:
            await socketio.sio.emit(
                self.event,
                data=self.data,
                room=self.room,
                namespace=self.namespace,
                callback=self.callback
                )
            if self.event != 'weight_changed': #do not log weight-changed will pollute the log 
                if self.entity is None:
                    socketio.ws_server_log.info(colored(f'Sent | {self.event}','light_magenta') + f' - {self.data}')
                else:
                    socketio.ws_server_log.info(colored(f'Sent | {self.event} to {self.entity}','light_magenta') + f' - {self.data}')
            else:
                print(common.formatString('INFO',colored(f'Sent | {self.event} - {self.data}','light_magenta')))
        except Exception as e:
            socketio.ws_server_log.error(f"Failed to emit socketio : {e}")
        
    def start_processing(self):
        # temporarily no need set predecessor 
        # self.pred_ids = self.find_pred()
        self.update_req(dict(status=ec.OrderStatus.PROCESSING.value))

    def set_complete(self):
        self.update_req(dict(status=ec.OrderStatus.COMPLETED.value))
        self.remove_request(self)
    
    def over_max_retry(self):
        self.update_req(dict(status=ec.OrderStatus.TIMED_OUT.value))
        self.remove_request(self)
        socketio.ws_server_log.error(f'Req ID {self.id} has already time out. Please check the connection between HCC and {self.entity}')

    def soft_delete_req(self):
        self.update_req(dict(status=ec.OrderStatus.DELETED.value))
        self.remove_request(self)
        socketio.ws_server_log.error(f'Req ID {self.id} has been deleted.')

    def update_req(self,update:dict):
        for attr,value in update.items():
            setattr(self,attr,value)
        db.ws_request_db_func.update_req(self.id,update)
    
    def update_req_attempt(self):
        self.retry += 1
        db.ws_request_db_func.update_ws_request_retry(self.id)
        
@common.log_and_suppress_error(socketio.ws_server_log)
def ack_ws_request(data=None):
    if data != None:
        id = int(data['request_id'])
        
        socketio.ws_server_log.info(f'Received | websocket CALLBACK - request id: {id}',color='light_green')
        
        SioBroadcastHandler.complete_request(id)

@common.log_and_suppress_error(socketio.ws_server_log)
def work_arrival_callback(data=None):
    if data != None:
        station_code = int(data['station_code'])
        handle_at_station = data['handle_at_station']
        req_id = data['request_id']
        bin_code = str(data['storage_code'])
        
        socketio.ws_server_log.info(f'Received | work_arrival CALLBACK - request id: {req_id}. data: {data}',color='light_green')
        
        station = db.st_db_func.get_station_by_code(station_code)
        if station.type == ec.CubeStationType.LARGE.value and station.adjacent and handle_at_station:
            order = db.st_order_db_func.find_active_order(station_code,bin_code)
            next_bin_job = db.st_mov_db_func.find_job(dict(type = ec.CubeStationJobType.NEXT_BIN.value,
                                                                  order_id = order.id))
            if next_bin_job: # Prevent unable to find job when sm 
                db.st_db_func.update_station(station_code,dict(bin_at_worker = bin_code))
                pred_job = db.st_mov_db_func.find_job(dict(id=next_bin_job.pred_id))
                if pred_job.type == ec.CubeStationJobType.BRIDGE.value: # only if is bridge job need to control open door 
                    db.st_mov_db_func.inject_door_open(next_bin_job,pred_job) #If it is already door open, ignore create
                    
        SioBroadcastHandler.complete_request(req_id)

@common.log_and_suppress_error(socketio.ws_server_log)
def ack_emo_event(data=None):
    from ...service_handler.cube_handler import send_tcp_msg

    socketio.ws_server_log.info(f'Received | emo CALLBACK. data: {data}',color='light_green')

    if data:
        status = data['status']
        emo_id = int(data['emo_id'])

        if status:
            send_tcp_msg(emo_id,common.message_formatter.EMOMsgFromatter.ack_emo_on_msg(emo_id),target_module=ec.ModuleCode.EMO.value)
        else:
            send_tcp_msg(emo_id,common.message_formatter.EMOMsgFromatter.ack_emo_off_msg(emo_id),target_module=ec.ModuleCode.EMO.value)


@common.decorateAllFunctionInClass(common.log_and_suppress_error(socketio.ws_server_log))
class CubeWSEmit:
    
    # def update_status(bin, station_code):
        
    #     statusReply = common.JsonPayload.jsonFormat({"station_code": int(station_code), "bins": bin}, "")
    #     socketio.emit_ws_iso_event("status", data = statusReply, broadcast=True, namespace='/station')
    
    def emit_active_station(client=None):

        from ...runtime import runtime
        
        disconnect_threshold_list_copy = runtime.disconnect_threshold_list
        
        stationList = db.st_db_func.get_active_station_code_list()
        for station_code in disconnect_threshold_list_copy.keys():
            stationList.append(station_code)
        stationReply = common.JsonPayload.activeConnectionPayload(stationList)

        socketio.SocketIOServer.emit_ws_event('active_connections', data = stationReply, room = client, namespace='/station')
        if Config.SUPPORT_V2:
            socketio.SocketIOServer.emit_ws_event('active-connections', data = stationReply, room = client, namespace='/station')

    def emit_recovery_status(station_code:int,status:bool):
        recovery_status_payload = common.JsonPayload.recoveryStatusPayload(station_code,status)
        socketio.SocketIOServer.emit_ws_event('recovery_status',data=recovery_status_payload)
        
    def emit_job_done_event(job:model.StationMovement,station_code:int,storage_code:int,station:StationAdapter):
        
        station = db.st_db_func.get_station_by_code(station_code)

        if job.type in [ec.CubeStationJobType.MOVE_TO_WORK.value,ec.CubeStationJobType.BRIDGE.value, ec.CubeStationJobType.LIGHT_UP.value]:  # If it is arriving at workpoint, inform Mediator and SM
            if station.type != ec.CubeStationType.I.value or (station.type == ec.CubeStationType.I.value and job.type == ec.CubeStationJobType.LIGHT_UP.value):
                work_arrival_data = common.JsonPayload.workArrivalPayload(station_code,storage_code,datetime.datetime.now(tz=datetime.timezone.utc).isoformat())
                
                # TC
                tc_entity = determine_tc_entity(station_code)
                db.ws_request_db_func.add_ws_request(tc_entity, work_arrival_data, 'work_arrival')     
                            
                if not Config.TC_DRY_RUN_MODE:
                    # SM
                    if Config.SUPPORT_V2:
                        work_arrival_data = {
                                        "station_code": station_code,
                                        "station_id": station_code,
                                        "bin_code": str(storage_code),
                                        "storage_code": storage_code
                                        }
                    db.ws_request_db_func.add_ws_request(ec.ExternalEntities.SM.value, work_arrival_data, 'work_arrival')                                

                
                common.notify_wcs(f"Bin {storage_code} arrived at work point of ST{station_code}.")
                

        elif job.type == ec.CubeStationJobType.MOVE_TO_JUNCTION.value:
            if station.type == ec.CubeStationType.QC.value:
                junction_arrival_data = common.JsonPayload.junctionArrivalPayload(int(station_code),int(storage_code),False)
                if not Config.TC_DRY_RUN_MODE:
                    # SM
                    db.ws_request_db_func.add_ws_request(ec.ExternalEntities.SM.value,junction_arrival_data,'junction_arrival')
        
        elif job.type == ec.CubeStationJobType.MOVE_TO_PICK.value:

            pick_arrival_data = common.JsonPayload.pickArrivalPayload(station_code,
                                                                        int(job.bin_no),
                                                                        job.to_index
                                                                        )
            
            if not Config.TC_DRY_RUN_MODE:
                # SM
                db.ws_request_db_func.add_ws_request(ec.ExternalEntities.SM.value,pick_arrival_data, 'pick_arrival')
            common.notify_wcs(f"Bin {storage_code} arrived at pick point of ST{station_code}.")
        
        else:
            common.notify_wcs(f"Bin {storage_code} move from index {job.from_index} to {job.to_index} of ST{station_code}.")

    def emit_staiton_request_event(station_code:int,tc_job_id:int,action:str,approved:bool,reason:str,is_cancelled:bool = False):
        
        station = db.st_db_func.get_station_by_code(station_code)
        reply_request_data = common.JsonPayload.dp_request_respond(station_code,tc_job_id,approved,reason,is_cancelled = is_cancelled)

        if station.type != ec.CubeStationType.I.value:
            if approved:
                socketio.SocketIOServer.emit_ws_event('request', data = reply_request_data, namespace='/station')
            else:
                socketio.ws_server_log.warning(f"ST{station_code} reject dropping, will not send to TC cause will be handled soon.")
        else:
            tc_entity = determine_tc_entity(station_code)
            if approved:
                req : model.StationGatewayReq = db.st_gw_req_db_func.get_latest_gateway_req_by_job_id(tc_job_id)
                if req.status == ec.OrderStatus.CANCELED.value:
                    from ...service_handler import send_tcp_msg
                    send_tcp_msg(station.code,common.STMsgFormatter.gw_cancel_request_msg(station.code,req.tc_job_id,req.position,req.type,req.storage_code))
                    return
                db.st_gw_req_db_func.update_gateway_req_status_by_job_id(tc_job_id,ec.OrderStatus.COMPLETED.value)
            else:
                socketio.ws_server_log.warning(f'PLC reject {action} while HCC ST{station_code} condition is allowed, please check this error.')
                db.st_gw_req_db_func.update_gateway_req_status_by_job_id(tc_job_id,ec.OrderStatus.CANCELED.value)
            db.ws_request_db_func.add_ws_request(tc_entity,reply_request_data,'request')

    def emit_station_update_event(station_code,tc_job_id):
        reply_update_data = common.JsonPayload.db_udpate_respond(station_code,tc_job_id)
        socketio.SocketIOServer.emit_ws_event('update', data = reply_update_data, namespace='/station')

    def emit_weight_changed_event(station_code:int,storage_code:int,weight:str):

        weightReply = common.JsonPayload.weightChangedPayload(station_code,storage_code,weight)
        socketio.SocketIOServer.emit_ws_event("weight_changed", data = weightReply,namespace='/station')
    
    def emit_bridge_arrival_event(station_code:int,storage_code:int):
        junction_arrival_data = common.JsonPayload.junctionArrivalPayload(station_code,storage_code,True)
        db.ws_request_db_func.add_ws_request(ec.ExternalEntities.SM.value,junction_arrival_data,'junction_arrival')

    def emit_emo_event(emo_id:int,status:bool):
        
        data = common.JsonPayload.informEMOPayload(emo_id,status)
        socketio.SocketIOServer.emit_ws_event('cube_emo', data=data, namespace='/station',callback=ack_emo_event)

class StationNamespace(AsyncNamespace):
    
    @common.log_and_suppress_error(socketio.ws_server_log)
    # ---------------------  General Events ------------------------------
    def on_connect(self,sid,environ):
        socketio.ws_server_log.info(f'A socketio client has connected. sid - {sid}',color='light_green')
    
    @common.log_and_suppress_error(socketio.ws_server_log)
    def on_disconnect(self,sid):
        for client in websocket_clients.keys():
            if websocket_clients[client] == sid:
                socketio.ws_server_log.error(f'{client} has disconnected from HWX WS Server.')
                websocket_clients[client] = None
                break
        else:
            socketio.ws_server_log.error(f'An unknown client has disconencted from HWX WS Server. sid - {sid}')


    # ---------------------  TC Events ------------------------------
    @common.log_and_suppress_error(socketio.ws_server_log)
    def on_heartbeat(self,sid,data):
        ws_heartbeat_log.info(f'Received | heartbeat - {data}')
        reply = {'message': "pong"}
        ws_heartbeat_log.info(f'Reply | heartbeat - {reply}')
        return reply
    
    @common.log_and_suppress_error(socketio.ws_server_log)
    def on_init(self,sid,data):
        
        socketio.ws_server_log.info(f'Received | init -  {data}',color='light_magenta')
        
        entity = data['entity']
                
        if entity == ec.ExternalEntities.TC.value:
            zone = data['zone']
            cprint(f"TC {zone} Init",'light_green')
            # request_all_status()
            sub_entity = f'{entity}_{zone}'
            if sub_entity in ec.TcSubEntities._value2member_map_:
                websocket_clients[sub_entity] = sid
            else:
                socketio.ws_server_log.warning(f'WARNING: Tc Init sub_entity {sub_entity} is invalid. Ignoring.')
        elif entity == ec.ExternalEntities.SM.value:
            cprint("SM Init",'light_green')
            websocket_clients[ec.ExternalEntities.SM.value] = sid       
        elif entity == ec.ExternalEntities.MED.value:
            station_code = int(data['station_code'])
            cprint(f'MEDIATOR {station_code} Init','light_green')
            key = f'{ec.ExternalEntities.MED.value}{station_code}'
            websocket_clients[key] = sid
                
        CubeWSEmit.emit_active_station(sid) #only broadcast to the client who init 
        
        return common.StringResponse.successReply()
    
    # can remove future
    @common.log_and_suppress_error(socketio.ws_server_log)
    def on_status(self,sid,data):
        from ...service_handler import send_tcp_msg

        socketio.ws_server_log.info(f'Received | status - {data}',color='light_magenta')
        send_tcp_msg(int(data['station_code']), common.STMsgFormatter.status_msg(int(data['station_code'])))

    @common.sio_log_and_suppress_error(socketio.ws_server_log)
    @common.before_sio_event_handler
    def on_request(self,sid,data):
        from ...service_handler import send_tcp_msg

        is_cancel = data.get('is_cancelled',False)
        socketio.ws_server_log.info(f'Received | request - {data}',color='light_red' if is_cancel else 'light_magenta')
        
        station_code = int(data['station_code'])
        storage_code = str(data['storage_code'])
        tc_job_id = int(data['job_id'])
        position = int(data['position'])

        if data['action'] == "drop":
            action = ec.StationRequestType.DROP.value
        elif data['action'] == "pick":
            action = ec.StationRequestType.PICK.value

        station = db.st_db_func.get_station_by_code(station_code)

        """
        Perform vlaidation checking on incoming request d/p
        If is invalid normal request, let TC spam
        If is invalid cancel request, we return callback to prevent spam and complete on TC side
        """
        if action == ec.StationRequestType.PICK.value and not db.st_order_db_func.check_bin_is_processed(station.code,storage_code):
            socketio.ws_server_log.warning(f"Will not send pick request to station cause {data['storage_code']} is not processed/store yet or invalid storage_code.")
            callback =  common.responseSerialize.JsonPayload.dp_request_cb(station_code,tc_job_id,False,is_cancel,not is_cancel,reason =f"Invalid bin or bin {storage_code} is not processed yet")
            socketio.ws_server_log.info(f'Reply | request callback - {callback}',color='light_magenta')
            return callback
        if action == ec.StationRequestType.DROP.value and position not in [station.inner_drop,station.outer_drop]:
            socketio.ws_server_log.warning(f"Will not send drop request to station cause {position} is not the drop index of ST{station_code}.")
            callback =  common.responseSerialize.JsonPayload.dp_request_cb(station_code,tc_job_id,False,is_cancel,not is_cancel, reason = f"Invalid drop index for ST{station_code}")
            socketio.ws_server_log.info(f'Reply | request callback - {callback}',color='light_magenta')
            return callback
        if action ==ec.StationRequestType.DROP.value and station.mode == ec.CubeStationMode.ENROLL.value:
            socketio.ws_server_log.warning(f"Will not send drop request to station cause ST{station.code} is current in enrolling mode.")
            callback =  common.responseSerialize.JsonPayload.dp_request_cb(station_code,tc_job_id,False,is_cancel,not is_cancel, reason = f"ST{station_code} is enrolling")
            socketio.ws_server_log.info(f'Reply | request callback - {callback}',color='light_magenta')
            return callback
        if station.type != ec.CubeStationType.I.value:
            if action == ec.StationRequestType.PICK.value and not db.st_order_db_func.check_bin_arrive_pick(station.code,storage_code) :
                socketio.ws_server_log.warning(f"Will not send request to station cause {data['storage_code']} is not arrived at pick point yet or update pick job is already completed.")
                callback =  common.responseSerialize.JsonPayload.dp_request_cb(station_code,tc_job_id,False,is_cancel,not is_cancel, reason = f"Bin has not arrived pick point yet")
                socketio.ws_server_log.info(f'Reply | request callback - {callback}',color='light_magenta')
                return callback
          
        # cancel request 
        if is_cancel:
            cancel_msg = common.STMsgFormatter.gw_cancel_request_msg(station_code,tc_job_id,position,action,storage_code)
            if action == ec.StationRequestType.DROP.value:
                station.latest_cancel_drop_msg[position] = cancel_msg 
                station.latest_drop_msg[position] = ''
                station.record_gw_status(True,position,f'TC cancel drop request of {storage_code}, pending on station ack, req id : {tc_job_id}')
            elif action == ec.StationRequestType.PICK.value:
                station.latest_cancel_pick_msg[position] = cancel_msg 
                station.latest_pick_msg[position] = ''
                station.record_gw_status(False,position,f'TC cancel pick request of {storage_code}, pending on station ack, req id : {tc_job_id}')

            if station.type == ec.CubeStationType.I.value:
                with socketio.request_lock:
                    gateway_req = db.st_gw_req_db_func.get_latest_gateway_req_by_job_id(tc_job_id)
                    if not gateway_req:
                        socketio.ws_server_log.warning(f'No req with id : {tc_job_id} is created yet, canceled request will not be handled on HCC side')
                    else:
                        # check if the gateway req already approve by PLC by checking status == COMPLETED
                        if gateway_req.status in [ec.OrderStatus.COMPLETED.value]:
                            db.st_gw_req_db_func.update_gateway_req_status_by_job_id(tc_job_id,ec.OrderStatus.CANCELED.value)
                        else:
                            db.st_gw_req_db_func.update_gateway_req_by_job_id(tc_job_id,dict(canceled_ack = True,
                                                                                            status = ec.OrderStatus.CANCELED.value))
                            
                    callback =  common.responseSerialize.JsonPayload.dp_request_cb(station_code,tc_job_id,True,is_cancel,False)
                    socketio.ws_server_log.info(f'Reply | request callback - {callback}',color='light_magenta')
                    return callback
            else:
                # 26/5/2025 Other than I station will not send cancel request to PLC
                callback =  common.responseSerialize.JsonPayload.dp_request_cb(station_code,tc_job_id,True,True,False)
                socketio.ws_server_log.info(f'Reply | request callback (OLD station will not send CR command - {callback}',color='light_magenta')
                # send_tcp_msg(station_code,cancel_msg)
                # callback =  common.responseSerialize.JsonPayload.dp_request_cb(station_code,tc_job_id,True,is_cancel,True)
                # socketio.ws_server_log.info(f'Reply | request callback - {callback}',color='light_magenta')
                return callback

        msg = common.STMsgFormatter.gw_request_msg(station_code,tc_job_id,position,action,storage_code)
        if station.type == ec.CubeStationType.I.value:
            
            req_is_canceled = req_is_duplicated =  req_is_pending = False
            status = ec.OrderStatus.AVAILABLE.value

            pending_cancel_request = db.st_gw_req_db_func.get_gateway_req(dict(status = ec.OrderStatus.CANCELED.value,
                                                                               canceled_ack = False,
                                                                               station_code = station_code))
            if pending_cancel_request:
                callback = common.responseSerialize.JsonPayload.dp_request_cb(station_code,tc_job_id,True,is_cancel,True)
                socketio.ws_server_log.warning(f'There is cancel request pending on PLC, will not accept this request for now.')
                socketio.ws_server_log.info(f'Reply | request callback - {callback}',color='light_magenta')
                return callback 

            if action == ec.StationRequestType.DROP.value:
                pending_req = db.st_gw_req_db_func.get_gateway_req(dict(status=ec.OrderStatus.PENDING.value,station_code=station_code,type=ec.StationRequestType.DROP.value))
                if pending_req and pending_req.tc_job_id != tc_job_id:
                    callback = common.responseSerialize.JsonPayload.dp_request_cb(station_code,tc_job_id,True,is_cancel,True)
                    socketio.ws_server_log.warning(f'There is already a drop request pending on next bin, please cancel it before sending new drop request, will not accept this request for now.')
                    socketio.ws_server_log.info(f'Reply | request callback - {callback}',color='light_magenta')
                    return callback 
            
                if db.st_order_db_func.get_num_of_bin_in_station_pre_processed(station_code)>0:
                    status = ec.OrderStatus.PENDING.value
                    req_is_pending = True

            gateway_req : model.StationGatewayReq = db.st_gw_req_db_func.get_latest_gateway_req_by_job_id(tc_job_id)
            

            if not gateway_req:
                # add request event into db
                req_id = db.st_gw_req_db_func.add_gw_request_event(station_code=station_code,tc_job_id=tc_job_id, storage_code=storage_code,type=action, position=position, status=status)
            else:
                if gateway_req.status == ec.OrderStatus.CANCELED.value:
                    req_id = db.st_gw_req_db_func.update_gateway_req_by_id(gateway_req.id,dict(status=status,
                                                                                  canceled_ack = False))
                    req_is_canceled= True
                else:
                    socketio.ws_server_log.warning(f"Duplicate request ")
                    req_is_duplicated = True

            if not req_is_duplicated:
                if action == ec.StationRequestType.DROP.value:
                    if not req_is_pending:
                        station.latest_drop_msg[position] = msg
                        station.latest_cancel_drop_msg[position] = ''
                        station.record_gw_status(True,position,f'TC request to drop {storage_code}, pending station to response, req id : {req_id}')
                        # db.st_event_db_func.create_st_event(ec.CubesIStationEvent.REQ_DROP.value,station_code,storage_code,request_id=req_id)
                        # db.st_event_db_func.create_st_event(ec.CubesIStationEvent.DROP_DONE.value,station_code,storage_code,request_id=req_id)
                        db.st_event_db_func.create_bundled_events(True,dict(
                            station_code = station_code,
                            storage_code = storage_code,
                            request_id = req_id
                        ))
                        
                if action == ec.StationRequestType.PICK.value:
                    station.latest_pick_msg[position] = msg
                    station.latest_cancel_pick_msg[position] = ''
                    station.record_gw_status(False,position,f'TC request to pick {storage_code}, pending station to response, req id : {req_id}')
                    is_enroll = db.st_order_db_func.check_bin_is_enroll(station_code,storage_code)
                    # db.st_event_db_func.create_st_event(ec.CubesIStationEvent.REQ_PICK.value,station_code,storage_code,is_enroll=is_enroll,request_id=req_id)
                    # db.st_event_db_func.create_st_event(ec.CubesIStationEvent.PICK_DONE.value,station_code,storage_code,is_enroll=is_enroll,request_id=req_id)
                    db.st_event_db_func.create_bundled_events(False,dict(
                        station_code = station_code,
                        storage_code = storage_code,
                        request_id = req_id,
                        is_enroll = is_enroll
                    ))

                if req_is_canceled:
                    socketio.ws_server_log.warning(f"Received canceled request, recover it back to {status}.")

            callback =  common.responseSerialize.JsonPayload.dp_request_cb(station_code,tc_job_id,True,is_cancel,False)
            socketio.ws_server_log.info(f'Reply | request callback - {callback}',color='light_magenta')
            return callback
        else:
            send_tcp_msg(station_code, common.STMsgFormatter.gw_request_msg(station_code,tc_job_id,position,action,storage_code))
            
            if action == ec.StationRequestType.DROP.value:
                station.latest_drop_msg[position] = msg
                station.latest_cancel_drop_msg[position] = ''
                station.record_gw_status(True,position,f'TC request to drop {storage_code}, pending station to response, req id : {tc_job_id}')
            if action == ec.StationRequestType.PICK.value:
                station.latest_pick_msg[position] = msg
                station.latest_cancel_pick_msg[position] = ''
                station.record_gw_status(False,position,f'TC request to pick {storage_code}, pending station to response, req id : {tc_job_id}')
            callback = common.responseSerialize.JsonPayload.dp_request_cb(station_code,tc_job_id,True,is_cancel,True)
            socketio.ws_server_log.info(f'Reply | request callback - {callback}',color='light_magenta')
            return callback

    @common.sio_log_and_suppress_error(socketio.ws_server_log)
    @common.before_sio_event_handler
    def on_update(self,sid,data):
        from ...service_handler import send_tcp_msg
        
        socketio.ws_server_log.info(f'Received | update - {data}',color='light_magenta')
        
        station_code = int(data['station_code'])
        tc_job_id = int(data['job_id'])
        position = int(data['position'])
        storage_code = str(data['storage_code'])

        if (data['action'] == "drop"):
            action = ec.StationRequestType.DROP.value
        elif (data['action'] == "pick" or data['action'] == "Pick"):
            action = ec.StationRequestType.PICK.value
        
        station = db.st_db_func.get_station_by_code(station_code)
        msg =  common.STMsgFormatter.gw_update_msg(station_code,tc_job_id,position,action,storage_code)

        if action == ec.StationRequestType.DROP.value:
            station.latest_drop_msg[position] = msg
            station.record_gw_status(True,position,f'TC done dropping bin {storage_code}, pending station to ack, req id : {tc_job_id}')
        elif action == ec.StationRequestType.PICK.value:
            if station.type == ec.CubeStationType.I.value: #I station MTP job might not be the last st movement
                if station.queue[station.inner_pick] != storage_code:
                    socketio.ws_server_log.warning(f"Will not send update to station cause {data['storage_code']} is not at pick point, please check TC module.")
                    return
            else:
                if not db.st_order_db_func.check_bin_arrive_pick(station.code,storage_code):
                    socketio.ws_server_log.warning(f"Will not send update to station cause {data['storage_code']} is not at pick point, please check TC module.")
                    return
            station.latest_pick_msg[position] = msg
            station.record_gw_status(False,position,f'TC done picking bin {storage_code}, pending station to ack, req id : {tc_job_id}')

        send_tcp_msg(station_code, msg)

    @common.sio_log_and_suppress_error(socketio.ws_server_log)
    @common.before_sio_event_handler
    def on_station_order(self,sid,data):
        socketio.ws_server_log.info(f'Received | station_order - {data}',color='light_magenta')
        
        station_code = int(data['station_code'])
        storage_code = str(data['storage_code'])
        entity = data['request_by']
        
        
        if entity == ec.ExternalEntities.TC.value:
            qc = data['qc']
            bridge = data['bridge']
            tc_order_id = data['order_id']
            start_index = int(data['drop_index'])
            enroll = False
            is_unique = not db.st_order_db_func.check_order_exist(tc_order_id)
          
        if not is_unique:
            socketio.ws_server_log.warning(f'Ignoring duplicate station order with storage_code: {storage_code}.')
        elif not db.st_mov_db_func.on_station_order(station_code,storage_code,tc_order_id,start_index,qc=qc,bridge=bridge):
            socketio.ws_server_log.warning(f'Failed to create station order {storage_code}.')
        else:
            
            callback_msg = {
                'station_code': station_code,
                'storage_code': int(storage_code),
                'order_id': tc_order_id
            }
            
            callback_data = common.JsonPayload.jsonFormat(callback_msg)
            common.notify_wcs(f"Bin {storage_code} has droppped at {'drop' if not enroll else 'enrollment'} point of ST{station_code}")
            socketio.ws_server_log.info(f'Reply | station_order CALLBACK {entity} - {callback_data}',color='light_magenta')
            return callback_data
    
    def on_weight_requested(self,sid,data):
        
        from ...service_handler.cube_handler import send_tcp_msg
         
        socketio.ws_server_log.info(f'Received | weight_requested (wcs) - {data}',color='light_magenta')
        
        # Construct full bin name from int bin_id that WMS provided
        station_code = int(data['station_code'])
        storage_code = str(data['storage_code'])
                
        # if station_code in rt.runtime.weight_polling_stations:
        #     cprint(f'Station already polling for weight. Ignoring call.','light_yellow',attrs=["underline"])
        # else:
        station = db.st_db_func.get_station_by_code(station_code)
        if not station.bin_at_worker:
            socketio.ws_server_log.warning(f'No bin at station worker point.')
        send_tcp_msg(station_code,common.STMsgFormatter.weight_msg(station_code,storage_code))

        return common.JsonResponse.wmsSuccessCallback()
    
    def on_weight_detached(self,sid,data):
                        
        socketio.ws_server_log.info(f'Received | weight-detached (wcs) - {data}',color='light_magenta')
        
        station_code = int(data['station_code'])
        
        # if station_code in rt.runtime.weight_polling_stations:
        #     common.BackgroundTask.stop_weight_polling(station_code)

        return common.JsonResponse.wmsSuccessCallback()

    @common.sio_log_and_suppress_error(socketio.ws_server_log)
    def on_cube_emo(self,sid,data):
        from ...service_handler.cube_handler import send_tcp_msg

        emo_id = data['emo_id']
        socketio.ws_server_log.info(f'Received | cube_emo - {data}',color='light_magenta')
        send_tcp_msg(emo_id,common.message_formatter.EMOMsgFromatter.status_msg(emo_id),target_module=ec.ModuleCode.EMO.value)

socketio.sio.register_namespace(StationNamespace('/station'))

def determine_tc_entity(station_code:int, index= None)->str:
    try:
        '''Used to determine which of the 3 sub-TCs to send the request to as some station span multiple zones.
           Large workstation has B or C zones
           IQC has A or C zones'''
        station = db.st_db_func.get_station_by_code(station_code)
        
        if index is None:
            tc_entity = f"TC_{station.zone}"
        elif station.type == ec.CubeStationType.LARGE.value:
            if index == station.inner_pick:
                tc_entity = f'TC_{station.zone}'
            elif index == station.outer_pick:
                tc_entity = f'TC_C'
        elif station.type == ec.CubeStationType.QC.value:
            if index == station.inner_pick:
                tc_entity = f'TC_C'
            elif index == station.outer_pick:
                tc_entity = f'TC_A'
        else:
            tc_entity = f"TC_{station.zone}"
        return tc_entity
    except Exception as e:
        socketio.ws_server_log.error(f'determine_tc_entity Error: {e}')
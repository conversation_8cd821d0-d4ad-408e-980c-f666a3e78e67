import datetime

from typing import List
from collections import defaultdict

from config import Config
from .. import enum_collections as ec

from pingspace_python_packages.centralized_logging.util import Body , Message

class StringResponse:
    # For PLC
    
    @staticmethod
    def PairingSuccess(module_code:str,plc_id:int):
        return f"{module_code},{plc_id},Pairing,SUCCESS;"

    @staticmethod
    def successReply():
        return "OK"
    
class StandardResponse:
    # For websocket / socketio
    # For http
    @staticmethod
    def response(
        status: bool = True,
        message: str = str(),
        data: list = list(),
        model: dict = dict(),
        code: ec.HTTPStatus = None,
        metadata: dict = defaultdict()

    ):
        if not code: code = ec.HTTPStatus.OK.value if status else ec.HTTPStatus.BAD_REQUEST.value
        return locals(), code
    
class JsonResponse:
    # For websocket / socketio
    @staticmethod
    def wmsSuccessCallback():
        return {
            "status": 200,
            "message": "OK"
        }
    
    @staticmethod
    def wmsFailedCallback(error):
        return {
            "status": 400,
            "message": error
        }
        
    # For http

class JsonPayload:
    # For websocket / socketio
    @staticmethod
    def jsonFormat(msg, error=''):
        json = {
            "data": msg,
            "error": error
        }
        return json 
    
    @staticmethod
    def activeConnectionPayload(station_code_list:List[int], error = "")->dict:
        json = {
            "data": {
                "stations": station_code_list
                },
            "error": error
        }
        return json
    
    @staticmethod
    def recoveryStatusPayload(station_code:int,status:bool, error = "")->dict:
        json = {
            "data": {
                "station_code": station_code,
                "status":status
                },
            "error": error
        }
        return json
    @staticmethod
    def workArrivalPayload(station_code:int,storage_code:int,time_arrive:datetime.datetime,error = "")->dict:
        json = {
            "data": {
                "station_code": station_code,
                "storage_code": storage_code,
                "time_arrive":time_arrive
            },
            "error": error 
        }
        return json
    
    @staticmethod
    def junctionArrivalPayload(station_code:int,storage_code:int,is_bridge:bool,error = "")->dict:
        json = {
            "data": {
                "station_code": station_code,
                "storage_code": storage_code,
                "is_bridge":is_bridge
            },
            "error": error 
        }
        return json
    
    @staticmethod
    def pickArrivalPayload(station_code:int,storage_code:int,pick_index:int,error = "")->dict:
        json = {
            "data": {
                "station_code": station_code,
                "storage_code": storage_code,
                "pick_index":pick_index,
            },
            "error": error 
        }
        return json
    
    @staticmethod
    def dp_request_respond(station_code:int,job_id:int,status:bool,reason:str = "",error:str="",is_cancelled :bool= False)->dict:
        json = {
            "data":{
                "station_code": station_code,
                "job_id": job_id,
                "status": status,
                "is_cancelled":is_cancelled,
                "message": "Approved" if status else "Denied",
                "reason":reason if not status else "",
                "request_id":None
            }, 
            "error":error
        }
        return json
    
    @staticmethod
    def dp_request_cb(station_code:int,job_id:int,status:bool,is_cancelled:bool,tc_spam:bool,reason :str = "", error=""):
        json = {
            "data":{
                "station_code": station_code,
                "job_id": job_id,
                "status": status,
                "is_cancelled": is_cancelled,
                "reason":reason if not status else ""
            }, 
            "error":error
        } if not tc_spam else {
            "data": None,
            "error":error
        }
        return json
    @staticmethod
    def db_udpate_respond(station_code:int,job_id:int,error:str="")->dict:
        json = {
            "data":{
                "station_code": station_code,
                "job_id": job_id,
                "message": "Station has successfully updated bin."
            }, 
            "error":error
        }
        return json
    
    @staticmethod
    def weightChangedPayload(station_code:int, storage_code:int,weight:str,error:str= "")->dict:
        json = {
            "data":{
                "station_code": station_code,
                "storage_code": storage_code,
                "weight": weight
            }, 
            "error":error
        }
        return json

    @staticmethod
    def informErrorPayload(msg:str,error_level:str,error_desc:str,asrs_id:int,order_id:int=None,station_id:int=None,pallet_id:str=None, domain :str = ec.ModuleCode.ASRS.value,action:str = None ):
        """For ASRS use to inform error"""
        data = {
                "message" :msg ,
                "error_level" :error_level ,
                "error_desc" :error_desc,
                "asrs_id" : asrs_id ,
                "order_id" :order_id, 
                "station_id" : station_id,
                "pallet_id" : pallet_id,
                "domain":domain,
                "action":action,
            }
        return data
    
    @staticmethod
    def informStationErrorPayload(matrix_code:int,station_code:int,event:str,id:int,error_name:str,error_message:str,happened_time,resolved_time):
        """Use to send WMS/Mediator station error code"""
        data = {
            "event":event,
            "payload":
            {
                    "matrix_code": f'{matrix_code}',
                    "station_code": station_code,
                    "identity_key": str(id),
                    "error_code": error_name,
                    "error_description": error_message,
                    "happened_at_utc": happened_time,
                    "resolved_at_utc": resolved_time
            }    
        }
        return data
    
    @staticmethod
    def informEMOPayload(emo_id:int,status:bool,error:str="")->dict:
        json = {
            "data": {
                "emo_id":emo_id,
                "status": status
            },
            "error": error 
        }
        return json
    @staticmethod
    def informBinEnrollPayload(station_code,storage_code):
        
        data ={
            "station" : station_code,
            "storage" : storage_code,
        }
        return data  

    @staticmethod
    def informBinEnrollPayloadv2(station_code:int,storage_code:int):
        
        data ={
            "stationCode" : station_code,
            "storageCode" : storage_code,
        }
        return data  
    
    @staticmethod
    def informBridgeFailurePayload(station_code,storage_code):
        data = {
            'station_code': int(station_code),
            'storage_code': int(storage_code)
        }
        return data

    @staticmethod
    def wmsWSModeChangePaylaod(matrix_code:int,station_code:int, mode:str, storage_code:str, time):
        data = {
            "category":"Workstation",
            "data": {
                "details":{
                    "bin_no":storage_code,
                    "mode":mode
                },
                "matrix_code":f"{matrix_code}",
                "device_identifier":f"Workstation-{matrix_code}",
                "station_code":station_code,
                "status": "Available",
                "updated_at_utc": time
            },
            "event":ec.WebhookEvent.MODE_CHANGE.value
        }

        return data
    
class RMQMessage:

    @staticmethod
    def ws_server_messager(event_name:str,item:dict)->str:
        """
        Data format that need to indicate TC_BT to send to TC_DASH through which event and the json body 
        """
        data = {
            "event":event_name,
            "body": {
                'item':item,
                'created_at': datetime.datetime.now(datetime.timezone.utc).isoformat()
            }
        }

        return data
    
    @staticmethod
    def hcc_bin_info_messager(msg:str):
        data = {
            "message":msg,
            "message_type":[ec.RabbitMQMessageType.STORAGE_OVERWEIGHT_EVENT_MESSAGE.value]
        }

        return data

    @staticmethod
    def centralized_logging_messager(msg:str,log_type:str,log_name:str,**opt_parameter)->str:
    
        correlationId:str = opt_parameter.get('correlationId','-')
        containterName:str = opt_parameter.get('containterName', 'cube-ihub-hcc')
        content:str = opt_parameter.get('content','-')
        exceptionStackTrace:str = opt_parameter.get('exceptionStackTrace','-')
        
        ipaddress = Config.get_flask_conn()
        
        utcTimestamp = datetime.datetime.now(datetime.timezone.utc)
        unixTimestamp = int(round(datetime.datetime.timestamp(utcTimestamp))) 

        return Body(
            message = Message(
                correlationId = correlationId,
                utcTimestamp = str(utcTimestamp),
                unixTimestamp = unixTimestamp,
                domain = 'HCC',
                originator = log_name,
                logType = log_type,
                ipAddress = ':'.join(ipaddress),
                containerName = containterName,
                message = msg,
                content = content,
                innerException = exceptionStackTrace,
                environment = Config.FLASK_ENV 
            ),
            messageType = [Config.CENTRALIZED_LOGGING_Q_MSG_TYPE]
        )
from dataclasses import dataclass

from app import db


@dataclass
class AsrsStations(db.Model):
    id: int
    station_id: int
    type: str
    created_at: str
    updated_at: str

    __tablename__ = 'asrs_stations'

    id = db.Column(
        db.Integer,
        primary_key=True
    )

    station_id = db.Column(
        db.Integer,
        index=False,
        unique=True,
        nullable=False
    )

    type = db.Column(
        db.String(128),
        index=False,
        unique=False,
        nullable=False
    )

    created_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

    updated_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

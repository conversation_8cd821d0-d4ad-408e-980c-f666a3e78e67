import datetime 

from typing import Set,Tuple
from flask_restx import Namespace,Resource, reqparse,fields

from .... import api
from config import Config
from ... import api_routing as api_routing,request_parsers as req_parser
from .... import common as common,enum_collections as ec,database as db

nsstation = Namespace(api_routing.UrlFolder.station,"Station API")

# Add this model definition near the top with other models
station_seed_model = api.model('StationSeed', {
    'station_code': fields.Integer(required=True, description='Station code',min=1),
    'matrix_code': fields.Integer(required=True, description='Matrix code',min=1),
})

bulk_station_seed_model = api.model('BulkStationSeed', {
    'stations': fields.List(fields.Nested(station_seed_model), required=True, description='List of stations to seed')
})

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsstation.route(api_routing.UrlPath.seed_stations)
class SeedStations(Resource):
    @nsstation.doc(description='Seed multiple stations with provided configuration. (Currently only ablke to support seeding I station type) Please be aware that this will clear all existing data in the database.')
    @api.expect(bulk_station_seed_model)
    def post(self):
        """
        Seed multiple stations based on provided configuration
        """
        
        from seeder import clearDB
        from .... import initialization as init
        from ....http_router import HttpRequest
        from ....communications_provider.socketio.cube import SioBroadcastHandler


        data = api.payload
        stations = data.get('stations', [])
        
        if not stations:
            return common.StandardResponse.response(
                success=False,
                message="No stations provided in request"
            )

        if station_orders := db.st_order_db_func.find_order(dict(status=ec.OrderStatus.PROCESSING.value)):
            return common.StandardResponse.response(False,message=f"Cannot seed station when there is order in processing. Please wait until all order is completed.")
        
        # Clear existing stations if needed
        clearDB()
        
        to_seed_station_list = []
        unique_station_code = set()
        for station_data in stations:
            station_code = station_data['station_code']
            matrix_code = station_data['matrix_code']
            
            if station_code in unique_station_code:
                return common.StandardResponse.response(
                    False,
                    message=f"Duplicate station code {station_code} provided in request",
                    code =ec.HTTPStatus.BAD_REQUEST.value
                )
            unique_station_code.add(station_code)
            to_seed_station_list.append((station_code, matrix_code))
            
        results = db.st_db_func.bulk_seed_station(to_seed_station_list)

        init.station_initialization()
        HttpRequest.reinitialization()
        SioBroadcastHandler.reinitialization()

        return common.StandardResponse.response(
            data={
                'total_stations': len(results),
            }
        )


station_details_parser = reqparse.RequestParser()
station_details_parser.add_argument("code",required = False,type = int,location = 'args')
station_details_parser.add_argument("zone",required = False,type = str,location = 'args')

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsstation.route(api_routing.UrlPath.station_details)
class StationDetails(Resource):
    @nsstation.doc(description='To get the details of the station by zone or code or None.')
    @api.expect(station_details_parser)
    def get(self):

        args = station_details_parser.parse_args(strict = True)
        zone = args['zone']
        code = args['code']

        data = db.st_db_func.get_station_detail(zone,code)

        if data:
            return common.StandardResponse.response(data=data)
        else:
            msg = f"No result found in db with zone = {zone} and code = {code}"
            return common.StandardResponse.response(False,msg)



station_plc_status_parser = req_parser.code_parser.copy()

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsstation.route(api_routing.UrlPath.station_plc_status)
class StationPLCStatus(Resource):
    """
    This endpoint will be called when 
        1. Opertor trigger recovery mode from TC dashbaord

    Currently only able to support 
    - REGULAR   ( cell 6 & 7  )
    - LARGE     ( cell 6 & 12 )
    - I station ( cell 3      )
    """
    @nsstation.doc(description='To get the latest plc status.')
    @api.expect(station_plc_status_parser)
    def get(self):

        args = station_details_parser.parse_args(strict = True)
        code = args['code']

        station = db.st_db_func.get_station_by_code(code)

        def generate_bin_status(cell:int)->str:
            bin_status = '|' * cell
            return bin_status

        if not Config.RECOVERY_MODE:
            return common.StandardResponse.response(data= [])

        if station.type == ec.CubeStationType.I.value:
            return common.StandardResponse.response(data=station.queue)

        # Other station ( queue represent the sequence of bin in station )
        else:
            if station.plc_status:
                splited_plc_status = station.plc_status.split('|')
            else:
                return common.StandardResponse.response(False,message=f"No bin status from PLC ")
            
            bin_in_station = db.st_order_db_func.get_all_bin_in_station(station.code)
            num_of_bin_in_plc_status = len([bin for bin in splited_plc_status if bin])

            # (1) Check if bin number is tally between HCC and PLC status
            if num_of_bin_in_plc_status != len(bin_in_station):
                return common.StandardResponse.response(False,message=f"Intally number of bin in station from plc status.  HCC: {bin_in_station} PLC:{splited_plc_status}")
                
            
            station_reversed_queue = station.queue[::-1]
            for index in range(len(splited_plc_status)-1,-1,-1): # start from the end of the station index 
                
                plc_bin = splited_plc_status[index]

                if not plc_bin: # skip index without bin  
                    continue

                order = db.st_order_db_func.find_active_order(station.code,plc_bin)

                # (2) Check if bin in plc status is recorded in HCC
                if not order:
                    return common.StandardResponse.response(False,message=f"Unknown bin ({plc_bin}) from plc status.  HCC: {station.queue} PLC:{splited_plc_status}")
                
                move_to_pick_job = db.st_mov_db_func.find_job(dict(type = ec.CubeStationJobType.MOVE_TO_PICK.value,
                                                                   order_id=order.id))
                move_to_work_job = db.st_mov_db_func.find_job(dict(type = ec.CubeStationJobType.MOVE_TO_WORK.value,
                                                                   order_id=order.id))
                
                try:
                    hcc_bin = station_reversed_queue.pop()
                    
                    # (3) Check the sequence of the bin 
                    if plc_bin != hcc_bin:
                        return common.StandardResponse.response(False,message=f"Invalid sequence of bin from PLC status.  HCC: {station.queue} PLC:{splited_plc_status}")
                    
                except IndexError:
                    # (4) LARGE staiton queue does not record bin before work point, so we only check if their MTW is completed 
                    if station.type==ec.CubeStationType.LARGE.value:
                        if index > station.worker:
                            return common.StandardResponse.response(False,message=f"Missing bin in HCC queue, pleae check HCC module.  HCC: {station.queue} PLC:{splited_plc_status}")                    
                        if index == station.worker:
                            if move_to_work_job.status == ec.OrderStatus.COMPLETED.value:
                                return common.StandardResponse.response(False,message=f"Missing bin in HCC queue, pleae check HCC module.  HCC: {station.queue} PLC:{splited_plc_status}")
                    else:
                        return common.StandardResponse.response(False,message=f"Missing bin in HCC queue, pleae check HCC modulee.  HCC: {station.queue} PLC:{splited_plc_status}")                    
                
                
                # (5) check the bin location according to the checkpoint of station ( Before, At, After Work Point/Pick Point )
                if index > station.worker:
                    # check if the bin after work point is already processed
                    if not db.st_order_db_func.check_bin_is_processed(station.code,plc_bin):
                        return common.StandardResponse.response(False,message=f"Bin ({plc_bin}) is not proccessed yet, should not pass worker point.  HCC: {station.queue} PLC:{splited_plc_status}")
                    if not move_to_pick_job:
                        return common.StandardResponse.response(False,message=f"Bin ({plc_bin}) does not move to pick job yet, please check HCC module.  HCC: {station.queue} PLC:{splited_plc_status}")
                    if index > move_to_pick_job.to_index:
                        return common.StandardResponse.response(False,message=f"Bin ({plc_bin}) move over its pick point index {move_to_pick_job.to_index}.  HCC: {station.queue} PLC:{splited_plc_status}")
                    if index == move_to_pick_job.to_index and move_to_pick_job.status != ec.OrderStatus.COMPLETED.value:
                        return common.StandardResponse.response(False,message=f"Bin ({plc_bin}) at pick point index {move_to_pick_job.to_index} but never give job done, lvl2 Engineer to recover.  HCC: {station.queue} PLC:{splited_plc_status}")
                    if move_to_pick_job.status == ec.OrderStatus.COMPLETED.value and index != move_to_pick_job.to_index:
                        return common.StandardResponse.response(False,message=f"Bin ({plc_bin}) has complete his move to pick job, should at index {move_to_pick_job.to_index}.  HCC: {station.queue} PLC:{splited_plc_status}")
                    # if index == move_to_pick_job.to_index and move_to_pick_job.status != ec.OrderStatus.COMPLETED.value:
                    #     return common.StandardResponse.response(False,message=f"Bin ({plc_bin}) at its pick point index {move_to_pick_job.to_index} but never give job done.  HCC: {station.queue} PLC:{splited_plc_status}")
                elif index == station.worker:
                    if is_processed := db.st_order_db_func.check_bin_is_processed(station.code,plc_bin):
                        if move_to_pick_job and move_to_pick_job.status == ec.OrderStatus.COMPLETED.value:
                            return common.StandardResponse.response(False,message=f"Bin ({plc_bin}) should be at index {move_to_pick_job.to_index}.  HCC: {station.queue} PLC:{splited_plc_status}")
                        elif not move_to_pick_job:
                            return common.StandardResponse.response(False,message=f"Bin ({plc_bin}) does not move to pick job yet, please check HCC module.  HCC: {station.queue} PLC:{splited_plc_status}")
                    else:
                        if station.bin_at_worker and plc_bin != station.bin_at_worker:
                            return common.StandardResponse.response(False,message=f"Bin ({plc_bin}) should not be at work point {station.worker}, should be bin {station.bin_at_worker}.  HCC: {station.queue} PLC:{splited_plc_status}")
                        if move_to_work_job.status != ec.OrderStatus.COMPLETED.value:
                            return common.StandardResponse.response(False,message=f"Bin ({plc_bin}) at work point {station.worker} but never give job done, contact lvl2 Engineer to recover.  HCC: {station.queue} PLC:{splited_plc_status}")
                else:
                    if move_to_pick_job and move_to_pick_job.status == ec.OrderStatus.COMPLETED.value:
                        return common.StandardResponse.response(False,message=f"Bin ({plc_bin}) should be at index {move_to_pick_job.to_index}.  HCC: {station.queue} PLC:{splited_plc_status}")
                    if plc_bin == station.bin_at_worker:
                        return common.StandardResponse.response(False,message=f"Bin ({plc_bin}) should be at work point {station.worker}.  HCC: {station.queue} PLC:{splited_plc_status}")                            
                    if move_to_work_job.status == ec.OrderStatus.COMPLETED.value:
                        return common.StandardResponse.response(False,message=f"Bin ({plc_bin}) should be at/after work point {station.worker}.  HCC: {station.queue} PLC:{splited_plc_status}")
                    if index < move_to_work_job.from_index:
                        return common.StandardResponse.response(False,message=f"Bin ({plc_bin}) should be at/after drop point {move_to_work_job.from_index}.  HCC: {station.queue} PLC:{splited_plc_status}")

            
            # (6) check if there is missing bin from PLC status 
            if station_reversed_queue:
                return common.StandardResponse.response(False,message=f"Missing bin from PLC bin status {station_reversed_queue}.  HCC: {station.queue} PLC:{splited_plc_status}")
            
            return common.StandardResponse.response(data=splited_plc_status)


        #     # I station ( queue represent the state of station )
        #     if station.type == ec.CubeStationType.I.value:
        #         if splited_plc_status != station.queue:
        #             # check if number of bin is similar or not 
        #             num_of_hcc_bin = len([x for x in station.queue if x != ""])
        #             num_of_plc_bin = len([x for x in splited_plc_status if x != ""])
        #             if num_of_hcc_bin != num_of_plc_bin:
        #                 return common.StandardResponse.response(False, message=f"The number of bin for HCC and PLC status is different. HCC: {station.queue} PLC:{splited_plc_status}")
        #             # check if there is incomplete movement on hcc side cause inconsistent bin status else return error 
        #             moving_bin = set()
        #             for hcc_bin, plc_bin in zip(station.queue,splited_plc_status):
        #                 if hcc_bin != plc_bin:
        #                     # If two bin has value, means there is error, missing latest status PLC or missing job done from PLC
        #                     if hcc_bin == '' or plc_bin == '':
        #                         exist_bin = hcc_bin if hcc_bin else plc_bin
        #                         if exist_bin in moving_bin: continue
        #                         bin_processing_job = db.st_mov_db_func.find_latest_job(dict(bin_no=exist_bin,
        #                                                                                     station_id = station.id,
        #                                                                                     status = ec.OrderStatus.PROCESSING.value))
        #                         # if the inconsistent bin is not a moving job from 0 to / 2 to 0 , means there is error
        #                         if bin_processing_job and abs(bin_processing_job.from_index-bin_processing_job.to_index) == 2:
        #                             moving_bin.add(exist_bin)
        #                         else:
        #                             return common.StandardResponse.response(False,message=f"Inconsistent bin status between HCC and PLC. HCC: {station.queue} PLC:{splited_plc_status}")
        #                     else:
        #                         return common.StandardResponse.response(False,message=f"Inconsistent bin status between HCC and PLC. HCC: {station.queue} PLC:{splited_plc_status}")            
                            
            

        # else:
        #     return common.StandardResponse.response(False,message=f"No bin status from PLC ")
            

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsstation.route(api_routing.UrlPath.station_gw_status)
class StationGWStatus(Resource):
    @nsstation.doc(description='To get the gw status of station .')
    @api.expect(req_parser.code_parser)
    def get(self):
        args = req_parser.code_parser.parse_args(strict=True)
        code = args['code']

        station = db.st_db_func.get_station_by_code(code)

        gw_in_status = {key: value for key, value in station.gw_in_status.items() if key is not None}
        gw_out_status = {key: value for key, value in station.gw_out_status.items() if key is not None}
        data = [
            gw_in_status,
            gw_out_status
        ]
        

        return common.StandardResponse.response(data=data)

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsstation.route(api_routing.UrlPath.station_healthcheck)
class StationHealthcheck(Resource):
    @nsstation.doc(description='Get healthcheck of all stations - shows jobs processing for more than 1 minute without completion')
    def get(self):
        """
        Get healthcheck of all stations showing jobs that have been processing for more than 1 minute
        Returns station code, storage code, job details, and PLC acknowledgment status
        """
        import datetime
        import pytz
        
        # Calculate the threshold time (1 minute ago)
        # Use timezone-aware datetime to match the database field
        threshold_time = datetime.datetime.now(pytz.UTC) - datetime.timedelta(minutes=1)
        
        # Get all processing jobs that are older than 1 minute
        processing_jobs = db.st_mov_db_func.station_mov_dal.find_jobs_for_healthcheck(threshold_time)
        
        healthcheck_data = []
        
        for job in processing_jobs:
            job_detail = {
                'station_code': job.station_id,
                'storage_code': job.bin_no,
                'job_type': job.type,
                'from_index': job.from_index,
                'to_index': job.to_index,
                'processing_duration_minutes': round((datetime.datetime.now(pytz.UTC) - job.updated_at).total_seconds() / 60, 2) if job.updated_at else None,
                'description': 'PLC Acknowledged' if job.plc_ack else ('Not Acknowledged by PLC' if job.plc_ack is False else 'PLC Ack Status Unknown')
            }
            healthcheck_data.append(job_detail)
        
        # Sort by processing duration (longest first)
        healthcheck_data.sort(key=lambda x: x['job_details']['processing_duration_minutes'] or 0, reverse=True)
        
        return common.StandardResponse.response(
            data=healthcheck_data
        )
        
@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsstation.route(api_routing.UrlPath.station_movements_index)
class StationMovementsIndexing(Resource):
    @nsstation.doc(description='To get the position of bin in a station.')
    @api.expect(req_parser.code_parser)
    def get(self):

        args = req_parser.code_parser.parse_args(strict=True)
        code = args['code']

        formated_bin_queue = db.st_mov_db_func.get_bin_queue_indexing(code)
        return common.StandardResponse.response(message = formated_bin_queue)

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsstation.route(api_routing.UrlPath.station_movements)
class StationMovements(Resource):
    @nsstation.doc(description='To get the job of a station.')
    @api.expect(req_parser.code_parser)
    def get(self):

        args = req_parser.code_parser.parse_args(strict=True)
        code = args['code']

        data = db.st_mov_db_func.get_station_movement(code)
        
        return common.StandardResponse.response(data = data)

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsstation.route(api_routing.UrlPath.station_history)
class StationHistory(Resource):
    @nsstation.doc(description='To get bin history of a staiton.')
    @api.expect(req_parser.code_parser)
    def get(self):

        args = req_parser.code_parser.parse_args(strict=True)
        code = args['code']

        data = db.st_order_db_func.get_station_bin_history(code)
        
        return common.StandardResponse.response(data = data)

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsstation.route(api_routing.UrlPath.station_errors)
class StationErrors(Resource):
    @nsstation.doc(description='To get station error and error details of a station.')
    @api.expect(req_parser.code_parser)
    def get(self):

        args = req_parser.code_parser.parse_args(strict=True)
        code = args['code']

        data = db.st_error_db_func.get_station_error_details(code)
        return common.StandardResponse.response(data = data)

station_record_parser = reqparse.RequestParser()
station_record_parser.add_argument("code",required = False,type = int,location = 'args')
station_record_parser.add_argument("from",required = True,type = str,location = 'args', help = "Ex : 2023-12-31 (YYYY-MM--dd)")
station_record_parser.add_argument("to",required = True,type = str,location = 'args', help = "Ex : 2023-12-31 (YYYY-MM--dd)")

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsstation.route(api_routing.UrlPath.station_records)
class StationRecords(Resource):
    @nsstation.doc(description='To get station records in a range of date. (Ex: st down time, st order count ...)')
    @api.expect(station_record_parser)
    def get(self):
        try:

            args = station_record_parser.parse_args(strict=True)
            from_date = args['from']
            to_date = args['to']
            station_code = args['code']

            from_date_obj = datetime.datetime.combine(datetime.datetime.strptime(from_date, '%Y-%m-%d'), datetime.time.min)
            to_date_obj = datetime.datetime.combine(datetime.datetime.strptime(to_date, '%Y-%m-%d'), datetime.time.max)

            result_arr = []

            def _seconds_converter(seconds:int)->str:
                minutes, seconds = divmod(seconds, 60)
                hours, minutes = divmod(minutes, 60)
                days,hours = divmod(hours,24)


                final_str = ""
                # Format the output string
                if days>0:
                    final_str += f'{int(days)} Day '
                if hours > 0:
                    final_str  += f'{int(hours)} Hours '
                if minutes > 0:
                    final_str += f'{int(minutes)} Minutes '
                if seconds >0:
                    final_str += f'{int(seconds)} Seconds'

                return final_str

            if station_code:
                st_list = [db.st_db_func.get_station_by_code(station_code)]
            else:
                st_list = db.st_db_func.get_all_station_list()
            for st in st_list:
                station_obj = {}
                station_obj['station_code'] = st.code
                # st_down_time
                st_down_record_list = db.st_record_db_func.get_st_record_by_date_type(st.code,from_date_obj,to_date_obj,ec.StationRecordType.DOWN.value)
                st_down_total_time = 0
                for st_down_record in st_down_record_list: #need to calculate according to date, cant use total seconds
                    query_start_time = max(st_down_record.start_time,from_date_obj)
                    if st_down_record.end_time:
                        query_end_time = min(st_down_record.end_time,to_date_obj)
                    else:
                        if datetime.datetime.now() > to_date_obj:
                            query_end_time = to_date_obj
                        else:
                            query_end_time = datetime.datetime.now()

                    intersection = (query_end_time-query_start_time).total_seconds()
                    st_down_total_time += intersection

                station_obj["station_down_count"] = len(st_down_record_list)
                station_obj["station_down_total_time"] = _seconds_converter(st_down_total_time) if st_down_total_time>0 else "0"
                # st_error
                st_error_list = db.st_error_db_func.get_station_error_by_date(st.code,from_date_obj,to_date_obj)
                station_obj["station_error_count"] = len(st_error_list)
                # st_enrollment
                # st_enroll_record_list = db.st_record_db_func.get_st_record_by_date_type(st.code,from_date_obj,to_date_obj,ec.StationRecordType.ENROLL.value)
                # st_enroll_total_time = 0 
                # for st_enroll_record in st_enroll_record_list:
                #     query_start_time = max(st_enroll_record.start_time,from_date_obj)
                #     if st_enroll_record.end_time:
                #         query_end_time = min(st_enroll_record.end_time,to_date_obj)
                #     else:
                #         if datetime.datetime.now() > to_date_obj:
                #             query_end_time = to_date_obj
                #         else:
                #             query_end_time = datetime.datetime.now()
                #     intersection = (query_end_time-query_start_time).total_seconds()
                #     st_enroll_total_time += intersection
                
                # station_obj["station_enroll_total_time"] = _seconds_converter(st_enroll_total_time) if st_enroll_total_time>0 else "0"
                num_of_bin_enroll = len(db.st_order_db_func.find_st_orders_by_type_date(st.code,ec.OrderType.ENROLL.value,from_date_obj,to_date_obj))
                station_obj["number_of_bin_enrolled"] = num_of_bin_enroll
                # st_order 
                st_order_list = db.st_order_db_func.find_st_orders_by_type_date(st.code,ec.OrderType.NORMAL.value,from_date_obj,to_date_obj)
                # st_order_total_time = sum((order.updated_at-order.created_at).seconds for order in st_order_list)
                # st_average_order_time = 0
                # if len(st_order_list):
                #     st_average_order_time = st_order_total_time/len(st_order_list)
                # station_obj["station_order_average_time"] = _seconds_converter(st_average_order_time) if st_average_order_time>0 else "0"
                station_obj["station_order_count"] = len(st_order_list)

                # special station
                if st.type == ec.CubeStationType.QC.value:
                    st_qc_order_list = db.st_order_db_func.find_st_orders_by_type_date(st.code,ec.OrderType.QC.value,from_date_obj,to_date_obj)
                    # st_qc_order_total_time = sum((qc_order.updated_at-qc_order.created_at).seconds for qc_order in st_qc_order_list)
                    # if len(st_qc_order_list):
                    #     st_order_total_time += st_qc_order_total_time
                    #     st_average_order_time = st_order_total_time/(len(st_order_list)+len(st_qc_order_list))
                    # station_obj["station_order_average_time"] = _seconds_converter(st_average_order_time) if st_average_order_time>0 else "0"
                    station_obj["station_order_count"] = (len(st_order_list)+len(st_qc_order_list))


                if st.type == ec.CubeStationType.BRIDGE.value:
                    bridge_order_list = db.st_order_db_func.find_st_orders_by_type_date(st.code,ec.OrderType.BRIDGE.value,from_date_obj,to_date_obj)
                    # st_bridge_total_time = sum((bridge_order.updated_at-bridge_order.created_at).seconds for bridge_order in bridge_order_list)
                    # st_average_bridge_time = 0 
                    # if len(bridge_order_list):
                    #     st_average_bridge_time = st_bridge_total_time/len(bridge_order_list)
                    # station_obj["station_bridge_average_time"] = _seconds_converter(st_average_bridge_time) if st_average_bridge_time>0 else "0"
                    station_obj["station_bridge_count"] = len(bridge_order_list)

                #bin processing time 
                # total_processing_time = 0 
                # total_processed_bin = 0 
                # st_order_list = db.st_order_db_func.find_st_orders_by_date(st.code,from_date_obj,to_date_obj)
                # for order in st_order_list:
                #     mtw_job = db.st_mov_db_func.find_job(dict(order_id=order.id,station_id=st.id,type=ec.CubeStationJobType.MOVE_TO_WORK.value))
                #     nb_job = db.st_mov_db_func.find_job(dict(order_id=order.id,station_id=st.id,type=ec.CubeStationJobType.NEXT_BIN.value))

                #     if mtw_job and nb_job:
                #         process_time = (nb_job.updated_at - mtw_job.updated_at).seconds
                #         total_processing_time += process_time
                #         total_processed_bin += 1
                
                # station_obj["station_average_bin_processing_time"] = _seconds_converter(total_processing_time/total_processed_bin) if total_processing_time>0 else "0"
                result_arr.append(station_obj)    
            
            return common.StandardResponse.response(data = result_arr)
        except ValueError:
            return common.StandardResponse.response(status = False,message="Invalid date format, should be in YY-mm-dd")


station_error_record_parser = reqparse.RequestParser()
station_error_record_parser.add_argument("code",required = False,type = int,location = 'args')
station_error_record_parser.add_argument("from",required = True,type = str,location = 'args', help = "Ex : 2023-12-31 (YYYY-MM--dd)")
station_error_record_parser.add_argument("to",required = True,type = str,location = 'args', help = "Ex : 2023-12-31 (YYYY-MM--dd)")
@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsstation.route(api_routing.UrlPath.station_error_records)
class StationErrorRecords(Resource):
    @nsstation.doc(description='To get station error records in a range of date')
    @api.expect(station_error_record_parser)
    def get(self):
        try:
            args = station_record_parser.parse_args(strict=True)
            from_date = args['from']
            to_date = args['to']
            station_code = args['code']

            from_date_obj = datetime.datetime.combine(datetime.datetime.strptime(from_date, '%Y-%m-%d'), datetime.time.min)
            to_date_obj = datetime.datetime.combine(datetime.datetime.strptime(to_date, '%Y-%m-%d'), datetime.time.max)

            result_arr = []

            if station_code:
                st_list = [db.st_db_func.get_station_by_code(station_code)]
            else:
                st_list = db.st_db_func.get_all_station_list()

            for st in st_list:
                st_error_record_list = db.st_error_db_func.get_station_error_by_date(st.code,from_date_obj,to_date_obj)
                for error in st_error_record_list:
                    result_arr.append(error.as_dict_for_record())
            
            return common.StandardResponse.response(data=result_arr)

        except ValueError:
            return common.StandardResponse.response(status = False,message="Invalid date format, should be in YY-mm-dd")


data_retrieval_parser = reqparse.RequestParser()
data_retrieval_parser.add_argument("zone",required = True, type = str, location = "args")
data_retrieval_parser.add_argument("date",required = True, type = str, location = "args", help = "Ex : 2023-12-31 (YYYY-MM--dd)")


@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsstation.route(api_routing.UrlPath.data_retrieval)
class DataRetrieval(Resource):
    @nsstation.doc(description='To collect data of station for ceratin date and zone.')
    @api.expect(data_retrieval_parser)
    def get(self):
        
        data = data_retrieval_parser.parse_args()

        zone = data['zone']
        # must be in format Y-M-d
        date = data['date']
    
        json_reply = db.st_db_func.data_retrieve(zone, date)

        return common.StandardResponse.response(model = json_reply)

station_job_done_error_parser = reqparse.RequestParser()
station_job_done_error_parser.add_argument("code",required = False,type = int,location = 'args')
station_job_done_error_parser.add_argument("date",required = True,type = str,location = 'args', help = "Ex : 2023-12-31 (YYYY-MM--dd)")

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsstation.route(api_routing.UrlPath.station_job_done_error_log)
class StationJobDoneErrorLog(Resource):
    @nsstation.doc(description='To get station job done error that affecting operation.')
    @api.expect(station_job_done_error_parser)
    def get(self):
        try:
            from file_read_backwards import FileReadBackwards
                
            data = station_job_done_error_parser.parse_args(strict=True)
            station_code = data['code']
            date = data['date']
            
            if station_code:
                header = f'{ec.ModuleCode.STATION.value},{station_code}'
            else:
                header = None

            date_obj = datetime.datetime.strptime(date, '%Y-%m-%d')
            date_obj = date_obj.strftime('%Y-%m-%d')
            
            logfile_name = 'cube_inner_error'    
            logfile_path_dir = common.PathConfiguration.get_directory_str()
            logfile_path = f'{logfile_path_dir}/CUBE-{date_obj}/{logfile_name}.log'
            
            return_data = []
            job_done_err_msg_set : Set[str] = set()
            
            with FileReadBackwards(logfile_path,encoding='utf-8') as frb:
                while True:
                    l = frb.readline()
                    if not l :
                        break
                    
                    if header:
                        if header not in l:
                            continue
                    
                    job_done_err_msg = {}
                    
                    split_info = l.replace('\n','').split(' - ')
                    err_reason = split_info[3].split(":")[0]
                    err_msg = split_info[3].split(":")[1]
                    
                    # avoid showing spamming msg error
                    if split_info[3] in job_done_err_msg_set:
                        continue
                    
                    job_done_err_msg['date'] = split_info[0]
                    job_done_err_msg['code'] = station_code if station_code else err_msg.split(',')[1]
                    job_done_err_msg['error_reason'] = err_reason
                    job_done_err_msg['error_msg'] = err_msg
                    
                    job_done_err_msg_set.add(split_info[3])
                    return_data.append(job_done_err_msg)
                    
                    if len(return_data) >= 15 :
                        return common.StandardResponse.response(data = return_data)
        except FileNotFoundError:
            return common.StandardResponse.response(False,message='File not found, could be no error yet.')
                
        return common.StandardResponse.response(data = return_data) 


station_log_parser = req_parser.code_parser.copy()
station_log_parser.add_argument("date",required = True,type = str,location = 'args', help = "Ex : 2023-12-31 (YYYY-MM--dd)")
station_log_parser.add_argument("from",required = True,type = str,location = 'args', help = "Ex : 14:20:40 if UTC time please - 8 hour")
station_log_parser.add_argument("to",required = True,type = str,location = 'args', help = "Ex : 14:20:40")
@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsstation.route(api_routing.UrlPath.station_log)
class StationLog(Resource):
    @nsstation.doc(description= 'To get station logging.')
    @api.expect(station_log_parser)
    def get(self):
        
        args = station_log_parser.parse_args(strict=True)
        station_code = args['code']
        date = args['date']
        from_t = args['from']
        to_t = args['to']

        def _format_msg_and_get_color(msg)->Tuple[str,str]:
            splited_msg = msg.split(':')
            if len(splited_msg) == 1: # warning message
                return splited_msg[0].strip(),'yellow'
            else:
                if splited_msg[0][0] == 'R':
                    return splited_msg[1].strip(),'blue'
                else:
                    return splited_msg[1].strip(),'cyan'


        from_dt_str = date + " " + from_t
        from_dt_object = datetime.datetime.strptime(from_dt_str, "%Y-%m-%d %H:%M:%S")
        from_dt_object = from_dt_object - datetime.timedelta(seconds=1)

        to_dt_str = date + " " + to_t
        to_dt_object = datetime.datetime.strptime(to_dt_str,"%Y-%m-%d %H:%M:%S")
        to_dt_object= to_dt_object + datetime.timedelta(seconds=1)


        # if mongo db 
        if Config.MONGO:
            from ....communications_provider.mongo import MongoClient

            from_timestamp = int(from_dt_object.timestamp())
            to_timestamp = int(to_dt_object.timestamp())

            plc_log = MongoClient.get_plc_log(station_code,from_timestamp,to_timestamp)
            plc_log_list = list(plc_log)

            for log in plc_log_list:
                message, color = _format_msg_and_get_color(log['message'])
                log['message'] = message
                log['color'] = color
                log['time'] = datetime.datetime.fromtimestamp(log['time']).strftime('%Y-%m-%d %H:%M:%S.%f')
                log.pop("_id")
                log.pop("expire_at")
                
            return_data = plc_log_list
        else:
            logfile_name = f'{ec.ModuleCode.STATION.value}{station_code}'
            logfile_path_dir = common.PathConfiguration.get_directory_str()
            logfile_path = f'{logfile_path_dir}/CUBE-{date}/{logfile_name}.log'

            return_data = []

            with open(logfile_path,encoding='utf-8') as frb:
                while True:
                    l = frb.readline()
                    if not l :
                        break
                    if l =='':
                        continue
                    if ',W,' in l:
                        continue

                    split_info = l.replace('\n','').split(' - ')
                    msg_dt_str_with_mili = split_info[0]
                    # takeout miliseconds
                    msg_dt_obj = datetime.datetime.strptime(msg_dt_str_with_mili, "%Y-%m-%d %H:%M:%S.%f")
                    if msg_dt_obj < from_dt_object or msg_dt_obj > to_dt_object:
                        continue

                    station_msg = {}

                    station_msg['time'] = msg_dt_str_with_mili

                    current_msg,color = _format_msg_and_get_color(split_info[3])

                    station_msg['message'] = current_msg
                    station_msg['color'] = color

                    return_data.append(station_msg)

        
        return common.StandardResponse.response(data=return_data)

shell_command_parser  = req_parser.station_code_body_parser.copy()
shell_command_parser.add_argument("command",required=True,type=str,location='json')
@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsstation.route(api_routing.UrlPath.shell_command)
class ShellCommand(Resource):
    @nsstation.doc(description='To send shell command to station.')
    @api.expect(shell_command_parser)
    def post(self):

        from ....service_handler import send_tcp_msg

        data = shell_command_parser.parse_args(strict=True)

        station_code = data.station_code
        command = data.command

        shell_command = f'{ec.ModuleCode.STATION.value},{station_code},{ec.CubesProtocol.SHELL.value},{command};'

        status = send_tcp_msg(station_code,shell_command)
        common.notify_shell(shell_command,status,False)

        return common.StandardResponse.response()
    

wms_dash_parser = reqparse.RequestParser()
wms_dash_parser.add_argument("station",required = False,type = int,location = 'args')
wms_dash_parser.add_argument("page",required = False,type = int,location = 'args', default = 1)
wms_dash_parser.add_argument("per_page",required = False,type = int,location = 'args', default =10)


@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsstation.route(api_routing.UrlPath.wms_dash)
class WMSDashboard(Resource):
    @nsstation.doc(description='Api for WMS dashbaord to get the station details.')
    @api.expect(wms_dash_parser)
    def get(self):
        import pytz
        from math import ceil

        args = wms_dash_parser.parse_args(strict = True)
        station_matrix_code = args['station']
        page = args['page']
        per_page = args['per_page']

        if station_matrix_code is not None:
            station_list = db.st_db_func.get_station_list_by_matrix_code(station_matrix_code)
        else:
            station_list = db.st_db_func.get_all_station_list()
        
        if not station_list:
            msg = f"No result found in db with station code = {station_matrix_code}"
            return common.StandardResponse.response(False,msg,metadata={'total_page':0})

        return_data = []
        station_set = set()
        for station in station_list:
            if station.matrix_code in station_set:
                # skip duplicate station matrix code
                continue

            station_data = {}

            station_data['matrix_code']=f"{station.matrix_code}"
            station_data['status'] = common.MatrixConverter.get_station_wms_status(station.code)

            if station_existing_error := db.st_error_db_func.get_existing_station_error_msg_list(station.code):
                time = station_existing_error[0].created_at
                err_description = []
                for error in station_existing_error:
                    err_description.append(error.data['data']['error_message'])
                station_data['error'] = {
                    'time':time.astimezone(pytz.utc).isoformat().replace('+00:00', 'Z'),
                    'error_description':err_description
                }
            else:
                station_data['error'] = None

            if station.type == ec.CubeStationType.I.value:
                station_data['bin_status'] = []
                _station_list = db.st_db_func.get_station_list_by_matrix_code(station.matrix_code)
                pre_bin , post_bin ,at = [], [], None
                for _station in _station_list:
                    pre_post_bin = db.st_order_db_func.get_bin_in_station_pre_post_processed(_station.code)
                    pre_bin.extend(pre_post_bin[0])
                    post_bin.extend(pre_post_bin[1])
                    if _station.bin_at_worker: at = _station.bin_at_worker 
                
                station_data['bin_status'] = {
                    "pre":pre_bin,
                    "at":at,
                    "post":post_bin
                }

            else:
                pre_post_bin = db.st_order_db_func.get_bin_in_station_pre_post_processed(station.code)
                station_data['bin_status'] = {
                    "pre":pre_post_bin[0],
                    "at":station.bin_at_worker,
                    "post":pre_post_bin[1]
                }
            station_set.add(station.matrix_code)
            return_data.append(station_data)


        total_items = len(return_data)
        total_pages = ceil(total_items / per_page)

        start_index = (page - 1) * per_page
        end_index = start_index + per_page

        paginated_data = return_data[start_index:end_index]
        return common.StandardResponse.response(data=paginated_data,metadata = {
                "total_page": total_pages})
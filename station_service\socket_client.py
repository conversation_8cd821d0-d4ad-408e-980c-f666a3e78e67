import sys
import socketio
import time

sio = socketio.Client()

@sio.event
def disconnect():
    print("I'm disconnected!")

@sio.on("order-error")
def ordererror(data):
    print(data)
    print(f"I received a message!' {data} via order_error.")

@sio.event
def message(data):
    print(f"I received a message! {data} data via message")

@sio.event
def test(sid):
    print(f"I received a message!' via test {sid}")

@sio.on("mediator-init")
def on_test(sid,data):
    print(f"I received a message!' {data} via mediator-init {sid}")
    
    
@sio.on('bin-arrival',namespace='/station')
def on_bin_arrival(data):
    print(data["station_id"])
    print(data["storage_code"])
    
    return {"station_id":13,"request_id":data["request_id"]}

@sio.on('junction_arrival',namespace='/station')
def on_bin_arrival(data):
    data = data['data']
    print(data['request_id'])
    print(data["station_code"])
    print(data["storage_code"])
    
    return {"station_code":data["station_code"],"storage_code":data['storage_code'],"request_id":data["request_id"],"to_index":26}

@sio.on('work_arrival',namespace='/station')
def on_work_arrival(data):
    data = data['data']
    print(data['request_id'])
    print(data["station_code"])
    print(data["storage_code"])
    
    return {"station_code":int(data["station_code"]),"storage_code":data['storage_code'],"request_id":data["request_id"],"handle_at_station":True}

# @sio.on('*')
# def catch_all(event, data):
#     print(event)
#     print(data)

@sio.event
def connect():
    print("I'm connected!")    
    

if __name__ == '__main__':
    # sio.connect('http://127.0.0.1:4050',namespaces=['/station'])
    sio.connect('http://127.0.0.1:4050/station')
    if sio.connected:
        time.sleep(0.5)
        data = {"entity": "SM"}
        i = 0 
        sio.emit("init",data, '/station')
        print(f"Emit init {sio.sid}")

 
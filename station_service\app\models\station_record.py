from typing import Dict, List
from dataclasses import dataclass

from .. import db


@dataclass
class StationRecord(db.Model):
    id: int
    station_code:int
    type:str
    start_time:str
    end_time:str
    total_seconds:float
    created_at:str
    updated_at:str
    

    __tablename__ = 'station_record'

    id = db.Column(
        db.Integer,
        primary_key=True)
    
    station_code = db.Column(
        db.Integer,
        index=False,
        nullable=False)

    type = db.Column(
        db.String(20),
        index=False,
        unique=False,
        nullable=True)

    start_time = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

    end_time = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True,
        default = None
    )

    total_seconds = db.Column(
        db.Float,
        index=False,
        unique=False,
        nullable=True,
        default = None
    )

    created_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

    updated_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

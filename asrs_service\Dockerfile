FROM python:3.11-alpine

WORKDIR /app

COPY requirements.txt .

RUN mkdir -p -m 0700 ~/.ssh && ssh-keyscan github.com >> ~/.ssh/known_hosts
RUN python -m pip install --upgrade pip
RUN \
 apk add --no-cache postgresql-libs && \
 apk add --no-cache --virtual .build-deps gcc musl-dev postgresql-dev && \
 apk --purge del .build-deps
RUN --mount=type=ssh pip3 install -r requirements.txt --no-cache-dir


COPY . .

CMD ["python3", "-u", "-m", "asrs.wsgi"]

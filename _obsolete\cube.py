    # #! Danger api, will delete whole StationMovements table
    # def reset():
            
    #     db.st_mov_db_func.clear_jobs()
    #     station_list = db.st_db_func.get_active_station_code_list()
    #     for station in station_list:
    #         msg_to_send = f"ST,{station},Z;"
    #         station_server.sendMsg(f'ST{station}', msg_to_send)

    #     return common.StandardResponse.response()



# def station_sync():
    #     '''
    #     bins variable contains a dictionary based on index of station. 
    #     Can either be empty '' to define no bin or can contain storage code.           
    #     bins = {
    #         '0': '',
    #         '1': '0002',
    #         '2': '0432',
    #         '3': '9623',
    #         '4': '4673',
    #         '5': '6923'
    #     }
    #     '''  
    #     global cyclestop_station
    #     station_service = StationEHServices()
        
    #     data = request.get_json()
    #     route_log.info(blue(f'Received - station_sync : ')+f'{data}')
    #     station_id = int(data['station_id'])
    #     station = st_db_func.get_station_by_code(station_id)
    #     bins = data['bins']
    #     route_log.info(yellowBright(f'Station sync triggered for station {station_id}. Proceeding to sync up bins'))
    #     cyclestop_station = True
    #     affected_jobs = station_service.generate_affected_jobs(station)
    #     # manual_override_station_jobs(station_id)
    #     station_service.sync_station(bins,station,affected_jobs)
        
    #     return common.StandardResponse.successResponse()

    # def inform_change():
    
    #     data = request.get_json()
    #     route_log.info(blue(f'Received - inform-change: ')+f'{data}')
        
    #     station_code = data['station_code']
    #     zone = data['zone']
    #     type = data['type']
    #     rotation = data['rotation']
    #     adjacent = data['adjacent']
    #     inner_drop = data['inner_drop']
    #     inner_pick = data['inner_pick']
    #     outer_drop = data['outer_drop']
    #     outer_pick = data['outer_pick']
    #     worker = data['worker']
    #     is_deleted = data['is_deleted']
        
    #     station = db.st_db_func.get_station_by_code(int(station_code))
    #     if station:
    #         #Update station/ soft delete
    #         db.st_db_func.wcs_update_station_details(station_code,zone,type,rotation,adjacent,inner_drop,inner_pick,outer_drop,outer_pick,worker, is_deleted)
    #     else:
    #         #create new station row
    #         db.st_db_func.create_station(station_code,zone,type,rotation,adjacent,inner_drop,inner_pick,outer_drop,outer_pick,worker)
    #     return common.StandardResponse.successResponse()

    # def modify_station():
    
    #     data = request.get_json()
    #     route_log.info(blue('Received - modify_station : ')+f'{data}')
        
    #     station_code = int(data['station_code'])
    #     action = data['action']
    #     if action == enum_collection.ModifyStationAction.START.value:
    #         pass
    #     elif action == enum_collection.ModifyStationAction.STOP.value:
    #         msg = station_server.force_disconnect(station_code)
    #         return msg
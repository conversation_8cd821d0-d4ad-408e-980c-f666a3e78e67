from termcolor import colored

from .. import database as db,models as model,common as common,service_handler as handler,enum_collections as ec
from ..blueprints.route.client import MedHttpClient


asrs_error_handling_log = common.get_logger_console('asrs_error_handling')
 
@common.decorateAllFunctionInClass(common.log_and_suppress_error(asrs_error_handling_log))
class ASRSEH:
        
    def process_eh_job(self,job_id:int, pc_id:int):
        """specially to handle when hcc receive null job done 'R' message from PC
        While loop to send pick message to plc to scan the pallet id and recover
        Normally run in a thread

        Args:
            job_id (int): the error handling job with type "PC_LOST_DATA"
            pc_id (int): the pc_id who send null done message to hcc 
        """
        
        job = db.as_job_db_func.get_job_by_id(job_id)
        if job:
            asrs_id = pc_id
            msg = f'{ec.ModuleCode.ASRS.value},{asrs_id},P,{job.id},I{pc_id},0,0;'
            db.as_job_db_func.update_job_msg(job_id,msg)
            error_job = handler.ErrorJobHandler(job_id,asrs_id)   
            error_job.start_error_job()     
        else:
            asrs_error_handling_log.error(colored(f'No pc lost data job found for job id {job_id}','light_red'))

    async def recovery_pick(self,picked_pallet:str, order_id:int, order_asrs_id:int, error:bool):
        """Create a drop job to put recovered pallet back based on who triggered the error.
            If error = True then it is triggered by PLC
            if error = False then it is triggered by HWX validate incorrect pallet picked.
            Spawn, send and monitor drop job to complete

        Args:
            picked_pallet (str): the pallet pick by the asrs
            order_id (int): the order id that throw error
            order_asrs_id (int): the asrs id that handle the order
            error (bool): if true mean triggered by plc else triggerd by hwx
        """    
        
        if f'{ec.ModuleCode.ASRS.value}{order_asrs_id}' not in handler.asrs_in_recovery:
            handler.asrs_recovery(order_asrs_id)
            
            
            data = await MedHttpClient.request_location(picked_pallet)
            x = int(data['x'])
            y = int(data['y'])
            z = int(data['z'])

            drop_job_id = db.as_job_db_func.create_recovery_drop_job(order_id, picked_pallet, error)
            bay, level, side = handler.ASRSLogic.bay_level_mapper(x, y, z)
            msg = f'AS,{order_asrs_id},D,{drop_job_id},{picked_pallet},{side},{level},{bay};'
            db.as_job_db_func.update_job_msg(drop_job_id,msg)
            recovery_job = handler.ErrorJobHandler(drop_job_id,order_asrs_id)
            recovery_job.start_error_job() 
        else:
            asrs_error_handling_log.warning(colored(f'Not able to start recovery pick cause ASRS {order_asrs_id} is under recovery','light_yellow'))

    async def handle_asrs_error_code(self,msg:str):
        """ 
        err1 will inform mediator FE + cancel job , engineer recover pallet back to original rack.
        err2 will trigger recovery flow , hcc create drop job to instruct ASRS store pallet back to original rack.
        err3 will inform mediator FE , pure notification.

        input : AS,1,P,JJJJ,XXXXXX,ERR2 
            output :  {
                        "msg" :msg ,
                        "order_id" :error_job.order_id, 
                        "asrs_id" : order.asrs_id,
                        "pallet_id" : error_job.pallet_id,
                        "error_level" :ec.AsrsRecoveryTriggers.ERR1.value ,
                        "error_desc" :ec.AsrsErrorDesc.ERR1.value
                    }

        refer https://pentamaster.sharepoint.com/:x:/s/Pingspace/EXB_Ku3RIGBIqBvYKF7k4kQBZ0zoZLwzjmx003sMP2tUXw?e=5cwlbX         

        Args:
            msg (str): msg that send from plc that contain err in the msg

        """
        
        splited_msg = msg.split(',')
        module = splited_msg[0]
        plc_id = int(splited_msg[1])
        

        # ERR3
        if ec.AsrsRecoveryTriggers.ERR3.value in msg :
            jsonPayload = common.JsonPayload.informErrorPayload(msg,ec.AsrsRecoveryTriggers.ERR3.value,ec.AsrsErrorDesc.ERR3.value,plc_id, domain = module)
            await MedHttpClient.update_error(jsonPayload)
            common.saveNotification(title = f"ASRS Station Error",
                                    module=ec.ModuleCode.ASRS.value,
                                    Device = f'{module}{plc_id}',
                                    Message = ec.Notify.ERR3.value
                                    )
            return jsonPayload
        
        else:
            error_job:model.Asrs_job = db.as_job_db_func.get_job_by_id(int(splited_msg[3]))

            if error_job == None:
                eMessage = f'Error job not exist. Received:{msg}'
                asrs_error_handling_log.error(colored(eMessage,'light_red'))
                return
            else :
                
                asrs_error_handling_log.warning(colored(f'ASRS has error. Message given: {msg}','light_yellow'))
                order :model.Asrs_order  = db.as_order_db_func.get_order(error_job.order_id, True)

                # ERR1
                if ec.AsrsRecoveryTriggers.ERR1.value in msg:
                    db.as_job_db_func.set_job_error_for_order(error_job.order_id)
                    handler.free_asrs(int(splited_msg[1]))
                    await MedHttpClient.cancel_job(error_job.pallet_id)
                    
                    jsonPayload = common.JsonPayload.informErrorPayload(msg,ec.AsrsRecoveryTriggers.ERR1.value,ec.AsrsErrorDesc.ERR1.value,order.asrs_id,order.order_id,order.station_id,error_job.pallet_id)
                    await MedHttpClient.update_error(jsonPayload)
                    common.saveNotification(title = f"ASRS Station Error",
                                            module=ec.ModuleCode.ASRS.value,
                                            Device = f'{module}{plc_id}',
                                            Message = ec.Notify.ERR1.value
                                            )
                    return jsonPayload
                        
                # ERR2
                elif ec.AsrsRecoveryTriggers.ERR2.value in msg:
                        
                    await self.recovery_pick(error_job.pallet_id, order.id,order.asrs_id, True)
                    
                    jsonPayload = common.JsonPayload.informErrorPayload(msg,ec.AsrsRecoveryTriggers.ERR2.value,ec.AsrsErrorDesc.ERR2.value,order.asrs_id,order.order_id,order.station_id,error_job.pallet_id)
                    await MedHttpClient.update_error(jsonPayload)
                    common.saveNotification(title = f"ASRS Station Error",
                                            module=ec.ModuleCode.ASRS.value,
                                            Device = f'{module}{plc_id}',
                                            Message = ec.Notify.ERR2.value
                                            )
                    return jsonPayload
        
                else:
                    asrs_error_handling_log.error(colored(f'Invalid error command {msg}','light_red'))
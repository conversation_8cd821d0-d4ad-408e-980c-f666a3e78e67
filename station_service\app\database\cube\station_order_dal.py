import datetime

from typing import List,Tuple
from sqlalchemy import cast, desc, Date

from ... import app,DBMonitor,db
from ... import common as common,models as model,enum_collections as ec

db_log = common.LogManager('cube_db_error',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_log))
class StationOrderSQLAlchemyQueries:
        
    @DBMonitor.retry_session
    def add_new_order(self,data:dict)->int:
        with app.app_context():
            st_order = model.StationOrder(
                station_code = data.get('station_code'),
                storage_code = data.get('storage_code'),
                tc_order_id = data.get('tc_order_id'),
                type = data.get('type'),
                is_processed=data.get('is_processed'),
                status=ec.OrderStatus.PROCESSING.value
            )
            curr_session = db.session
            curr_session.add(st_order)
            curr_session.commit()

            order_id = st_order.id 

            return order_id

    @DBMonitor.retry_session
    def find_order(self,filter_dict:dict)->model.StationOrder:
        with app.app_context():
            curr_session = db.session
            st_order = curr_session.query(model.StationOrder).filter_by(**filter_dict).first()
            return st_order
    
    @DBMonitor.retry_session
    def find_latest_order(self,filter_dict:dict)->model.StationOrder:
        with app.app_context():
            curr_session = db.session
            st_order = curr_session.query(model.StationOrder).filter_by(**filter_dict).order_by(desc('created_at')).first()
            return st_order
        
    @DBMonitor.retry_session
    def find_orders(self,filter_dict:dict)->List[model.StationOrder]:
        with app.app_context():
            curr_session = db.session
            st_orders = curr_session.query(model.StationOrder).filter_by(**filter_dict).all()
            return st_orders
        
    @DBMonitor.retry_session
    def find_active_order_join_movement(self,filter_dict:dict)->List[Tuple[model.StationOrder,model.StationMovement]]:
        with app.app_context():
            curr_session = db.session
            order_join_movement = curr_session.query(model.StationOrder,model.StationMovement)\
                                              .join(model.StationOrder,(model.StationOrder.id == model.StationMovement.order_id))\
                                              .filter_by(**filter_dict)\
                                              .order_by(model.StationMovement.created_at)\
                                              .all()
            return order_join_movement


    @DBMonitor.retry_session
    def find_st_orders_by_date(self,station_code:int,from_date:datetime.datetime,to_date:datetime.datetime)->List[model.StationOrder]:
        with app.app_context():
            curr_session = db.session
            orders = curr_session.query(model.StationOrder).filter(cast(model.StationOrder.created_at,Date)>= from_date)\
                                                                .filter(cast(model.StationOrder.created_at,Date)<= to_date)\
                                                                .filter_by(station_code=station_code,status=ec.OrderStatus.COMPLETED.value)\
                                                                .all()
            return orders
        
    @DBMonitor.retry_session
    def find_st_orders_by_type_date(self,station_code:int,type:ec.OrderType,from_date:datetime.datetime,to_date:datetime.datetime)->List[model.StationOrder]:
        with app.app_context():
            curr_session = db.session
            orders = curr_session.query(model.StationOrder).filter(cast(model.StationOrder.created_at,Date)>= from_date)\
                                                                .filter(cast(model.StationOrder.created_at,Date)<= to_date)\
                                                                .filter_by(station_code=station_code,type=type,status=ec.OrderStatus.COMPLETED.value)\
                                                                .all()
            return orders
    
    @DBMonitor.retry_session
    def find_bin_history_today(self,storage_code:str,today_date:datetime.date)->List[model.StationOrder]:
        with app.app_context():
            curr_session = db.session
            orders = curr_session.query(model.StationOrder).filter(cast(model.StationOrder.created_at,Date)==today_date)\
                                                                .filter_by(storage_code=storage_code)\
                                                                .order_by(desc(model.StationOrder.created_at))\
                                                                .all()
            return orders
    
    @DBMonitor.retry_session
    def find_station_bin_history_today(self,station_code:int,today_date:datetime.date)->List[model.StationOrder]:
        with app.app_context():
            curr_session = db.session
            orders = curr_session.query(model.StationOrder).filter(cast(model.StationOrder.created_at,Date)==today_date)\
                                                                .filter_by(station_code=station_code)\
                                                                .filter(model.StationOrder.status.in_([ec.OrderStatus.COMPLETED.value,ec.OrderStatus.DELETED.value]))\
                                                                .order_by(desc(model.StationOrder.created_at))\
                                                                .all()
            return orders
    @DBMonitor.retry_session
    def update_order(self,filter_dict:dict,update:dict)->int:
        with app.app_context():
            curr_session = db.session
            order = curr_session.query(model.StationOrder).filter_by(**filter_dict).first()
            if order:
                for k,v in update.items():
                    order.__setattr__(k,v)
                # order.updated_at = datetime.datetime.now()
                order_id = order.id
                curr_session.commit()
                return order_id
            else:
                db_log.error(f'No station order with {str(filter_dict)} found')
                return None



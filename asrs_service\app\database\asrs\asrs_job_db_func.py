import datetime

from typing import <PERSON><PERSON>
from termcolor import colored

from config import Config
from ...models import Asrs_job
from ... import common as common, enum_collections as ec
from .asrs_job_dal import AsrsJobSQLAlchemyQueries

db_func_log = common.get_logger_console('asrs_db_func')

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_func_log))
class AsrsJobDBFunc:
    
    asrs_job_dal = AsrsJobSQLAlchemyQueries()
    
    def generate_jobs(self,asrs_order_id: int):
        
        from . import as_station_db_func,as_order_db_func
        from ...service_handler import ASRSLogic
        
        order_id = asrs_order_id
        order = as_order_db_func.get_order(asrs_order_id,True)
        bay, level, side = ASRSLogic.bay_level_mapper(order.x, order.y, order.z)
        station_type : str = as_station_db_func.get_station_type_by_id(order.station_id)
       
        if order.method == common.ec.AsrsMethod.GET.value:
            
            pick_job_id = self.asrs_job_dal.create_job(
                order_id,
                ec.AsrsJobType.PICK.value,
                ec.OrderStatus.AVAILABLE.value,
                order.pallet_id
            )
            destination_module = side
            
            msg= f'{ec.ModuleCode.ASRS.value},{order.asrs_id},P,{pick_job_id},{destination_module},{level},{bay};'
            self.asrs_job_dal.update_job(pick_job_id,dict(msg=msg))


            drop_job_id = self.asrs_job_dal.create_job(
                order_id,
                ec.AsrsJobType.DROP.value,
                ec.OrderStatus.AVAILABLE.value,
                order.pallet_id,
                pred_id=pick_job_id
            )
            if station_type == ec.AsrsStationType.LIFTER_STATION.value or station_type == ec.AsrsStationType.QC_STATION.value:
                destination_module = f'O{order.asrs_id}'
                if Config.MOCK_ASRS_ORDER is True:
                    destination_module = f'I{order.asrs_id}'
            elif station_type == ec.AsrsStationType.BREAKBULK_STATION.value:
                destination_module = f'B{order.asrs_id}'
            
            msg = f'{ec.ModuleCode.ASRS.value},{order.asrs_id},D,{drop_job_id},{order.pallet_id},{destination_module},{level},{bay};'
            self.asrs_job_dal.update_job(drop_job_id,dict(msg=msg))


            move_job_id = self.asrs_job_dal.create_job(
                order_id,
                ec.AsrsJobType.MOVE.value,
                ec.OrderStatus.AVAILABLE.value,
                order.pallet_id,
                pred_id=drop_job_id
            )
            
            if station_type == ec.AsrsStationType.LIFTER_STATION.value:
                curr_location = f'A{order.asrs_id}O'
                to_location = f'P1'
            elif station_type == ec.AsrsStationType.BREAKBULK_STATION.value:
                curr_location = f'A{order.asrs_id}B'
                to_location = f'A{order.asrs_id}BW'
            elif station_type == ec.AsrsStationType.QC_STATION.value:
                curr_location = f'A{order.asrs_id}O'
                to_location = f'QC1'
                
            pallet_conveyor_id = ASRSLogic.get_pc_id(str(order.asrs_id), station_type == ec.AsrsStationType.BREAKBULK_STATION.value )

            msg = f'{ec.ModuleCode.PalletConveyor.value},{pallet_conveyor_id},D,{move_job_id},{curr_location},{order.pallet_id},{to_location};'
            self.asrs_job_dal.update_job(move_job_id,dict(msg=msg))


        elif order.method == ec.AsrsMethod.PUT.value:
            
            move_job_id = self.asrs_job_dal.create_job(
                order_id,
                ec.AsrsJobType.MOVE.value,
                ec.OrderStatus.AVAILABLE.value,
                order.pallet_id
            )
            
            if station_type == ec.AsrsStationType.LIFTER_STATION.value:
                curr_location = f'P1'
                to_location = f'A{order.asrs_id}I'
            elif station_type == ec.AsrsStationType.BREAKBULK_STATION.value:
                curr_location = f'A{order.asrs_id}BW'
                to_location = f'A{order.asrs_id}B'
            elif station_type == ec.AsrsStationType.QC_STATION.value:
                curr_location = f'QC1'
                to_location = f'A{order.asrs_id}I'
                
            pallet_conveyor_id = ASRSLogic.get_pc_id(str(order.asrs_id),station_type == ec.AsrsStationType.BREAKBULK_STATION.value)

            msg = f'{ec.ModuleCode.PalletConveyor.value},{pallet_conveyor_id},D,{move_job_id},{curr_location},{order.pallet_id},{to_location};'
            self.asrs_job_dal.update_job(move_job_id,dict(msg=msg))


            pick_job_id = self.asrs_job_dal.create_job(
                order_id,
                ec.AsrsJobType.PICK.value,
                ec.OrderStatus.AVAILABLE.value,
                order.pallet_id,
                pred_id=move_job_id
            )
            
            if station_type == ec.AsrsStationType.LIFTER_STATION.value or station_type == ec.AsrsStationType.QC_STATION.value:
                destination_module = f'I{order.asrs_id}'
            elif station_type == ec.AsrsStationType.BREAKBULK_STATION.value:
                destination_module = f'B{order.asrs_id}'

            msg= f'{ec.ModuleCode.ASRS.value},{order.asrs_id},P,{pick_job_id},{destination_module},{level},{bay};'
            self.asrs_job_dal.update_job(pick_job_id,dict(msg=msg))


            drop_job_id = self.asrs_job_dal.create_job(
                order_id,
                ec.AsrsJobType.DROP.value,
                ec.OrderStatus.AVAILABLE.value,
                order.pallet_id,
                pred_id=pick_job_id
            )
            
            destination_module = side
            
            msg = f'{ec.ModuleCode.ASRS.value},{order.asrs_id},D,{drop_job_id},{order.pallet_id},{destination_module},{level},{bay};'
            self.asrs_job_dal.update_job(drop_job_id,dict(msg=msg))
            
    def create_recovery_drop_job(self,order_id:int, recovery_pallet_id:str, error= False)->int:
        if error is False:
            trigger = ec.AsrsRecoveryTriggers.WRONG_PALLET.value
        else:
            trigger = ec.AsrsRecoveryTriggers.ERR2.value

        drop_job_id = self.asrs_job_dal.create_job(
            order_id,
            ec.AsrsJobType.RECOVERY_DROP.value,
            ec.OrderStatus.AVAILABLE.value,
            recovery_pallet_id,
            triggered_by=trigger,
        )
        return drop_job_id
    
    def create_pc_lost_data_job(self)->int:
        if not self.check_pc_lost_data_job_exist():
            job_id = self.asrs_job_dal.create_job(
                None,
                ec.AsrsJobType.PC_LOST_DATA.value,
                ec.OrderStatus.AVAILABLE.value,
                None
            )
            return job_id
        else:
            return None
        
    def get_job_by_id(self,id:int)->Asrs_job:
        return self.asrs_job_dal.get_job_by_id(id)
    
    def get_jobs_by_order_id(self,order_id:int)->Asrs_job:
        job = self.asrs_job_dal.get_jobs_by_order_id(order_id)
        if job == None:
            db_func_log.error(f'No job with order id {id} found.')
        return job
    
    def get_succ_job(self,job_id:int):
        job = self.asrs_job_dal.get_succ_job(job_id)
        if job == None:
            db_func_log.error(f'No successor foudn for job with id {job_id} .')
        return job
    
    def get_active_jobs_by_order_id(self,order_id:int):
        return self.asrs_job_dal.get_active_jobs_by_order_id(order_id)
    
    def get_earliest_job_for_order(self,order_id:int)->Asrs_job:
        return self.asrs_job_dal.get_earliest_job_by_order_id(order_id)
    
    def get_processing_error_jobs(self):
        return self.asrs_job_dal.get_processing_error_jobs()
    
    def check_pc_lost_data_job_exist(self)->bool:
        job = self.asrs_job_dal.get_available_job_by_type(ec.AsrsJobType.PC_LOST_DATA.value)
        return True if job else False
    
    def check_pred_job_completed(self,pred_id:int)->bool:
        if pred_id == None:
            return True
        else:
            pred_job = self.get_job_by_id(pred_id)
            if pred_job:
                if pred_job.job_status == ec.OrderStatus.COMPLETED.value:
                    return True
                else:
                    return False
            else:
                db_func_log.error(colored(f'No job found ofr job id {pred_id}','light_red'))
                return False
    
    def check_all_job_completed_for_order(self,order_id:int)->bool:
        jobs = self.get_jobs_by_order_id(order_id)
        for job in jobs:
            if job.job_status != ec.OrderStatus.COMPLETED.value and job.job_status != ec.OrderStatus.CRITICAL_ERROR.value:
                return False
        return True

    def sync_pc_lost_data_job(self,pallet_id:str,err_job_id:int)->Tuple[bool,int]:
        move_job_without_data :Asrs_job = self.asrs_job_dal.get_lost_data_job(pallet_id)
        pc_lost_data_job : Asrs_job = self.asrs_job_dal.get_job_by_id(err_job_id)
        if move_job_without_data is None:
            raise Exception(f'No processing move job found with pallet_id {pallet_id}')
        # PUT ORDER
        if move_job_without_data.pred_id is None:
            ''' Flag all related jobs to this pallet into correct state to sync up with actual Hardware.
                Find back the pallet id, finish off PC job as well as pick job as ASRS already holding the pallet,
                Proceed with drop into rack job'''
            
            self.complete_job(pc_lost_data_job.id)
            self.complete_job(move_job_without_data.id)
            

            succ_job = self.get_succ_job(move_job_without_data.id)
            if succ_job:
                self.complete_job(succ_job.id)
            return False, move_job_without_data.order_id
        # GET ORDER
        else:
            self.complete_job(move_job_without_data.id)
            return True, move_job_without_data.order_id

    def ack_job(self,job_id:int):
        self.asrs_job_dal.update_job(job_id,dict(ack=True))
    
    def processing_job(self,job_id:int):
        self.asrs_job_dal.update_job(job_id,dict(job_status = ec.OrderStatus.PROCESSING.value))
    
    def complete_job(self,job_id:int):
        self.asrs_job_dal.update_job(job_id,dict(job_status = ec.OrderStatus.COMPLETED.value))
        
    def update_job_msg(self,job_id:int, msg :str):
        self.asrs_job_dal.update_job(job_id,dict(msg = msg))
    
    def reset_job(self,job_id:int):
        self.asrs_job_dal.update_job(job_id,dict(
            job_status = ec.OrderStatus.AVAILABLE.value,
            ack = False,
            ))
        
    def redo_asrs_pick(self,order_id:int):
        pick_job_for_order = self.asrs_job_dal.get_job_by_order_id_type(order_id,ec.AsrsJobType.PICK.value)
        self.reset_job(pick_job_for_order.id)

    def set_job_error_for_order(self,order_id:int):
        jobs = self.asrs_job_dal.get_jobs_by_order_id(order_id)
        for job in jobs:
            self.asrs_job_dal.update_job(job.id,dict(job_status = ec.OrderStatus.CRITICAL_ERROR.value))

    def check_response_interval(self):
        interval = datetime.timedelta(minutes = 1)
        return self.asrs_job_dal.check_jobs_interval(interval,True)
    
    def check_ack_interval(self):
        interval = datetime.timedelta(seconds = 60)
        return self.asrs_job_dal.check_jobs_interval(interval, False)

from .key_pair_dal import KeyPairSQLAlchemyQueries

class KeyPairSDBFunc:

    key_pair_dal = KeyPairSQLAlchemyQueries()

    def create_keypair(self,private_key_pem_bytes:bytes,public_key_pem_bytes:bytes):
        self.key_pair_dal.create_keypair(private_key_pem_bytes,public_key_pem_bytes)

        return True
    
    def get_keypair(self):
        keypair = self.key_pair_dal.get_keypair()
        return keypair


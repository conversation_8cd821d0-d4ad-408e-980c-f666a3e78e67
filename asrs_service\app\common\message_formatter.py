# import app.database as db
# import app.models as model
# import app.enum_collections as ec


# class MsgFormatter:

#     module_code = None

#     @classmethod
#     def get_full_msg(cls,station_code:int,job:model.StationMovement):
#         if job.type == ec.CubeStationJobType.DOOR_OPEN.value:
#             return cls.open_door_msg(station_code,job.id)
#         if job.type == ec.CubeStationJobType.MOVE_TO_WORK.value :
#             order = db.st_order_db_func.find_order(dict(id=job.order_id))
#             if order.type == ec.OrderType.BRIDGE.value:
#                 return cls.bridge_msg(station_code,job.id,job.from_index,job.to_index,'C',job.bin_no)
#         if job.type == ec.CubeStationJobType.LIGHT_UP.value :
#             return cls.light_up_msg(station_code,job.bin_no)
#         if job.type == ec.CubeStationJobType.LIGHT_DOWN.value :
#             return cls.light_down_msg(station_code,job.bin_no)
        
#         return cls.move_msg(station_code,job.id,job.from_index,job.to_index,'C',job.bin_no)

#     @classmethod
#     def heartbeat_reply_msg(cls,station_code:int)->str:
#         return f'{cls.module_code},{station_code},{ec.CubesProtocol.H.value},{ec.HeartBeat.HEARTBEAT_REPLY.value};'
    
#     @classmethod
#     def initialize_msg(cls,station_code:int,bin_status:str)->str:
#         return f'{cls.module_code},{station_code},Initialize,{bin_status};'
    
#     @classmethod
#     def pairing_reply_msg(cls,station_code:int)->str:
#         return f'{cls.module_code},{station_code},{ec.CubesProtocol.P.value},SUCCESS;'
    
#     @classmethod
#     def recovery_status_msg(cls,station_code:int,plc_status:str)->str:
#         return f'{cls.module_code},{station_code},{ec.CubesProtocol.P.value},RECOVERY,{plc_status};'

#     @classmethod
#     def status_msg(cls,station_code:int)->str:
#         return f'{cls.module_code},{station_code},{ec.CubesProtocol.S.value};'

#     @classmethod
#     def bridge_msg(cls,station_code:int,job_id:int,job_from_index:int,job_to_index:int,rotation:str,job_bin_no:str)->str:
#         return f'{cls.module_code},{station_code},{ec.CubesProtocol.B.value},{job_id},{job_from_index}|{job_to_index}|{rotation}|{job_bin_no};'

#     @classmethod
#     def move_msg(cls,station_code:int,job_id:int,job_from_index:int,job_to_index:int,rotation:str,job_bin_no:str)->str:
#         return f'{cls.module_code},{station_code},{ec.CubesProtocol.M.value},{job_id},{job_from_index}|{job_to_index}|{rotation}|{job_bin_no};'
    
#     @classmethod
#     def job_done_msg(cls,station_code:int,job_id:int,job_from_index:int,job_to_index:int,rotation:str,job_bin_no:str)->str:
#         return f'{cls.module_code},{station_code},{ec.CubesProtocol.J.value},{job_id},{job_from_index}|{job_to_index}|{rotation}|{job_bin_no};'
    
#     @classmethod
#     def open_door_msg(cls,station_code:int,job_id:int)->str:
#         return f'{cls.module_code},{station_code},{ec.CubesProtocol.DO.value},{job_id};'

#     @classmethod
#     def gw_request_msg(cls,staiton_code:int,tc_job_id:int,index:int,type:str,bin_no:str)->str:
#         return f'{cls.module_code},{staiton_code},{ec.CubesProtocol.R.value},{tc_job_id},{index}|{type}|{bin_no};'
    
#     @classmethod
#     def gw_cancel_request_msg(cls,staiton_code:int,tc_job_id:int,index:int,type:str,bin_no:str)->str:
#         return f'{cls.module_code},{staiton_code},{ec.CubesProtocol.CR.value},{tc_job_id},{index}|{type}|{bin_no};'

#     @classmethod
#     def gw_update_msg(cls,station_code:int,tc_job_id:int,index:int,type:str,bin_no:str):
#         return f'{cls.module_code},{station_code},{ec.CubesProtocol.U.value},{tc_job_id},{index}|{type}|{bin_no};'
    
#     @classmethod
#     def weight_msg(cls,station_code:int,bin_no:str):
#         return f'ST,{station_code},{ec.CubesProtocol.W.value},{bin_no};'

#     @classmethod
#     def enroll_msg(cls,station_code:int):
#         return f"ST,{station_code},{ec.CubesProtocol.E.value};"
    
#     @classmethod
#     def reject_enroll_msg(cls,station_code:int,storage_code:str):
#         return f"ST,{station_code},{ec.CubesProtocol.RE.value},|{storage_code};"

#     @classmethod
#     def remove_bin_msg(cls,station_code:int,storage_code:str):
#         return f"ST,{station_code},{ec.CubesProtocol.REMOVE.value},{storage_code};"
    
#     @classmethod
#     def light_up_msg(cls,station_code:int,storage_code:str):
#         return f"ST,{station_code},{ec.CubesProtocol.L.value},{storage_code},ON;"
    
#     @classmethod
#     def light_down_msg(cls,station_code:int,storage_code:str):
#         return f"ST,{station_code},{ec.CubesProtocol.L.value},{storage_code},OFF;"

# class STMsgFormatter(MsgFormatter):

#     module_code = ec.ModuleCode.STATION.value


# class SDMsgFormatter(MsgFormatter):

#     module_code = ec.ModuleCode.SERVCIE_DOOR.value

#     @classmethod
#     def open_door_msg(cls,sd_id):
#         return f'{cls.module_code},{sd_id},{ec.CubesProtocol.OPEN.value};'

#     @classmethod
#     def close_door_msg(cls,sd_id):
#         return f'{cls.module_code},{sd_id},{ec.CubesProtocol.CLOSE.value};'
    
# class EMOMsgFromatter(MsgFormatter):
    
#     module_code = ec.ModuleCode.EMO.value

#     @classmethod
#     def heartbeat_reply_msg(cls,emo_id:int) -> str:
#         return f'{cls.module_code},{emo_id},{ec.CubesProtocol.H.value},{ec.HeartBeat.HEARTBEAT_REPLY.value};'
    
#     @classmethod
#     def ack_emo_on_msg(cls,emo_id:int)->str:
#         return f'ACK,{cls.module_code},{emo_id},{ec.ToggleType.ON.value};'
    
#     @classmethod
#     def ack_emo_off_msg(cls,emo_id:int)->str:
#         return f'ACK,{cls.module_code},{emo_id},{ec.ToggleType.OFF.value};'
    
#     @classmethod
#     def status_msg(cls,emo_id:int) -> str:
#         return f'{cls.module_code},{emo_id},{ec.CubesProtocol.S.value};'

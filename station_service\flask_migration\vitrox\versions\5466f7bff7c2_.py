"""empty message

Revision ID: 5466f7bff7c2
Revises: 8f884f6444e3
Create Date: 2024-06-26 16:38:34.489586

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5466f7bff7c2'
down_revision = '8f884f6444e3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('stations', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_recovery', sa.Bo<PERSON>an(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('stations', schema=None) as batch_op:
        batch_op.drop_column('is_recovery')

    # ### end Alembic commands ###

from typing import Callable
from termcolor import colored,cprint

from .log import get_logger_console, get_logger

logger_home = dict()

class Log_Type:
    Fatal = 0
    Error = 1
    Warning = 2
    Info = 3
    Debug = 4
    Trace = 5

class LogManager:
    
    def __init__(self,log_name:str,display_console:bool=False, centralize_logging:bool= False):
        self.log_name = log_name
        self.centralize_logging = centralize_logging
        self.display_console = display_console
        if display_console:
            self.file_log = get_logger_console(log_name)
        else:
            self.file_log = get_logger(log_name)
    
    def info(self,msg:str,color:str=None):
        from .notifications import  notify_centralized_logging

        if color:
            self.file_log.info(colored(msg,color))
        else:
            self.file_log.info(msg)
        if self.centralize_logging:
            notify_centralized_logging(msg,Log_Type.Info,self.log_name)

    def warning(self,msg:str):
        from .notifications import  notify_centralized_logging

        if self.display_console:
            msg = colored(msg,'light_yellow')
        self.file_log.warning(msg)
        if self.centralize_logging:
            notify_centralized_logging(msg,Log_Type.Warning,self.log_name)
    

    def error(self,msg:str):
        from .notifications import  notify_centralized_logging

        if self.display_console:
            msg = colored(msg,'light_red')
        self.file_log.error(msg)
        if self.centralize_logging:
            notify_centralized_logging(msg,Log_Type.Error,self.log_name)

    def msg_log(self,station_code:int,msg:str,color:str=None):
        from .notifications import  notify_centralized_logging

        if color:
            self.file_log.info(colored(msg,color))
        else:
            self.file_log.info(msg)
        if self.centralize_logging:
            notify_centralized_logging(msg,Log_Type.Info,self.log_name)

def get_logger_header(type, id):
    try:
        # Create a nested to store loggers by type and id (type -> id -> logger)
        if type not in logger_home.keys():
            logger_home[type] = dict()
        if id not in logger_home[type].keys():
            logger = get_logger_console(id)
            logger_home[type][id] = logger
        else:
            logger = logger_home[type][id]
        return logger
    except Exception as e:
        cprint(f'{get_logger_header.__qualname__}() error. Exception thrown:{e}','light_red')

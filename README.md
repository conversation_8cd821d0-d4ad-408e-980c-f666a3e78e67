# Hardware Control Centre / Hardware Communications Centre
This is a repo that include different services that used to communicate with different hardware.

- ASRS
- Station ( CUBE )
- Charging Station ( CUBE )
- ~~MD~~

## ASRS
Services that mainly used to contorl ASRS & Pallet Conveyor movement. 

Two main flow
- GET ( station call pallet and ASRS will deliver the pallet to the station )
- PUT ( station store the used pallet and ASRS will store the pallet back to the rack)

### To Start 

```
cd asrs_service
```

> Read README udner asrs-service directory and follow the instructions to set up and run station service.


## Station
Service that mainly used together with cube matrix system to control and communicate with station.

Communicate with TC,SM, WMS & PLC to provide station information and send command to station.


### To Start 

```
cd station_service
```

> Read README under station_service directory and follow the instructions to set up and run station service.


## Charging 

To Be Completed

---

## Rules and Regulations
1. When developing different services, make sure your file/folder does not go beyond the highest service level structure. (Ex: ```station_service/```)
2. Make sure to constantly update ```.gitignore``` , ```README.md```, ```_example/``` when needed to keep every repo users in sync.
3. **Main** branch is the branch that will be used to deploy to production, so make sure have enough test before merging into **Main** branch.






from flask_restx import Namespace,Resource

from ....runtime import runtime
from .... import database as db,common as common,api
from ... import api_routing, request_parsers as req_parser

nsconnection = Namespace(api_routing.UrlFolder.connection,description="Connection API")


@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsconnection.route(api_routing.UrlPath.connection_list)
class ConnectionList(Resource):
    @nsconnection.doc(description='To check which station is connecting and get the connection details.')
    def get(self):

        station_list = runtime.get_runtime_st_list()

        st_addr_list = []       
        for station in station_list:
            if station.is_connected:
                st_addr_list.append([station.host,station.port,station.name])  
       
        return common.StandardResponse.response(data = st_addr_list)

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsconnection.route(api_routing.UrlPath.connection_ip_list)
class ConnectionIPList(Resource):
    @nsconnection.doc(description='To check which IP is connected to HWX.')
    def get(self):

        ip_list = []
        for hash,tcp_client in runtime.runtime_tcp_client.items():
            ip_list.append((tcp_client.host,tcp_client.port))            
           
        return common.StandardResponse.response(data = ip_list)


disconnect_st_conn_parser = req_parser.station_code_parser.copy()

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nsconnection.route(api_routing.UrlPath.disconnect_st_connection)
class DisconnectSTConnection(Resource):
    @nsconnection.doc(description='To disconnect station connection.')
    @api.expect(disconnect_st_conn_parser)
    def post(self):
        
        from ....server.station_server import close_socket_connection

        data = disconnect_st_conn_parser.parse_args(strict=True)

        station_code = data.station_code

        station = db.st_db_func.get_station_by_code(station_code)

        if not station.is_connected:
            return common.StandardResponse.response(message=f'ST{station_code} was not connected')
        
        sokcet_conn = runtime.runtime_tcp_client[station.connection].conn
        close_socket_connection(sokcet_conn)

        return common.StandardResponse.response()



import time 
import logging

from typing import Callable
from flask_restx import Api
from flask_cors import CORS
from flask_migrate import Migrate
from flask_sqlalchemy import SQLAlchemy
from flask import Flask, request, Blueprint
from sqlalchemy.exc import SQLAlchemyError,OperationalError

from config import Config
from .enum_collections import HTTPStatus

EXCLUDE_PATHS = ['/cube/doc','/swaggerui','/cube/swagger.json','/asrs', '/md']
EXCLUDE_METHODS = ['OPTIONS']

cube = Blueprint('cube', __name__, url_prefix='/cube') 
api: Api = Api(
    cube,
    doc='/doc',
    title=f'{Config.SERVICE_AREA} HWX API',
    version='2.0',
    description='Hardware X OpenAPI Documentation',
    authorizations = {
        'swagger': {
            'type': 'apiKey',
            'in': 'header',
            'name': 'X-SWAGGER'
        }
    },
    security = 'swagger'
    )



class DBMonitor:

    is_down = True

    @classmethod
    def retry_session(cls, func: Callable):
        def _decorate(*args, **kwargs):
            while True:
                try:
                    res = func(*args, **kwargs)
                    cls.is_down = False
                    return res
                except OperationalError as e:
                    cls.is_down = True
                    print(f'Retrying {func.__qualname__} due to {e.args}.')
                    time.sleep(3)
                    continue
                except Exception or SQLAlchemyError as e:
                    raise Exception(f"{func.__qualname__} raise exception : {e}")
        return _decorate


app = Flask(__name__, instance_relative_config=False)
url = Config.get_sqlalchemy_db_uri()
app.config['SQLALCHEMY_DATABASE_URI'] = url
app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = True
db = SQLAlchemy(app)
migrate = Migrate(app=app,db=db,compare_type=True)


@DBMonitor.retry_session
def create_app():

    global api, app
    logging.getLogger('werkzeug').disabled = True
    CORS(app, resources={r"*": {"origins": "*"}})
    app.config.from_object('config.Config')
    
    with app.app_context():
        
        from .models import StationErrors, StationEvent, StationGatewayReq, StationMovement, StationOrder, StationRecord, Stations, WebsocketRequests,\
                               HttpRequests,\
                               KeyPair,\
                               ServiceDoor
        db.create_all()
    from .webserver.controller.test_log import nstesting
    from .webserver.controller.connection import nsconnection
    from .webserver.controller.station import nsstation
    from .webserver.controller.runtime import nsruntime
    from .webserver.controller.operation import nsoperation
    from .webserver.controller.mock import nsmock
    from .webserver.controller.db import nsdb
    from .webserver.controller.key import nskey
    from .webserver.controller.service_door import nsservice_door
    from .webserver.controller.wms import nswms 
    from .webserver import UrlFolder

    api.add_namespace(nstesting,f'/{UrlFolder.testing}')
    api.add_namespace(nsconnection,f'/{UrlFolder.connection}')
    api.add_namespace(nsstation,f'/{UrlFolder.station}')
    api.add_namespace(nsruntime,f'/{UrlFolder.runtime}')
    api.add_namespace(nsoperation,f'/{UrlFolder.operation}')
    api.add_namespace(nsmock,f'/{UrlFolder.mock}')
    api.add_namespace(nsdb,f'/{UrlFolder.db}')
    api.add_namespace(nskey,f'/{UrlFolder.key}')
    api.add_namespace(nsservice_door,f'/{UrlFolder.service_door}')
    api.add_namespace(nswms,f'/{UrlFolder.wms}')


    app.register_blueprint(cube)

    @app.route('/')
    def index():
        return 'HCC Web Server Hello World'
    

    @app.before_request
    def log_incoming_request():
        """ Logging all incoming raw request except method OPTIONS.
        
        Incoming header might got different standard depend on framework and library of caller e.g
        a. x-correlation-id
        b. X-Correlation-Id 
        c. X-Correlation-ID """

        from .log_http import LogHttp
        from .auth import AuthServices        
        from .common import StandardResponse

        if any(path in request.path for path in EXCLUDE_PATHS):
            return
      
        if request.method in EXCLUDE_METHODS:
            return

        if (auth := AuthServices.extract_authorization()) is None:
            unauthorized_message = 'Invalid Token'
        else:
            unauthorized_message = ''

        LogHttp.log_at_request(request,auth)

        if unauthorized_message:
            return StandardResponse.response(code=HTTPStatus.UNAUTHORIZED.value, message=unauthorized_message)
        
    @app.before_request
    def suppress_request():
        from .common import StandardResponse

        if (
            DBMonitor.is_down and
            not request.headers.get('X-SWAGGER') and
            not any(path in request.path for path in EXCLUDE_PATHS)
        ):
            return StandardResponse.response(False, 'Unable to access database.')
        
    @app.after_request
    def log_response_info(response):
        """
        Log response data after the request is handled.
        """
        from .log_http import LogHttp

        if any(path in request.path for path in EXCLUDE_PATHS):
            return response
        
        if request.method in EXCLUDE_METHODS :
            return response

        LogHttp.log_at_response(request,response)

        return response
    
    return app

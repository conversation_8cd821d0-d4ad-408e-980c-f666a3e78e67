from dotenv import load_dotenv
load_dotenv()
import sys

import seeder
from app import enum_collections as ec

if __name__ == "__main__":

    seeder.clearDB()

    if len(sys.argv) > 1:
        project = sys.argv[1]
        if project == "penta":
            result = seeder.seedPentaCubeStations()
        elif project == "vitrox":
            result = seeder.seedVitroxStations()
        elif project == "vitrox2":
            result = seeder.seedVitrox2Stations()
        elif project == "subang":
            result = seeder.seedSubangStations()
        elif project == "zoned":
            result = seeder.seedZoneDStations()
        elif project == "wirago":
            result = seeder.seedWiragoStations()
        else:
            result = "Invalid arguments"
    else:

        station_type = list(ec.CubeStationType.__members__.keys())

        code = 1

        for type in station_type:
            counter = 0
            num = int(input(f"Please enter the number of {type} station you want to seed: "))
            while counter < num:
                seeder.seed_station(type,code)
                code += 1
                counter += 1

        result = f'Seeded {code-1} station'

    print(result)

import datetime

from typing import List,Tuple

from ... import models as model,common as common,enum_collections as ec
from.station_order_dal import StationOrderSQLAlchemyQueries

db_func_log = common.LogManager('cube_db_func',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_func_log))
class  StationOrderDBFunc:

    st_order_dal = StationOrderSQLAlchemyQueries()

    def add_order(self,station_code:int,storage_code:str, tc_order_id:int,type:str,is_enroll:bool=False)->int:
        all_args = locals()  
        if 'self' in all_args:
            del all_args['self']
        all_args['is_processed'] = is_enroll
        order_id = self.st_order_dal.add_new_order(all_args)
        return order_id

    def update_order(self,filter_dict:dict,update:dict)->int:
        return self.st_order_dal.update_order(filter_dict,update)

    def find_order(self,filter_dict:dict)->model.StationOrder:
        return self.st_order_dal.find_order(filter_dict)
    
    def find_latest_order(self,filter_dict:dict):
        return self.st_order_dal.find_latest_order(filter_dict)
    
    def find_all_active_order(self,station_code:int)->List[model.StationOrder]:
        return self.st_order_dal.find_orders(dict(station_code=station_code,
                                                  status=ec.OrderStatus.PROCESSING.value))
    
    
    def find_all_active_order_join_movement(self,station_code:int)->List[model.StationOrder]:
        return self.st_order_dal.find_active_order_join_movement(dict(station_code=station_code,
                                                                      status=ec.OrderStatus.PROCESSING.value))
    
    def find_active_order(self,station_code:int,storage_code:str)->model.StationOrder:
        st_order = self.st_order_dal.find_order(dict(station_code=station_code,
                                                     storage_code=storage_code,
                                                     status=ec.OrderStatus.PROCESSING.value))
        
        if st_order is None:
            db_func_log.error(f'No order is processing for {storage_code} in ST{station_code} .')

        return st_order if st_order else None

    def find_active_order_by_bin_no(self,storage_code:str)->model.StationOrder|None:
        st_order = self.st_order_dal.find_order(dict(storage_code=storage_code,
                                                     status=ec.OrderStatus.PROCESSING.value))
        
        if st_order is None:
            db_func_log.error(f'No order is processing for {storage_code}.')

        return st_order

    def find_active_order_join_movement(self,station_code:int,storage_code:str)->List[Tuple[model.StationOrder,model.StationMovement]]:
        return self.st_order_dal.find_active_order_join_movement(dict(station_code=station_code,
                                                                      storage_code=storage_code,
                                                                      status=ec.OrderStatus.PROCESSING.value))
    
    def find_next_job_to_run(self,station_code:int,storage_code:str)->model.StationMovement:
        from . import st_db_func
        
        order_j_movement_list = self.find_active_order_join_movement(station_code,storage_code)

        station = st_db_func.get_station_by_code(station_code)

        if station.type == ec.CubeStationType.I.value:
            for order_j_movement in order_j_movement_list[1:]: #first movement will always be update pick for I station
                if order_j_movement[1].status != ec.OrderStatus.COMPLETED.value:
                    return order_j_movement[1]
            if order_j_movement_list[-1][1].type == ec.CubeStationJobType.MOVE_TO_PICK.value:
                return order_j_movement_list[0][1]    
            
        else: 
            for order_j_movement in order_j_movement_list:
                if order_j_movement[1].status != ec.OrderStatus.COMPLETED.value:
                    return order_j_movement[1]
            
        return None
    
    def find_st_orders_by_date(self,station_code:int,from_date:datetime.datetime,to_date:datetime.datetime)->List[model.StationOrder]:
        return self.st_order_dal.find_st_orders_by_date(station_code,from_date,to_date)

    def find_st_orders_by_type_date(self,station_code:int,type:ec.OrderType,from_date:datetime.datetime,to_date:datetime.datetime)->List[model.StationOrder]:
        return self.st_order_dal.find_st_orders_by_type_date(station_code,type,from_date,to_date)

    def get_all_bin_in_station(self,station_code)->List[str]:
        orders = self.st_order_dal.find_orders(dict(station_code=station_code,
                                          status=ec.OrderStatus.PROCESSING.value))
        bin_in_stations = [order.storage_code for order in orders]
        return bin_in_stations

    def get_enroll_count(self,station_code:int)->int:
        orders = self.st_order_dal.find_orders(dict(station_code=station_code,
                                          status=ec.OrderStatus.PROCESSING.value,
                                          type=ec.OrderType.ENROLL.value))
        return len(orders)    
    def get_num_of_bin_in_station(self,station_code)->int:
        orders = self.st_order_dal.find_orders(dict(station_code=station_code,
                                          status=ec.OrderStatus.PROCESSING.value))
        return len(orders)
    
    def get_bin_in_station_pre_post_processed(self,station_code:int)->List[int]:
        pre_processed_orders = self.st_order_dal.find_orders(dict(station_code=station_code,
                                                                  status=ec.OrderStatus.PROCESSING.value,
                                                                  is_processed=False))
        post_processed_orders = self.st_order_dal.find_orders(dict(station_code=station_code,
                                                                  status=ec.OrderStatus.PROCESSING.value,
                                                                  is_processed=True)) 
        pre_processed_bin = [order.storage_code for order in pre_processed_orders]
        post_processed_bin = [order.storage_code for order in post_processed_orders]

        from . import st_db_func

        station = st_db_func.get_station_by_code(station_code)
        if station.bin_at_worker and station.bin_at_worker in pre_processed_bin:
            pre_processed_bin.remove(station.bin_at_worker)

        return [pre_processed_bin,post_processed_bin]
    
    def get_num_of_bin_in_station_pre_processed(self,station_code:int)->int:
        pre_processed_orders = self.st_order_dal.find_orders(dict(station_code=station_code,
                                                                  status=ec.OrderStatus.PROCESSING.value,
                                                                  is_processed=False))
        if pre_processed_orders:
            return len(pre_processed_orders)
        else:
            return 0

    def get_bin_history(self,storage_code:str)->List[dict]:
        today_date = datetime.date.today()
        activity:List[dict]= [] 
        bin_orders = self.st_order_dal.find_bin_history_today(storage_code,today_date)
        for order in bin_orders:
            if order.type == ec.OrderType.ENROLL.value:
                if order.status == ec.OrderStatus.COMPLETED.value:
                    activity.append(dict(history=f'Picked at ST{order.station_code}',happened_at=order.updated_at.isoformat()))
                activity.append(dict(history=f'Enrolled at ST{order.station_code}',happened_at=order.created_at.isoformat()))                    
            else:
                if order.status == ec.OrderStatus.COMPLETED.value:
                    activity.append(dict(history=f'Picked at ST{order.station_code}',happened_at=order.updated_at.isoformat()))
                if order.status == ec.OrderStatus.DELETED.value:
                    activity.append(dict(history=f'Removed from ST{order.station_code}',happened_at=order.updated_at.isoformat()))
                if order.is_processed and order.processed_at:
                    activity.append(dict(history=f'Stored at ST{order.station_code}',happened_at=order.processed_at.isoformat()))
                activity.append(dict(history=f'Dropped at ST{order.station_code}',happened_at=order.created_at.isoformat()))
        return activity
    
    def get_station_bin_history(self,station_code:int)->List[dict]:
        today_date = datetime.date.today()
        bin_history:List[dict]= [] 
        bin_orders = self.st_order_dal.find_station_bin_history_today(station_code,today_date)
        for order in bin_orders:
            bin_history.append(dict(storage_code=order.storage_code,
                                    type=order.type,
                                    status=order.status,
                                    arrived_at=order.created_at.isoformat(),
                                    leaved_at=order.updated_at.isoformat(),
                                    processed_at=order.processed_at.isoformat() if order.processed_at else None))
        return bin_history
    def check_order_exist(self,tc_order_id:int)->bool:
        st_order = self.st_order_dal.find_order(dict(tc_order_id = tc_order_id))
        return True if st_order else False
        
    def check_enroll_order_exist(self,storage_code:str)->bool:
        st_order = self.st_order_dal.find_order(dict(storage_code = storage_code,type=ec.OrderType.ENROLL.value,status=ec.OrderStatus.PROCESSING.value))
        return True if st_order else False
    
    def check_bin_is_enroll(self,station_code:int,storage_code:str)->bool:
        st_order = self.find_active_order(station_code,storage_code)
        if st_order and st_order.type==ec.OrderType.ENROLL.value:
            return True
        else:
            return False
    
    def check_bin_is_processed(self,station_code:int,storage_code:str)->bool:
        st_order = self.find_active_order(station_code,storage_code)
        if st_order:
            return st_order.is_processed
        return False
       
    def check_bin_arrive_pick(self,station_code:int,storage_code:int)->bool:
        
        from . import st_mov_db_func

        order = self.find_active_order(station_code,storage_code)
        mtp_job = st_mov_db_func.find_job(dict(type=ec.CubeStationJobType.MOVE_TO_PICK.value,
                                               order_id=order.id))
        if not mtp_job or mtp_job.status != ec.OrderStatus.COMPLETED.value:
            return False
        
        return True

    def check_station_ready_change_mode(self,station_code:int,mode:ec.CubeStationMode)->bool:
        if mode == ec.CubeStationMode.ENROLL.value:
            """To start enroll need to clear all bin in station"""
            order = self.st_order_dal.find_order(dict(station_code=station_code,
                                                  status=ec.OrderStatus.PROCESSING.value))
        elif mode == ec.CubeStationMode.TRANSFER.value:
            """To start transfer mode need to wait all bin preworker to be processed"""
            order = self.st_order_dal.find_order(dict(station_code=station_code,
                                                    status=ec.OrderStatus.PROCESSING.value,
                                                    is_processed=False))
        else:
            order = None  
        return False if order else True
        



from dataclasses import dataclass

from .. import db

@dataclass
class WebsocketRequests(db.Model):
    id: int
    entity: str
    data: str
    event: str
    namespace: str
    status: str
    retry: int
    created_at: str
    updated_at: str

    __tablename__ = "websocket_requests"

    id = db.Column(
        db.Integer,
        primary_key=True
    )

    entity = db.Column(
        db.String(128),
        index=False,
        nullable=True
    )

    data = db.Column(
        db.JSON,
        index=False,
        nullable=False
    )

    event = db.Column(
        db.String(128),
        index=False,
        nullable=False
    )

    namespace = db.Column(
        db.String(128),
        index=False,
        nullable=True
    )

    status = db.Column(
        db.String(128),
        index=False,
        nullable=False
    )

    retry = db.Column(
        db.Integer,
        index=False,
        nullable=False
    )

    created_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

    updated_at = db.Column(
        db.DateTime,
        index=False,
        unique=False,
        nullable=True
    )

from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
class KeyGen:

    private_key = None
    public_key_pem = None

    @classmethod
    def initialize(cls):
        from .. import database as db

        keypair = db.keypair_db_func.get_keypair()

        if keypair:
            cls.private_key_bytes = keypair.private_key
            cls.private_key_obj = serialization.load_pem_private_key(
                cls.private_key_bytes,
                password=None,
                backend=default_backend()
            )
            cls.public_key_pem = keypair.public_key
        else:
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048,
                backend=default_backend()
            )

            cls.private_key_bytes = private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            )
            cls.private_key_obj = serialization.load_pem_private_key(
                cls.private_key_bytes,
                password=None,
                backend=default_backend()
            )
            cls.public_key_pem = cls.private_key_obj.public_key().public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )

            db.keypair_db_func.create_keypair(cls.private_key_bytes,cls.public_key_pem)


    @classmethod
    def get_public_key_pem(cls):
        return cls.public_key_pem.decode('utf-8')


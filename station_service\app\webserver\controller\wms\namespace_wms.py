import pytz

from math import ceil
from flask_restx.inputs import boolean
from flask_restx import Namespace,Resource, reqparse


from .... import api
from ... import api_routing as api_routing
from .... import common as common,enum_collections as ec,database as db


nswms = Namespace(api_routing.UrlFolder.wms,description="API for WMS")

wms_dash_parser = reqparse.RequestParser()
wms_dash_parser.add_argument("station",required = False,type = int,location = 'args')
wms_dash_parser.add_argument("page",required = False,type = int,location = 'args', default = 1)
wms_dash_parser.add_argument("per_page",required = False,type = int,location = 'args', default =10)


@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nswms.route(api_routing.UrlPath.wms_dash)
class WMSDashboard(Resource):
    @nswms.doc(description='Api for WMS dashbaord to get the station details.')
    @api.expect(wms_dash_parser)
    def get(self):

        args = wms_dash_parser.parse_args(strict = True)
        station_matrix_code = args['station']
        page = args['page']
        per_page = args['per_page']

        if station_matrix_code is not None:
            station_list = db.st_db_func.get_station_list_by_matrix_code(station_matrix_code)
        else:
            station_list = db.st_db_func.get_all_station_list()
        
        if not station_list:
            msg = f"No result found in db with station code = {station_matrix_code}"
            return common.StandardResponse.response(False,msg,metadata={'total_page':0})

        return_data = []
        station_set = set()
        for station in station_list:
            if station.matrix_code in station_set:
                # skip duplicate station matrix code
                continue

            station_data = {}

            station_data['matrix_code']=f"{station.matrix_code}"
            station_data['device_identifier']=f"Workstation-{station.matrix_code}"
            station_data['status'] = common.MatrixConverter.get_station_wms_status(station.code)


            if station.type == ec.CubeStationType.I.value:
                station_data['bin_status'] = []
                station_data['error'] = []
                _station_list = db.st_db_func.get_station_list_by_matrix_code(station.matrix_code)
                pre_bin , post_bin ,at = [], [], []
                for _station in _station_list:
                    if station_existing_error := db.st_error_db_func.get_existing_station_error_msg_list(_station.code):
                        time = station_existing_error[0].created_at
                        err_description = []
                        for error in station_existing_error:
                            err_description.append(error.data['data']['error_message'])
                        station_data['error'].append({
                            'station_code':_station.code,
                            'time':time.astimezone(pytz.utc).isoformat().replace('+00:00', 'Z'),
                            'error_description':err_description
                        })
                    pre_post_bin = db.st_order_db_func.get_bin_in_station_pre_post_processed(_station.code)
                    pre_bin.extend(pre_post_bin[0])
                    post_bin.extend(pre_post_bin[1])
                    if _station.bin_at_worker: at.append(_station.bin_at_worker)
                
                station_data['bin_status'] = {
                    "pre":pre_bin,
                    "at":at,
                    "post":post_bin
                }

            else:
                pre_post_bin = db.st_order_db_func.get_bin_in_station_pre_post_processed(station.code)
                station_data['bin_status'] = {
                    "pre":pre_post_bin[0],
                    "at":[station.bin_at_worker],
                    "post":pre_post_bin[1]
                }
            station_set.add(station.matrix_code)
            return_data.append(station_data)


        total_items = len(return_data)
        total_pages = ceil(total_items / per_page)

        start_index = (page - 1) * per_page
        end_index = start_index + per_page

        paginated_data = return_data[start_index:end_index]
        return common.StandardResponse.response(data=paginated_data,metadata = {
                "total_page": total_pages})


station_enroll_parser = reqparse.RequestParser()
station_enroll_parser.add_argument("station",required = True,type = int,location = 'json')
station_enroll_parser.add_argument("is_enroll",required = True, type = boolean, choices =(True,False))

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nswms.route(api_routing.UrlPath.wms_st_enroll)
class WMSSTEnroll(Resource):

    @nswms.doc(description='To start station enrollment mode for wms.')
    @api.expect(station_enroll_parser)
    def post(self):
        from ....service_handler import enrollment_log

        data = station_enroll_parser.parse_args(strict=True)

        st_matrix_code = data['station']
        station_is_enroll = data.is_enroll
        
        if st_matrix_code is None or st_matrix_code == "" or station_is_enroll is None or station_is_enroll == "":
            return common.StandardResponse.response(status = False, message="Invalid request data")
        
        st_matrix_code = int(st_matrix_code)

        station_list = common.MatrixConverter.get_matrix_station_list(st_matrix_code)

        for station in station_list:
            if station.type in [ec.CubeStationType.QC.value,ec.CubeStationType.BRIDGE.value]:
                return common.StandardResponse.response(status = False, message="Invalid station type.")
            
        st_to_update = []
    
        for station in station_list:
            if station_is_enroll:
                if not (station.mode in [ec.CubeStationMode.NORMAL.value, ec.CubeStationMode.ENROLL.value]):
                    return common.StandardResponse.response(False,message = f"ST{station.code} matrix_code {station.matrix_code} is currently in {station.mode} mode, please close it to proceed ENROLL MODE")
                if station.mode == ec.CubeStationMode.ENROLL.value:
                    continue
                if not db.st_order_db_func.check_station_ready_change_mode(station.code,ec.CubeStationMode.ENROLL.value):
                    return common.StandardResponse.response(status=False,message = f"Need to clear all bin in station to start enrollment mode for station")
                st_to_update.append(station.code)
            else:
                if station.mode != ec.CubeStationMode.ENROLL.value:
                    return common.StandardResponse.response(status=False,message = f"ST{station.code} matrix_code {station.matrix_code} is not in ENROLL mode")
                st_to_update.append(station.code)

        if not st_to_update:
            return common.StandardResponse.response(False,message = "Station is already in desired mode")
        
        for station_code in st_to_update:
            if station_is_enroll:
                common.BackgroundTask.start_station_mode(station_code,ec.CubeStationMode.ENROLL.value)
            else:
                common.BackgroundTask.stop_station_mode(station_code)
        
        enrollment_log.info(f"Update enrollment status for ST{st_to_update} {station_is_enroll}")

        matrix_st_to_update = [f"{common.converter.MatrixConverter.get_matrix_station_code(station_code)}-{common.converter.MatrixConverter.get_matrix_st_cluster_index(station_code)}" for station_code in st_to_update]
        return common.StandardResponse.response(message = f"Update enrollment status for ST{matrix_st_to_update} to {station_is_enroll}", data = matrix_st_to_update)

station_transfer_parser = reqparse.RequestParser()
station_transfer_parser.add_argument("station",required = True,type = int,location = 'json')
station_transfer_parser.add_argument("is_transfer",required = True, type = boolean, choices =(True,False))

@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nswms.route(api_routing.UrlPath.wms_st_trasnfer)
class WMSSTTransfer(Resource):

    @nswms.doc(description='To start station trasnfer mode for wms.')
    @api.expect(station_transfer_parser)
    def post(self):
        from ....service_handler import enrollment_log

        data = station_transfer_parser.parse_args(strict=True)

        st_matrix_code = data['station']
        is_transfer = data.is_transfer
        
        if st_matrix_code is None or st_matrix_code == "" or is_transfer is None or is_transfer == "":
            return common.StandardResponse.response(status = False, message="Invalid request data")
        
        st_matrix_code = int(st_matrix_code)

        station_list = common.MatrixConverter.get_matrix_station_list(st_matrix_code)

        if len(station_list)!=2:
            return common.StandardResponse.response(False,message="TRANSFER mode is only available for I2 station")
        
        st_to_update = []
    
        for station in station_list:
            if is_transfer:
                if not(station.mode in [ec.CubeStationMode.NORMAL.value,ec.CubeStationMode.TRANSFER.value]):
                    return common.StandardResponse.response(False,message = f"ST{station.code} matrix_code {station.matrix_code} is currently in {station.mode} mode, please close it to proceed TRASNFER MODE")
                if station.mode == ec.CubeStationMode.TRANSFER.value:
                    continue
                if not db.st_order_db_func.check_station_ready_change_mode(station.code,ec.CubeStationMode.TRANSFER.value):
                    return common.StandardResponse.response(status=False,message = f"Need to wait all fresh bin processed to proceed this action")
                st_to_update.append(station.code)
            else:
                if station.mode != ec.CubeStationMode.TRANSFER.value:
                    return common.StandardResponse.response(status=False,message = f"ST{station.code} matrix_code {station.matrix_code} is not in TRANSFER mode")
                if not db.st_order_db_func.check_station_ready_change_mode(station.code,ec.CubeStationMode.TRANSFER.value):
                    return common.StandardResponse.response(status=False,message = f"Need to wait all fresh bin processed to proceed this action")
                st_to_update.append(station.code)

        if not st_to_update:
            return common.StandardResponse.response(False,message = "Station is already in desired mode")
        
        for station_code in st_to_update:
            if is_transfer:
                common.BackgroundTask.start_station_mode(station_code,ec.CubeStationMode.TRANSFER.value)
            else:
                common.BackgroundTask.stop_station_mode(station_code)
        
        enrollment_log.info(f"Update transfer status for ST{st_to_update} {is_transfer}")

        matrix_st_to_update = [f"{common.converter.MatrixConverter.get_matrix_station_code(station_code)}-{common.converter.MatrixConverter.get_matrix_st_cluster_index(station_code)}" for station_code in st_to_update]
        return common.StandardResponse.response(message = f"Update transfer status for ST{matrix_st_to_update} to {is_transfer}", data = matrix_st_to_update)
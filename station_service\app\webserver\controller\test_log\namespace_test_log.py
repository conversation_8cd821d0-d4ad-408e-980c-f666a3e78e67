from flask_restx import Namespace,Resource

from .... import common as common,enum_collections
from ... import api_routing as api_routing

nstesting = Namespace(api_routing.UrlFolder.testing,description="API for testing")


@common.decorateAllFunctionInClass(common.log_and_suppress_return_error())
@nstesting.route(api_routing.UrlPath.test_log)
class TestLog(Resource):
    @nstesting.doc(description='An endpoint for testing.')
    def get(self):
        return common.StandardResponse.response()
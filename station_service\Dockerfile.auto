# Use an official Python runtime as a parent image
FROM python:3.11-alpine

RUN apk add gcc build-base linux-headers libffi-dev git openssh-client
RUN addgroup -S uwsgi && adduser -S -G uwsgi uwsgi

WORKDIR /app

COPY requirements.txt .

RUN mkdir -p -m 0700 ~/.ssh && ssh-keyscan github.com >> ~/.ssh/known_hosts
RUN python -m pip install --upgrade pip

RUN \
 apk add --no-cache postgresql-libs && \
 apk add --no-cache --virtual .build-deps gcc musl-dev postgresql-dev && \
 apk --purge del .build-deps
RUN --mount=type=ssh pip3 install -r requirements.txt --no-cache-dir

#install opentelemetry packages
RUN pip install opentelemetry-api                \
	opentelemetry-distro                         \
	opentelemetry-exporter-otlp                  \
	opentelemetry-exporter-otlp-proto-common     \
	opentelemetry-exporter-otlp-proto-grpc       \
	opentelemetry-exporter-otlp-proto-http       \
	opentelemetry-instrumentation                \
	opentelemetry-instrumentation-aio-pika       \
	opentelemetry-instrumentation-aiohttp-client \
	opentelemetry-instrumentation-aiohttp-server \
	opentelemetry-instrumentation-asgi           \
	opentelemetry-instrumentation-asyncio        \
	opentelemetry-instrumentation-dbapi          \
	opentelemetry-instrumentation-flask          \
	opentelemetry-instrumentation-grpc           \
	opentelemetry-instrumentation-jinja2         \
	opentelemetry-instrumentation-logging        \
	opentelemetry-instrumentation-psycopg		 \
	opentelemetry-instrumentation-psycopg2		 \
	opentelemetry-instrumentation-pymongo        \
	opentelemetry-instrumentation-redis          \
	opentelemetry-instrumentation-requests       \
	opentelemetry-instrumentation-sqlalchemy     \
	opentelemetry-instrumentation-sqlite3        \
	opentelemetry-instrumentation-system-metrics \
	opentelemetry-instrumentation-threading      \
	opentelemetry-instrumentation-urllib         \
	opentelemetry-instrumentation-urllib3        \
	opentelemetry-instrumentation-wsgi           \
	opentelemetry-propagator-aws-xray            \
	opentelemetry-proto                          \
	opentelemetry-sdk                            \
	opentelemetry-semantic-conventions           \
	opentelemetry-test-utils                     \
	opentelemetry-util-http


# Copy the current directory contents into the container at /app
COPY . .

# Run app.py when the container launches
CMD ["opentelemetry-instrument","python3","-u","wsgi.py","cube"]

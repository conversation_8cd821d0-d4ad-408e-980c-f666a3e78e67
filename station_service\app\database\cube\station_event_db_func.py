from __future__ import annotations

import json

from typing import TYPE_CHECKING,List

from ... import adapter as adp,models as model,common as common,enum_collections as ec
from .station_event_dal import StationEventSQLAlchemyQueries,app

if TYPE_CHECKING:
    from ...adapter import EventAdapter

db_func_log = common.LogManager('cube_db_func',display_console=True)

@common.decorateAllFunctionInClass(common.log_and_suppress_error(db_func_log))
class StationEventDBFunction:
    
    station_event_dal = StationEventSQLAlchemyQueries()

    def get_all_active_event(self,station_code:int=None)->List[model.StationEvent]:
        """Only available event will be added back to the event queue"""
        return self.station_event_dal.get_all_active_event(station_code)


    def get_st_event(self,filters : dict)->model.StationEvent:
        return self.station_event_dal.get_st_event(filters)

    def create_st_event(self,event_name:ec.CubesIStationEvent,station_code:int,storage_code:str,is_enroll :bool = False,request_id:int = None,is_processed:bool = False,job_to_complete_event:str = "[]"):
        
        with app.app_context():
            all_args = locals()  
            if 'self' in all_args:
                del all_args['self']
            st_event = self.station_event_dal.create_st_event(locals())
            adp.EventAdapter(st_event)
    
    def create_bundled_events(self,is_drop:bool, event_data: dict):
        self.station_event_dal.create_bundled_events(is_drop,event_data)
    
    def update_st_event(self,event:EventAdapter,update:dict):
        for attr,value in update.items():
            setattr(event,attr,value)
        if 'job_to_complete_event' in update:
            job_list = update.get('job_to_complete_event')
            str_job_list = json.dumps(job_list)
            update.update({"job_to_complete_event":str_job_list})
        self.station_event_dal.update_st_event(event.id,update)

    def recover_st_event(self,event:EventAdapter):

        self.update_st_event(event,dict(is_processed = False,
                                        status=ec.OrderStatus.AVAILABLE.value,
                                        err_msg="",
                                        job_to_complete_event=[]))
    
    def patch_bin_no(self,station:adp.StationAdapter,old_bin_no:str,new_bin_no:str):
        """
        When the update bin_no api is called, will cal this function to also update all the related station event to the new bin no
        """
        st_event_queue = station.event_queue.queue()

        for event in st_event_queue:
            event:adp.EventAdapter
            if event.storage_code == old_bin_no:
                self.update_st_event(event,dict(storage_code=new_bin_no))

    def reschedule_st_event_after_chargeout(self,station:adp.StationAdapter,storage_code:str,deleted_job_id:List[int],processing_station_order_id:int):
        """To recover the I station event queue after bin chargeout from station
        If the staiton event is already processed, need to check if the delete id is dependcy of the station event
        Remove the job id from the depending list, reprocessed the station event 
        If not processed, simply removed it if the event is created by the charge out bin 

        Args:
            station (adp.StationAdapter): _description_
            storage_code (str): _description_
            deleted_job_id (List[int]): _description_
        """
        from . import st_mov_db_func, st_order_db_func

        st_event_queue = station.event_queue.queue()
        for event in st_event_queue:
            event:adp.EventAdapter
            if event.storage_code == storage_code:
                event.cancel_event()
                for job_id in event.job_to_complete_event:
                    st_mov_db_func.delete_job(job_id)
            else:
                if event.is_processed:
                    intersect_job_id = list(set(deleted_job_id)&set(event.job_to_complete_event))
                    # if intersect_job_id:
                    for job_id in intersect_job_id:
                        event.job_to_complete_event.remove(job_id)

        order = st_order_db_func.find_order(dict(id=processing_station_order_id))
        self.create_st_event(ec.CubesIStationEvent.RECOVERY.value,station.code,storage_code,is_enroll=(order.type==ec.OrderType.ENROLL.value))

        


                    

    
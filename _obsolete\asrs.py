# from app.blueprints.asrs_handler import map_asrs_id, process_order_jobs
# from app import socketio
# from simple_chalk import greenBright, blueBright, redBright, yellowBright, magentaBright
# from app.database.asrs import create_asrs_order, generate_jobs
# from app.enum_collections import AsrsMethod
# from logfile import log

# asrs_wsroute_log = log.get_logger_console('asrs_wsroute')

# asrs_in_use = []


# def asrs_is_free(id, all = False):
#     try:
#         global asrs_in_use
#         if all:
#             asrs_in_use = []
#         else:
#             asrs_in_use.remove(int(id))
            
#         print(greenBright(f"ASRS is free {id}"))

#     except Exception as e:
#         print(redBright(f'asrs_is_free error. Exception thrown:{e}'))


# def emit_asrs_order_complete(pallet_no, station_id, order_id, order_type):
#     ''' Caller -> create_order() - > process_order_jobs() that run with socketio.start_background_task() '''
#     try:

#         if order_type == AsrsMethod.GET.value:
#             order_reply = {
#                 "pallet_no": pallet_no,
#                 "station_id": station_id
#             }
#             socketio.emit('pallet-arrived', order_reply, broadcast=True)
#             asrs_wsroute_log.info(magentaBright(f'Sent | pallet-arrived - ')+f'{order_reply}')

#         socketio.emit('order-completed', order_id, broadcast=True)
#         asrs_wsroute_log.info(magentaBright(f'Sent | order-completed - {order_id}'))
#     except Exception as e:
#         asrs_wsroute_log.error(redBright(f'emit_asrs_order_complete error. Exception thrown:{e}'))

# def emit_asrs_error(message:str,order_id:int, station_id:int,pallet_no:str,error_level:str , description:str):
#     try:
#         order_reply = {
#             "message": message,
#             "order_id" : order_id ,
#             "station_id" : station_id ,
#             "pallet_id" : pallet_no ,
#             "error_level" : error_level ,
#             "error_desc" : description
#         }
#         socketio.emit('order-error',order_reply, broadcast=True)
#         asrs_wsroute_log.info(magentaBright(f'Sent | order-error -s ')+f'{order_reply}')
#     except Exception as e:
#         asrs_wsroute_log.error(redBright(f'emit_arrs_error error. Exception thrown:{e}'))




# @socketio.on("mediator-init")
# def connect_mediator(data):
#     try:
#         asrs_wsroute_log.info(magentaBright(f'Received  | mediator-init'))
#         print(greenBright('Mediator has connected'))
#         return_data = {
#             'status': "OK"
#         }
#         return return_data
#     except Exception as e:
#         asrs_wsroute_log.error(redBright(f'connect_mediator error. Exception thrown:{e}'))
# 
# @socketio.on('asrs-order')
# def create_order(data):
#     try:
#         global asrs_in_use
#         asrs_wsroute_log.info(magentaBright(f'Received | asrs-order - ')+f'{data}')
#         type = ''

    #     ack_callback = {
    #         'status': "OK"
    #     }

    #     order_id = str(data['order_id'])
    #     asrs_id = map_asrs_id(data['x'])
    #     order = create_asrs_order(data['x'], data['y'], data['z'], asrs_id, data['method'], type, data['order_id'], data['pallet_no'], data['station_id'])
    #     if order is None:
    #         asrs_wsroute_log.warning(yellowBright(f'ASRS Order {order_id} already created. Ignoring this one.'))
    #         return ack_callback
    #     else:
    #         jobs = generate_jobs(order)
    #         socketio.start_background_task(target=process_order_jobs(order.order_id, jobs))

    #         return ack_callback
    # except Exception as e:
    #     asrs_wsroute_log.error(redBright(f'create_order error.Exception thrown:{e}'))
